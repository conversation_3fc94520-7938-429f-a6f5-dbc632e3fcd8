{"timestamp": "2025-09-19T19:01:40.050237", "total_tests": 5, "successful_tests": 0, "failed_tests": 5, "success_rate": "0.00%", "results": [{"path": "tests/test_ai_chat_processor.py", "success": false, "output": "", "error": "ImportError while loading conftest '/home/<USER>/Desktop/tradingview-automatio/tests/conftest.py'.\ntests/conftest.py:10: in <module>\n    import sqlalchemy\nE   ModuleNotFoundError: No module named 'sqlalchemy'\n"}, {"path": "tests/test_backward_compatibility.py", "success": false, "output": "", "error": "ImportError while loading conftest '/home/<USER>/Desktop/tradingview-automatio/tests/conftest.py'.\ntests/conftest.py:10: in <module>\n    import sqlalchemy\nE   ModuleNotFoundError: No module named 'sqlalchemy'\n"}, {"path": "tests", "success": false, "output": "", "error": "ImportError while loading conftest '/home/<USER>/Desktop/tradingview-automatio/tests/conftest.py'.\ntests/conftest.py:10: in <module>\n    import sqlalchemy\nE   ModuleNotFoundError: No module named 'sqlalchemy'\n"}, {"path": "tests/unit", "success": false, "output": "", "error": "ImportError while loading conftest '/home/<USER>/Desktop/tradingview-automatio/tests/conftest.py'.\ntests/conftest.py:10: in <module>\n    import sqlalchemy\nE   ModuleNotFoundError: No module named 'sqlalchemy'\n"}, {"path": "tests/integration", "success": false, "output": "", "error": "ImportError while loading conftest '/home/<USER>/Desktop/tradingview-automatio/tests/conftest.py'.\ntests/conftest.py:10: in <module>\n    import sqlalchemy\nE   ModuleNotFoundError: No module named 'sqlalchemy'\n"}]}