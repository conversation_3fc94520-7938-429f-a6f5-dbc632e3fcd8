# Webhook Ingest Container - Minimal Requirements
# Only what's needed for webhook processing

# Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Database
sqlalchemy==2.0.23
asyncpg==0.29.0
supabase==2.0.0

# Caching
redis==5.0.1

# HTTP Client
httpx==0.24.1
aiohttp>=3.8.0

# Configuration
python-dotenv==1.0.0

# Logging
structlog==23.2.0

# Monitoring
prometheus-client==0.19.0

# Total: 10 packages (vs 19 in original)
