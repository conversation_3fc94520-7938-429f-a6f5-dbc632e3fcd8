#!/usr/bin/env python3
"""
Real-Time Streaming Demo - Shows live data streaming in action
"""

import asyncio
import sys
import os
import time

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def streaming_demo():
    """Demonstrate real-time streaming with mock data"""
    print("🚀 Real-Time Data Streaming Demo")
    print("=" * 50)
    
    try:
        from bot.real_time_data_stream import real_time_streamer, DataSource, MarketData
        
        # Initialize the streamer
        print("📡 Initializing real-time data streamer...")
        await real_time_streamer.initialize()
        print("✅ Streamer initialized successfully")
        
        # Track received data
        received_data = []
        
        async def data_callback(data: MarketData):
            """Callback function to handle incoming market data"""
            received_data.append(data)
            timestamp = data.timestamp.strftime("%H:%M:%S")
            change_symbol = "📈" if data.change >= 0 else "📉"
            
            print(f"{timestamp} {change_symbol} {data.symbol}: ${data.price:.2f} "
                  f"({data.change:+.2f}, {data.change_percent:+.2f}%) "
                  f"[{data.source.value}]")
        
        # Subscribe to multiple symbols and sources
        print("\n📊 Subscribing to market data...")
        
        symbols = ["AAPL", "GOOGL", "MSFT"]
        sources = [DataSource.YAHOO_FINANCE, DataSource.ALPHA_VANTAGE]
        
        for symbol in symbols:
            for source in sources:
                success = await real_time_streamer.subscribe_to_symbol(
                    symbol, data_callback, [source]
                )
                if success:
                    print(f"✅ Subscribed to {symbol} via {source.value}")
                else:
                    print(f"❌ Failed to subscribe to {symbol} via {source.value}")
        
        # Show streaming for 15 seconds
        print(f"\n🔄 Streaming live data for 15 seconds...")
        print("Press Ctrl+C to stop early")
        print("-" * 50)
        
        start_time = time.time()
        try:
            while time.time() - start_time < 15:
                await asyncio.sleep(1)
                
                # Show periodic stats
                if len(received_data) > 0 and len(received_data) % 5 == 0:
                    print(f"\n📈 Stats: {len(received_data)} data points received")
                    
                    # Show latest prices
                    latest_prices = {}
                    for data in received_data[-10:]:  # Last 10 data points
                        latest_prices[data.symbol] = data.price
                    
                    price_summary = " | ".join([f"{symbol}: ${price:.2f}" for symbol, price in latest_prices.items()])
                    print(f"💰 Latest: {price_summary}")
                    print("-" * 50)
                    
        except KeyboardInterrupt:
            print("\n⏹️ Stopping early...")
        
        # Final statistics
        print(f"\n📊 Streaming Complete!")
        print("=" * 50)
        print(f"Total data points received: {len(received_data)}")
        
        if received_data:
            # Group by symbol
            symbol_data = {}
            for data in received_data:
                if data.symbol not in symbol_data:
                    symbol_data[data.symbol] = []
                symbol_data[data.symbol].append(data)
            
            print(f"\nSymbol breakdown:")
            for symbol, data_list in symbol_data.items():
                prices = [d.price for d in data_list]
                min_price = min(prices)
                max_price = max(prices)
                latest_price = prices[-1]
                price_change = latest_price - prices[0] if len(prices) > 1 else 0
                
                print(f"  {symbol}: ${latest_price:.2f} "
                      f"(range: ${min_price:.2f}-${max_price:.2f}, "
                      f"change: {price_change:+.2f})")
            
            # Group by source
            source_data = {}
            for data in received_data:
                source = data.source.value
                if source not in source_data:
                    source_data[source] = 0
                source_data[source] += 1
            
            print(f"\nSource breakdown:")
            for source, count in source_data.items():
                print(f"  {source}: {count} data points")
        
        # Test health check
        health = await real_time_streamer.get_stream_health()
        print(f"\n🏥 Stream Health:")
        print(f"  Active streams: {health['active_streams']}")
        print(f"  Data sources: {len(health['sources'])}")
        print(f"  Last data count: {health['last_data_count']}")
        
        # Cleanup
        print(f"\n🧹 Cleaning up...")
        await real_time_streamer.cleanup()
        print("✅ Cleanup completed")
        
        return True
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run the streaming demo"""
    success = await streaming_demo()
    
    if success:
        print("\n🎉 Real-Time Streaming Demo Completed Successfully!")
        print("The streamer is working and generating mock market data.")
        print("In production, this would connect to real market data APIs.")
    else:
        print("\n❌ Demo failed - check the error messages above")

if __name__ == "__main__":
    asyncio.run(main())
