#!/usr/bin/env python3
"""
Test if /ask command is properly registered and accessible
"""

import asyncio
import os
import sys
sys.path.insert(0, 'src')

async def test_ask_command():
    """Test if ask command is properly registered"""
    print("🔍 Testing /ask command registration...")
    
    try:
        from bot.core.bot import create_bot
        
        # Create bot instance
        bot = create_bot()
        print("✅ Bot created successfully")
        
        # Load extensions
        await bot._load_extensions()
        print("✅ Extensions loaded")
        
        # Check if ask command exists in the command tree
        ask_command = None
        for cmd in bot.bot.tree.walk_commands():
            if cmd.name == 'ask':
                ask_command = cmd
                break
        
        if ask_command:
            print("✅ /ask command found in command tree!")
            print(f"   - Name: {ask_command.name}")
            print(f"   - Description: {ask_command.description}")
            print(f"   - Type: {type(ask_command).__name__}")
            
            # Check if it's a slash command
            if hasattr(ask_command, 'callback'):
                print("✅ /ask command has callback function")
            else:
                print("❌ /ask command missing callback function")
                
            # Check parameters
            if hasattr(ask_command, 'parameters'):
                print(f"   - Parameters: {ask_command.parameters}")
            elif hasattr(ask_command, '_params'):
                print(f"   - Parameters: {ask_command._params}")
            else:
                print("   - Parameters: Not accessible")
                
            return True
        else:
            print("❌ /ask command NOT found in command tree")
            
            # List all available commands
            all_commands = [cmd.name for cmd in bot.bot.tree.walk_commands()]
            print(f"Available commands: {all_commands}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_ask_command())
    if success:
        print("\n🎉 /ask command is properly registered!")
        print("The command should be available in Discord.")
    else:
        print("\n❌ /ask command registration test failed.")
