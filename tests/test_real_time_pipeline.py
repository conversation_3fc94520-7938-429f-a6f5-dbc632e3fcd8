"""End-to-End Tests for Real-Time Data Pipeline

Tests the integration of WebSocket handler, buffer manager, and alert system.
Uses mocks for WebSocket and Discord to simulate live streaming, validation,
buffering, indicator computation, and alert triggering without external APIs.

Run with: pytest tests/test_real_time_pipeline.py -v
"""

import asyncio
import pytest
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta
import time

from src.bot.pipeline.commands.analyze.stages.fetch_data import run as fetch_data_run
from src.shared.data_pipeline.websocket_handler import PolygonWebSocketClient
from src.shared.data_pipeline.buffer_manager import DataBuffer
from src.bot.alerts.real_time_alerts import AlertManager
from src.bot.pipeline import PipelineContext

@pytest.fixture
def mock_context():
    """Mock PipelineContext for testing."""
    context = Mock(spec=PipelineContext)
    context.ticker = "AAPL"
    context.processing_results = {}
    context.error_log = []
    return context

@pytest.fixture
def mock_bot():
    """Mock Discord bot client."""
    bot = Mock()
    bot.send_message = AsyncMock()
    return bot

@pytest.fixture
def mock_config():
    """Mock config with real-time enabled."""
    return {
        'real_time': {'enabled': True, 'polygon_api_key': 'demo'},
        'alerts': {'price_change_pct': 5.0, 'volatility_high': 2.0}
    }

@patch('src.shared.config_loader.load_config')
@patch('src.bot.pipeline.commands.analyze.stages.fetch_data.load_config', return_value={'real_time': {'enabled': False}})  # Disable real for batch test
def test_batch_fetch_without_real_time(mock_config_batch, mock_context):
    """Test batch fetch works without real-time."""
    # Run fetch (assumes batch works; mock if needed)
    result = asyncio.run(fetch_data_run(mock_context))
    assert result == mock_context
    assert 'market_data' in mock_context.processing_results

@patch('src.shared.config_loader.load_config', return_value=mock_config())
@patch('src.bot.alerts.real_time_alerts.load_config', return_value=mock_config())
@pytest.mark.asyncio
async def test_end_to_end_real_time_pipeline(mock_config, mock_alert_config, mock_context, mock_bot):
    """End-to-End test: Simulate live data, buffer, alerts, measure latency."""
    buffer = DataBuffer(max_size=10)
    manager = AlertManager(bot_client=mock_bot, buffer=buffer, channel_id="test_channel")
    
    # Mock WebSocket client
    mock_client = Mock(spec=PolygonWebSocketClient)
    mock_client.connect = AsyncMock()
    mock_client.subscribe = AsyncMock()
    mock_client.on_update = None  # Will be set to buffer.add_update
    
    with patch('src.shared.data_pipeline.websocket_handler.PolygonWebSocketClient', return_value=mock_client):
        # Set callback to buffer
        def buffer_callback(update):
            start_time = time.time()
            success = buffer.add_update(update)
            latency = time.time() - start_time
            assert success, "Buffer add failed"
            assert latency < 0.5, f"High latency: {latency}s"
        
        mock_client.on_update = buffer_callback
        
        # Run fetch_data (starts live task)
        start_pipeline = time.time()
        result = await fetch_data_run(mock_context)
        pipeline_latency = time.time() - start_pipeline
        assert pipeline_latency < 2.0, f"Pipeline setup delay: {pipeline_latency}s"
        assert result == mock_context
        assert 'market_data' in mock_context.processing_results
        assert 'live_task' in mock_context.processing_results
        
        # Simulate live updates (mock WebSocket messages)
        updates = []
        base_price = 150.0
        for i in range(10):
            price = base_price + (i * 1.0)  # Gradual increase to trigger change
            update = {
                "symbol": "AAPL",
                "price": price,
                "size": 100 + i,
                "timestamp": (datetime.utcnow() - timedelta(seconds=10-i)).isoformat() + "Z",
                "type": "T",
                "provider": "polygon_live"
            }
            updates.append(update)
            buffer.add_update(update)  # Simulate add (sync for test)
        
        # Verify buffering
        latest = buffer.get_latest("AAPL")
        assert latest is not None
        assert latest['live_price'] == updates[-1]['price']
        assert len(buffer.get_buffer("AAPL")) == 10
        
        # Verify indicators
        indicators = buffer.compute_indicators("AAPL", window=5)
        assert indicators is not None
        assert 'sma' in indicators
        assert indicators['change_pct'] > 0  # From simulation
        
        # Start monitoring and simulate alert trigger
        await manager.start_monitoring(["AAPL"])
        await asyncio.sleep(1)  # Allow poll
        
        # Force alert by high change (simulate surge)
        surge_update = {
            "symbol": "AAPL",
            "price": base_price * 1.1,  # 10% surge
            "size": 200,
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "type": "T",
            "provider": "polygon_live"
        }
        buffer.add_update(surge_update)
        
        # Wait for alert check
        await asyncio.sleep(12)  # One poll cycle
        mock_bot.send_message.assert_called()  # Verify alert sent
        alert_msg = mock_bot.send_message.call_args[0][1]
        assert "PRICE_SURGE" in alert_msg.upper()
        assert "AAPL" in alert_msg
        
        # Verify no delays in analysis (indicators post-update)
        post_indicators = buffer.compute_indicators("AAPL")
        assert post_indicators['change_pct'] > 5.0  # Triggered threshold
        
        # Cleanup
        await manager.stop_monitoring(["AAPL"])
        mock_context.processing_results['live_task'].cancel()
    
    logger.info("End-to-end real-time pipeline test passed: No delays detected.")

if __name__ == "__main__":
    pytest.main([__file__, "-v"])