#!/usr/bin/env python3
"""
Direct test of fallback response generation
"""

import sys
import os
from collections import Counter

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_direct_fallback():
    """Test fallback responses directly"""
    
    print("🧪 TESTING DIRECT FALLBACK RESPONSES")
    print("=" * 50)
    
    # Import the AI processor
    from src.shared.ai_services.ai_processor_robust import RobustFinancialAnalyzer
    
    # Create analyzer instance
    analyzer = RobustFinancialAnalyzer()
    
    # Test queries that should trigger fallback responses
    test_queries = [
        "Hello",
        "What's the weather?", 
        "Can you help with trading?",
        "What time is it?",
        "Play some music"
    ]
    
    all_symbols_mentioned = []
    
    for query in test_queries:
        print(f"\n📝 Query: '{query}'")
        print("-" * 30)
        
        # Test multiple times to see variation
        for i in range(3):
            response = analyzer._generate_fallback_response(query)
            print(f"Response {i+1}: {response[:100]}...")
            
            # Extract symbols mentioned in response
            import re
            symbols = re.findall(r'\$([A-Z]+)', response)
            all_symbols_mentioned.extend(symbols)
            print(f"  Symbols found: {symbols}")
    
    print(f"\n🎯 OVERALL SYMBOL DISTRIBUTION")
    print("-" * 40)
    
    if all_symbols_mentioned:
        total_symbol_counts = Counter(all_symbols_mentioned)
        total_mentions = sum(total_symbol_counts.values())
        
        for symbol, count in total_symbol_counts.most_common():
            percentage = (count / total_mentions) * 100
            print(f"  {symbol}: {count} mentions ({percentage:.1f}%)")
        
        # Check NVDA bias
        nvda_percentage = (total_symbol_counts.get('NVDA', 0) / total_mentions) * 100
        
        if nvda_percentage > 30:
            print(f"\n❌ NVDA still has high bias: {nvda_percentage:.1f}%")
        elif nvda_percentage > 15:
            print(f"\n⚠️  NVDA has moderate presence: {nvda_percentage:.1f}%")
        else:
            print(f"\n✅ NVDA bias reduced: {nvda_percentage:.1f}%")
    else:
        print("No symbols found in responses")

if __name__ == "__main__":
    test_direct_fallback()
