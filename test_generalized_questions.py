#!/usr/bin/env python3
"""
Test how the bot handles generalized questions
"""

import asyncio
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_generalized_questions():
    """Test generalized questions that require analysis of multiple candidates"""
    
    print("🔍 TESTING GENERALIZED QUESTIONS")
    print("=" * 50)
    
    # Import the pipeline
    from src.bot.pipeline.commands.ask.pipeline import AskPipeline
    
    # Create pipeline instance
    pipeline = AskPipeline()
    
    test_queries = [
        "What's the best tech stock to buy right now?",
        "I want to find a good dividend stock for my portfolio",
        "Which AI stock has the most potential?",
        "What are some good growth stocks under $100?",
        "I'm looking for a safe blue chip stock to invest in"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n📝 Query {i}: '{query}'")
        print("-" * 60)
        
        try:
            # Process the query through the pipeline
            result = await pipeline.process_query(
                query=query,
                user_id="test_user",
                username="test_user",
                correlation_id=f"test_{i}"
            )
            
            # Extract key information
            response = result.get('response', 'No response')
            symbols = result.get('symbols', [])
            intent = result.get('intent', 'unknown')
            status = result.get('status', 'unknown')
            
            print(f"🎯 Intent: {intent}")
            print(f"📊 Symbols extracted: {symbols}")
            print(f"✅ Status: {status}")
            print(f"💬 Response preview: {response[:300]}...")
            
            # Analyze the response quality
            if response.startswith("❌"):
                print("⚠️  ISSUE: Bot returned error response instead of helpful guidance")
            elif "No symbols found" in response:
                print("⚠️  ISSUE: Bot focused on symbol extraction instead of answering question")
            elif len(response) < 50:
                print("⚠️  ISSUE: Response too short, likely not helpful")
            else:
                print("✅ GOOD: Bot provided substantial response")
                
            # Check if response mentions specific tickers inappropriately
            response_upper = response.upper()
            mentioned_tickers = []
            common_tickers = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'META', 'NFLX']
            for ticker in common_tickers:
                if ticker in response_upper:
                    mentioned_tickers.append(ticker)
            
            if mentioned_tickers:
                print(f"📈 Tickers mentioned: {mentioned_tickers}")
                print("⚠️  ANALYSIS: Check if these are justified or random picks")
            else:
                print("✅ GOOD: No specific tickers mentioned - providing general guidance")
                
        except Exception as e:
            print(f"❌ Error processing query: {e}")
            
        print()

if __name__ == "__main__":
    asyncio.run(test_generalized_questions())
