#!/usr/bin/env python3
"""
Test JSON parsing fix for AI responses
"""

import asyncio
import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def test_json_parsing():
    """Test that AI responses are properly formatted as JSON."""
    print("🧪 Testing JSON Parsing Fix")
    print("=" * 50)
    
    try:
        from src.shared.ai_chat.ai_client import AIClientWrapper
        
        # Create mock context
        class MockContext:
            def __init__(self):
                self.pipeline_id = "json_test"
        
        context = MockContext()
        ai_client = AIClientWrapper(context)
        ai_client.model = "deepcogito/cogito-v2-preview-deepseek-671b"
        
        print("✅ AI client initialized")
        print(f"🤖 Model: {ai_client.model}")
        
        # Test queries
        test_queries = [
            "waddup",
            "What's the price of AAPL?",
            "Give me technical analysis for MSFT",
            "hello",
            "Show me RSI for NVDA"
        ]
        
        print(f"\n🧪 Testing {len(test_queries)} queries...")
        print("=" * 50)
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n🔍 Test {i}: {query}")
            
            try:
                # Generate response
                response = await ai_client.generate_response(query)
                
                if response:
                    print(f"✅ Response received: {len(response)} characters")
                    print(f"📝 Response preview: {response[:100]}...")
                    
                    # Check if it looks like JSON
                    if response.strip().startswith('{') and response.strip().endswith('}'):
                        print("✅ Response appears to be JSON format")
                    else:
                        print("❌ Response does not appear to be JSON format")
                        print(f"Full response: {response}")
                else:
                    print("❌ No response generated")
                    
            except Exception as e:
                print(f"❌ Error: {e}")
        
        print("\n🎉 JSON parsing test completed!")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    asyncio.run(test_json_parsing())
