# Base Requirements - Core Dependencies
# Essential packages required by all environments

# Core Python utilities
python-dotenv>=1.0.0
pyyaml>=6.0
configparser>=5.3.0
aiofiles>=23.0.0
async-timeout>=4.0.0
tenacity>=8.2.0
tqdm>=4.65.0

# Database and Storage (Core)
sqlalchemy>=2.0.0
asyncpg>=0.27.0
aiosqlite>=0.19.0
redis>=4.5.0

# Web Framework (Core)
fastapi>=0.100.0
uvicorn>=0.23.0
httpx>=0.24.0
aiohttp>=3.8.0

# Logging and Monitoring (Core)
structlog>=23.0.0
psutil>=5.9.0

# Security (Core)
cryptography>=41.0.0
pyjwt>=2.8.0
passlib>=1.7.4

# Performance (Core)
cachetools>=5.3.0
aiorwlock>=1.3.0
ratelimit>=2.2.0

# Scheduling (Core)
apscheduler>=3.10.0
