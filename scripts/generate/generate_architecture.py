#!/usr/bin/env python3
"""
Codebase Explorer - Architecture Analysis

This script analyzes the codebase structure and generates a structured
representation that can be used by the interactive dashboard.

It uses AST parsing to extract metadata about commands, pipelines,
and other components.
"""

import ast
import os
import json
import yaml
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CodebaseAnalyzer:
    """Analyzes the codebase structure and extracts metadata."""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.extensions_dir = project_root / "src" / "bot" / "extensions"
        self.pipeline_dir = project_root / "src" / "bot" / "pipeline"
        
    def analyze_project(self) -> Dict[str, Any]:
        """Analyze the entire project and return structured data."""
        logger.info("Starting codebase analysis...")
        
        # Create the root structure
        architecture = {
            "name": self.project_root.name,
            "type": "project",
            "path": ".",
            "children": []
        }
        
        # Analyze src directory
        src_path = self.project_root / "src"
        if src_path.exists():
            architecture["children"].append(self._analyze_directory(src_path, "src"))
        
        logger.info("Codebase analysis completed")
        return architecture
    
    def _analyze_directory(self, dir_path: Path, display_path: str) -> Dict[str, Any]:
        """Recursively analyze a directory and its contents."""
        node = {
            "name": dir_path.name,
            "type": "directory",
            "path": display_path,
            "children": []
        }
        
        try:
            for item in dir_path.iterdir():
                # Skip hidden directories and __pycache__
                if item.name.startswith('.') or item.name == '__pycache__':
                    continue
                    
                relative_path = f"{display_path}/{item.name}" if display_path != "." else item.name
                
                if item.is_dir():
                    # Recursively analyze subdirectories
                    child_node = self._analyze_directory(item, relative_path)
                    if child_node:  # Only add if directory has content
                        node["children"].append(child_node)
                elif item.is_file() and item.suffix == '.py' and item.name != '__init__.py':
                    # Analyze Python files
                    file_node = self._analyze_python_file(item, relative_path)
                    if file_node:
                        node["children"].append(file_node)
                elif item.is_file() and item.name == '__init__.py':
                    # Include __init__.py files but without detailed analysis
                    node["children"].append({
                        "name": item.name,
                        "type": "file",
                        "path": relative_path
                    })
                elif item.is_file():
                    # Include other files
                    node["children"].append({
                        "name": item.name,
                        "type": "file",
                        "path": relative_path
                    })
        except PermissionError:
            logger.warning(f"Permission denied: {dir_path}")
        
        return node
    
    def _analyze_python_file(self, file_path: Path, display_path: str) -> Optional[Dict[str, Any]]:
        """Analyze a Python file and extract metadata."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            
            # Extract metadata
            metadata = {
                "name": file_path.name,
                "type": "file",
                "path": display_path,
                "commands": self._extract_commands(tree),
                "pipelines": self._extract_pipelines(tree),
                "imports": self._extract_imports(tree),
                "classes": self._extract_classes(tree),
                "functions": self._extract_functions(tree)
            }
            
            return metadata
            
        except Exception as e:
            logger.warning(f"Error analyzing {file_path}: {e}")
            return {
                "name": file_path.name,
                "type": "file",
                "path": display_path,
                "error": str(e)
            }
    
    def _extract_commands(self, tree: ast.AST) -> List[Dict[str, Any]]:
        """Extract Discord command metadata from a Python AST."""
        commands = []
        
        # Look for classes that inherit from commands.Cog
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                # Check if class inherits from commands.Cog
                is_cog = False
                for base in node.bases:
                    if isinstance(base, ast.Attribute) and base.attr == 'Cog':
                        is_cog = True
                        break
                    elif isinstance(base, ast.Name) and base.id == 'Cog':
                        is_cog = True
                        break
                
                if not is_cog:
                    continue
                
                class_name = node.name
                class_docstring = ast.get_docstring(node) or ""
                
                # Look for command methods in this class
                for item in node.body:
                    if isinstance(item, ast.FunctionDef):
                        # Check for @app_commands.command decorator
                        command_name = None
                        command_description = ""
                        command_params = []
                        
                        # Look for decorators
                        for decorator in item.decorator_list:
                            # Handle @app_commands.command() with arguments
                            if isinstance(decorator, ast.Call) and isinstance(decorator.func, ast.Attribute):
                                if decorator.func.attr == 'command':
                                    # Extract command name from decorator arguments
                                    for keyword in decorator.keywords:
                                        if keyword.arg == 'name':
                                            if isinstance(keyword.value, ast.Constant):
                                                command_name = keyword.value.value
                                        elif keyword.arg == 'description':
                                            if isinstance(keyword.value, ast.Constant):
                                                command_description = keyword.value.value
                                    # If no name was specified, use the function name
                                    if command_name is None:
                                        command_name = item.name
                            # Handle @app_commands.command (without parentheses)
                            elif isinstance(decorator, ast.Attribute) and decorator.attr == 'command':
                                command_name = item.name  # Use function name as command name
                        
                        # If we found a command, extract parameters
                        if command_name:
                            # Extract function parameters
                            for arg in item.args.args:
                                if arg.arg != 'self' and arg.arg != 'interaction':
                                    param_name = arg.arg
                                    param_annotation = ""
                                    if arg.annotation:
                                        param_annotation = ast.unparse(arg.annotation) if hasattr(ast, 'unparse') else str(arg.annotation)
                                    command_params.append({
                                        'name': param_name,
                                        'type': param_annotation
                                    })
                            
                            # Get function docstring
                            func_docstring = ast.get_docstring(item) or ""
                            
                            commands.append({
                                'name': command_name,
                                'class': class_name,
                                'description': command_description,
                                'parameters': command_params,
                                'docstring': func_docstring
                            })
        
        return commands
    
    def _extract_pipelines(self, tree: ast.AST) -> List[Dict[str, Any]]:
        """Extract pipeline metadata from a Python AST."""
        pipelines = []
        
        # Look for classes that inherit from BasePipelineStage
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                # Check if class inherits from BasePipelineStage
                is_stage = False
                for base in node.bases:
                    if isinstance(base, ast.Name) and base.id == 'BasePipelineStage':
                        is_stage = True
                        break
                    elif isinstance(base, ast.Attribute) and base.attr == 'BasePipelineStage':
                        is_stage = True
                        break
                
                if not is_stage:
                    continue
                
                class_name = node.name
                class_docstring = ast.get_docstring(node) or ""
                
                # Look for process method to understand what the stage does
                process_method = None
                for item in node.body:
                    if isinstance(item, ast.FunctionDef) and item.name == 'process':
                        process_method = item
                        break
                
                process_description = ""
                if process_method:
                    process_description = ast.get_docstring(process_method) or ""
                
                pipelines.append({
                    'name': class_name,
                    'type': 'pipeline_stage',
                    'description': class_docstring,
                    'process_description': process_description
                })
        
        return pipelines
    
    def _extract_imports(self, tree: ast.AST) -> List[Dict[str, Any]]:
        """Extract import statements from a Python AST."""
        imports = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append({
                        'type': 'import',
                        'module': alias.name,
                        'alias': alias.asname
                    })
            elif isinstance(node, ast.ImportFrom):
                for alias in node.names:
                    imports.append({
                        'type': 'from_import',
                        'module': node.module,
                        'name': alias.name,
                        'alias': alias.asname
                    })
        
        return imports
    
    def _extract_classes(self, tree: ast.AST) -> List[Dict[str, Any]]:
        """Extract class definitions from a Python AST."""
        classes = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                # Skip classes that are already processed as commands or pipeline stages
                is_cog = any(isinstance(base, (ast.Name, ast.Attribute)) and 
                            (getattr(base, 'id', '') == 'Cog' or getattr(base, 'attr', '') == 'Cog'))
                is_stage = any(isinstance(base, (ast.Name, ast.Attribute)) and 
                              (getattr(base, 'id', '') == 'BasePipelineStage' or getattr(base, 'attr', '') == 'BasePipelineStage'))
                
                if is_cog or is_stage:
                    continue
                
                classes.append({
                    'name': node.name,
                    'docstring': ast.get_docstring(node) or "",
                    'bases': [ast.unparse(base) if hasattr(ast, 'unparse') else str(base) for base in node.bases]
                })
        
        return classes
    
    def _extract_functions(self, tree: ast.AST) -> List[Dict[str, Any]]:
        """Extract function definitions from a Python AST."""
        functions = []
        
        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                # Skip methods inside classes (they're already covered)
                if any(isinstance(parent, ast.ClassDef) for parent in ast.walk(tree) if node in parent.body):
                    continue
                
                functions.append({
                    'name': node.name,
                    'async': isinstance(node, ast.AsyncFunctionDef),
                    'docstring': ast.get_docstring(node) or "",
                    'parameters': [arg.arg for arg in node.args.args]
                })
        
        return functions

def main():
    """Main function to run the codebase analysis."""
    # Get the project root directory
    project_root = Path(__file__).parent.absolute()
    
    # Create analyzer
    analyzer = CodebaseAnalyzer(project_root)
    
    # Analyze the project
    architecture = analyzer.analyze_project()
    
    # Save to file
    output_file = project_root / "architecture.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(architecture, f, indent=2, ensure_ascii=False)
    
    logger.info(f"Architecture saved to {output_file}")
    
    # Also save a pretty-printed version for easier reading
    pretty_file = project_root / "architecture_pretty.json"
    with open(pretty_file, 'w', encoding='utf-8') as f:
        json.dump(architecture, f, indent=4, ensure_ascii=False)
    
    logger.info(f"Pretty-printed architecture saved to {pretty_file}")
    
    # Print summary
    def count_items(node):
        count = 0
        if node['type'] == 'file':
            count = 1
        elif 'children' in node:
            for child in node['children']:
                count += count_items(child)
        return count
    
    total_files = count_items(architecture)
    logger.info(f"Analyzed {total_files} files in the codebase")

if __name__ == "__main__":
    main()