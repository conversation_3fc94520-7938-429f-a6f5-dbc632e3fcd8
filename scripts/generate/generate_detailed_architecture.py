#!/usr/bin/env python3
"""
Generate a detailed architecture report for the Codebase Explorer dashboard.

This script creates a more detailed view of the architecture with additional
information about key components, their relationships, and system structure.
"""

import json
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Project root directory
PROJECT_ROOT = Path(__file__).parent

def load_architecture():
    """Load the existing architecture.json file."""
    architecture_path = PROJECT_ROOT / 'architecture.json'
    if not architecture_path.exists():
        raise FileNotFoundError("architecture.json not found. Run generate_architecture.py first.")
    
    with open(architecture_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def find_node_by_path(node, path_parts):
    """
    Find a node in the architecture tree by its path.
    
    Args:
        node: The current node to search
        path_parts: List of path components to search for
        
    Returns:
        The found node or None if not found
    """
    if not path_parts:
        return node
    
    current_part = path_parts[0]
    remaining_parts = path_parts[1:]
    
    if 'children' in node:
        for child in node['children']:
            if child['name'] == current_part:
                return find_node_by_path(child, remaining_parts)
    
    return None

def enhance_architecture_with_details(architecture):
    """
    Enhance the architecture with additional details about key components.
    
    Args:
        architecture: The base architecture dictionary
        
    Returns:
        Enhanced architecture dictionary
    """
    # Add system overview
    architecture['overview'] = {
        'title': 'TradingView Automation Bot',
        'description': 'A professional-grade trading bot with AI-powered analysis, real-time data processing, and comprehensive risk management.',
        'version': '2.0.0',
        'components': [
            {
                'name': 'Discord Bot',
                'description': 'Main entry point for user interactions through Discord commands.',
                'type': 'interface'
            },
            {
                'name': 'Pipeline Engine',
                'description': 'Core processing engine that orchestrates data flow through stages.',
                'type': 'core'
            },
            {
                'name': 'AI Services',
                'description': 'AI-powered analysis and natural language processing.',
                'type': 'service'
            },
            {
                'name': 'Data Providers',
                'description': 'Multiple market data sources with intelligent aggregation.',
                'type': 'service'
            },
            {
                'name': 'Database',
                'description': 'Persistent storage for user data, watchlists, and historical records.',
                'type': 'storage'
            },
            {
                'name': 'Cache',
                'description': 'High-performance caching for frequently accessed data.',
                'type': 'storage'
            },
            {
                'name': 'Monitoring',
                'description': 'Comprehensive system monitoring and performance tracking.',
                'type': 'monitoring'
            }
        ]
    }
    
    # Enhance key directories with descriptions
    key_directories = {
        'src/bot/extensions': {
            'description': 'Discord command extensions that handle user interactions.',
            'type': 'interface',
            'key_files': [
                {
                    'name': 'ask.py',
                    'description': 'AI-powered question answering system with natural language processing.'
                },
                {
                    'name': 'analyze.py',
                    'description': 'Technical analysis engine for stock and crypto market data.'
                },
                {
                    'name': 'watchlist.py',
                    'description': 'User watchlist management with real-time alerts.'
                }
            ]
        },
        'src/bot/pipeline': {
            'description': 'Core pipeline engine that processes commands through stages.',
            'type': 'core',
            'key_files': [
                {
                    'name': 'pipeline_engine.py',
                    'description': 'Main pipeline execution engine with stage orchestration.'
                },
                {
                    'name': 'context_manager.py',
                    'description': 'Pipeline context management with data flow tracking.'
                }
            ]
        },
        'src/bot/pipeline/commands/ask': {
            'description': 'Ask command pipeline with AI processing stages.',
            'type': 'pipeline',
            'key_files': [
                {
                    'name': 'executor.py',
                    'description': 'Main execution entry point for the ask command pipeline.'
                },
                {
                    'name': 'stages',
                    'description': 'Individual processing stages for the ask pipeline.'
                }
            ]
        },
        'src/shared/ai_services': {
            'description': 'AI service implementations with multiple provider support.',
            'type': 'service',
            'key_files': [
                {
                    'name': 'ai_chat_processor.py',
                    'description': 'Main AI chat processor with OpenRouter integration.'
                },
                {
                    'name': 'tool_registry.py',
                    'description': 'Registry of available AI tools for enhanced analysis.'
                }
            ]
        },
        'src/shared/data_providers': {
            'description': 'Market data providers with intelligent aggregation.',
            'type': 'service',
            'key_files': [
                {
                    'name': 'aggregator.py',
                    'description': 'Data aggregator that combines multiple data sources.'
                },
                {
                    'name': 'yahoo_finance.py',
                    'description': 'Yahoo Finance data provider implementation.'
                }
            ]
        },
        'src/shared/database': {
            'description': 'Database integration with Supabase and connection management.',
            'type': 'storage',
            'key_files': [
                {
                    'name': 'supabase_manager.py',
                    'description': 'Supabase database connection and query management.'
                },
                {
                    'name': 'connection.py',
                    'description': 'Database connection pooling and management.'
                }
            ]
        }
    }
    
    # Add descriptions to key directories
    for path, info in key_directories.items():
        path_parts = path.split('/')
        node = find_node_by_path(architecture, path_parts)
        if node:
            node['description'] = info['description']
            node['component_type'] = info['type']
            if 'key_files' in info:
                node['key_files'] = info['key_files']
    
    # Add pipeline information
    architecture['pipelines'] = {
        'ask': {
            'name': 'Ask Pipeline',
            'description': 'AI-powered question answering pipeline with natural language processing.',
            'stages': [
                {
                    'name': 'Query Analysis',
                    'description': 'Analyzes user query to determine intent and extract symbols.'
                },
                {
                    'name': 'Data Collection',
                    'description': 'Collects relevant market data based on query analysis.'
                },
                {
                    'name': 'AI Processing',
                    'description': 'Processes query and data through AI models for insights.'
                },
                {
                    'name': 'Response Generation',
                    'description': 'Formats AI output into user-friendly response.'
                }
            ]
        },
        'analyze': {
            'name': 'Analyze Pipeline',
            'description': 'Technical analysis pipeline for comprehensive market data analysis.',
            'stages': [
                {
                    'name': 'Data Retrieval',
                    'description': 'Retrieves comprehensive market data for the requested symbol.'
                },
                {
                    'name': 'Technical Analysis',
                    'description': 'Performs technical analysis including indicators and patterns.'
                },
                {
                    'name': 'Signal Generation',
                    'description': 'Generates trading signals based on technical analysis.'
                },
                {
                    'name': 'Report Generation',
                    'description': 'Creates comprehensive analysis report with visualizations.'
                }
            ]
        }
    }
    
    return architecture

def main():
    """Main function to enhance the architecture and save it."""
    try:
        # Load the existing architecture
        architecture = load_architecture()
        
        # Enhance with additional details
        enhanced_architecture = enhance_architecture_with_details(architecture)
        
        # Save the enhanced architecture
        output_path = PROJECT_ROOT / 'architecture_detailed.json'
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(enhanced_architecture, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Enhanced architecture saved to {output_path}")
        print(f"✅ Enhanced architecture analysis complete. File saved to {output_path}")
        
    except Exception as e:
        logger.error(f"Error enhancing architecture: {e}")
        raise

if __name__ == '__main__':
    main()