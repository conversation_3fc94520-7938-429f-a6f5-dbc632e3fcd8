#!/usr/bin/env python3
"""
Test AI vs Regex Performance

This script compares AI-powered text parsing against traditional regex
for various financial text processing tasks.
"""

import asyncio
import time
import re
from typing import List, Dict, Any
import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.shared.ai_services.intelligent_text_parser import IntelligentTextParser, ParseType

class RegexBaseline:
    """Traditional regex-based parsing for comparison"""
    
    def __init__(self):
        self.symbol_patterns = [
            r'\$([A-Z]{1,5})\b',
            r'\b([A-Z]{2,5})\b(?:\s+(?:stock|shares|ticker))',
            r'(?:ticker|symbol):\s*([A-Z]{1,5})',
            r'([A-Z]{1,5})\s+(?:is|stock|price)'
        ]
        
        self.price_patterns = [
            r'\$(\d+(?:\.\d{2})?)',
            r'(\d+(?:\.\d+)?)\s*(?:dollars?|USD)',
            r'price.*?(\d+(?:\.\d{2})?)',
            r'trading\s+at\s+\$?(\d+(?:\.\d{2})?)'
        ]
        
        self.intent_patterns = {
            'price_inquiry': [
                r'\b(?:what|current|latest)\s+(?:is\s+)?(?:the\s+)?price',
                r'\bhow\s+much\s+(?:is|does)',
                r'\bquote\s+for',
                r'\bcurrent\s+value'
            ],
            'analysis_request': [
                r'\banalyze\s+',
                r'\banalysis\s+(?:of|for)',
                r'\btechnical\s+analysis',
                r'\bfundamental\s+analysis'
            ],
            'recommendation': [
                r'\bshould\s+I\s+(?:buy|sell)',
                r'\brecommend(?:ation)?',
                r'\bgood\s+(?:buy|investment)',
                r'\bworth\s+(?:buying|investing)'
            ]
        }
    
    def extract_symbols(self, text: str) -> List[str]:
        """Extract symbols using regex"""
        symbols = set()
        for pattern in self.symbol_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                symbol = match.group(1) if match.groups() else match.group(0)
                if symbol and 1 <= len(symbol) <= 5 and symbol.isupper():
                    symbols.add(symbol)
        return list(symbols)
    
    def extract_prices(self, text: str) -> List[float]:
        """Extract prices using regex"""
        prices = []
        for pattern in self.price_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                try:
                    price_str = match.group(1) if match.groups() else match.group(0)
                    price = float(price_str.replace('$', '').replace(',', ''))
                    if 0 < price < 100000:  # Reasonable price range
                        prices.append(price)
                except ValueError:
                    continue
        return prices
    
    def detect_intent(self, text: str) -> str:
        """Detect intent using regex"""
        text_lower = text.lower()
        for intent, patterns in self.intent_patterns.items():
            for pattern in patterns:
                if re.search(pattern, text_lower):
                    return intent
        return 'unknown'

class PerformanceTester:
    """Test performance and accuracy of AI vs Regex"""
    
    def __init__(self):
        self.ai_parser = IntelligentTextParser()
        self.regex_parser = RegexBaseline()
        
        # Test cases with expected results
        self.test_cases = [
            {
                'text': "What's the current price of AAPL stock?",
                'expected_symbols': ['AAPL'],
                'expected_intent': 'price_inquiry',
                'description': 'Simple price inquiry'
            },
            {
                'text': "I'm looking at $TSLA trading at $245.50 and $NVDA at $890.25",
                'expected_symbols': ['TSLA', 'NVDA'],
                'expected_prices': [245.50, 890.25],
                'description': 'Multiple symbols with prices'
            },
            {
                'text': "Can you analyze Microsoft Corporation (MSFT) fundamentals?",
                'expected_symbols': ['MSFT'],
                'expected_intent': 'analysis_request',
                'description': 'Analysis request with company name'
            },
            {
                'text': "Should I buy AMD? It's up 15% today to $125.75",
                'expected_symbols': ['AMD'],
                'expected_prices': [125.75],
                'expected_intent': 'recommendation',
                'description': 'Investment advice with percentage'
            },
            {
                'text': "Looking for stocks under $50 in the tech sector",
                'expected_prices': [50.0],
                'expected_intent': 'analysis_request',
                'description': 'Sector analysis with price filter'
            },
            {
                'text': "GOOGL earnings beat expectations, stock jumps to $2,850",
                'expected_symbols': ['GOOGL'],
                'expected_prices': [2850.0],
                'description': 'News with high-value stock'
            },
            {
                'text': "Compare AMZN vs WMT for long-term investment",
                'expected_symbols': ['AMZN', 'WMT'],
                'expected_intent': 'analysis_request',
                'description': 'Stock comparison'
            },
            {
                'text': "What do you think about cryptocurrency? BTC is at $45,000",
                'expected_symbols': ['BTC'],
                'expected_prices': [45000.0],
                'description': 'Crypto mention with high price'
            }
        ]
    
    async def run_tests(self) -> Dict[str, Any]:
        """Run comprehensive tests comparing AI vs Regex"""
        print("🧪 Starting AI vs Regex Performance Tests")
        print("=" * 60)
        
        results = {
            'test_cases': len(self.test_cases),
            'ai_results': [],
            'regex_results': [],
            'performance': {
                'ai_total_time': 0.0,
                'regex_total_time': 0.0,
                'ai_accuracy': 0.0,
                'regex_accuracy': 0.0
            }
        }
        
        for i, test_case in enumerate(self.test_cases, 1):
            print(f"\n📝 Test {i}: {test_case['description']}")
            print(f"   Text: \"{test_case['text']}\"")
            
            # Test AI parsing
            ai_result = await self._test_ai_parsing(test_case)
            results['ai_results'].append(ai_result)
            results['performance']['ai_total_time'] += ai_result['time']
            
            # Test Regex parsing
            regex_result = await self._test_regex_parsing(test_case)
            results['regex_results'].append(regex_result)
            results['performance']['regex_total_time'] += regex_result['time']
            
            # Compare results
            self._print_comparison(ai_result, regex_result, test_case)
        
        # Calculate overall accuracy
        ai_accuracy = sum(r['accuracy'] for r in results['ai_results']) / len(results['ai_results'])
        regex_accuracy = sum(r['accuracy'] for r in results['regex_results']) / len(results['regex_results'])
        
        results['performance']['ai_accuracy'] = ai_accuracy
        results['performance']['regex_accuracy'] = regex_accuracy
        
        self._print_summary(results)
        return results
    
    async def _test_ai_parsing(self, test_case: Dict) -> Dict[str, Any]:
        """Test AI-powered parsing"""
        start_time = time.time()
        
        result = {
            'symbols': [],
            'prices': [],
            'intent': 'unknown',
            'time': 0.0,
            'accuracy': 0.0,
            'errors': []
        }
        
        try:
            text = test_case['text']
            
            # Extract symbols
            if 'expected_symbols' in test_case:
                result['symbols'] = await self.ai_parser.extract_symbols(text, use_ai=True)
            
            # Extract prices
            if 'expected_prices' in test_case:
                result['prices'] = await self.ai_parser.extract_prices(text, use_ai=True)
            
            # Detect intent
            if 'expected_intent' in test_case:
                intent = await self.ai_parser.detect_intent(text, use_ai=True)
                result['intent'] = intent or 'unknown'
            
        except Exception as e:
            result['errors'].append(str(e))
        
        result['time'] = time.time() - start_time
        result['accuracy'] = self._calculate_accuracy(result, test_case)
        
        return result
    
    async def _test_regex_parsing(self, test_case: Dict) -> Dict[str, Any]:
        """Test regex-based parsing"""
        start_time = time.time()
        
        result = {
            'symbols': [],
            'prices': [],
            'intent': 'unknown',
            'time': 0.0,
            'accuracy': 0.0,
            'errors': []
        }
        
        try:
            text = test_case['text']
            
            # Extract symbols
            if 'expected_symbols' in test_case:
                result['symbols'] = self.regex_parser.extract_symbols(text)
            
            # Extract prices
            if 'expected_prices' in test_case:
                result['prices'] = self.regex_parser.extract_prices(text)
            
            # Detect intent
            if 'expected_intent' in test_case:
                result['intent'] = self.regex_parser.detect_intent(text)
            
        except Exception as e:
            result['errors'].append(str(e))
        
        result['time'] = time.time() - start_time
        result['accuracy'] = self._calculate_accuracy(result, test_case)
        
        return result
    
    def _calculate_accuracy(self, result: Dict, test_case: Dict) -> float:
        """Calculate accuracy score for a test result"""
        score = 0.0
        total_checks = 0
        
        # Check symbols
        if 'expected_symbols' in test_case:
            expected = set(test_case['expected_symbols'])
            actual = set(result['symbols'])
            if expected == actual:
                score += 1.0
            elif expected & actual:  # Partial match
                score += len(expected & actual) / len(expected | actual)
            total_checks += 1
        
        # Check prices
        if 'expected_prices' in test_case:
            expected = test_case['expected_prices']
            actual = result['prices']
            if len(expected) == len(actual):
                matches = sum(1 for e, a in zip(expected, actual) if abs(e - a) < 0.01)
                score += matches / len(expected)
            elif actual:  # Partial match
                score += 0.5
            total_checks += 1
        
        # Check intent
        if 'expected_intent' in test_case:
            if test_case['expected_intent'] == result['intent']:
                score += 1.0
            total_checks += 1
        
        return score / total_checks if total_checks > 0 else 0.0
    
    def _print_comparison(self, ai_result: Dict, regex_result: Dict, test_case: Dict):
        """Print comparison of AI vs Regex results"""
        print(f"   🤖 AI:    Symbols: {ai_result['symbols']}, Prices: {ai_result['prices']}, Intent: {ai_result['intent']}")
        print(f"   📝 Regex: Symbols: {regex_result['symbols']}, Prices: {regex_result['prices']}, Intent: {regex_result['intent']}")
        print(f"   ⏱️  Time:  AI: {ai_result['time']:.3f}s, Regex: {regex_result['time']:.3f}s")
        print(f"   🎯 Accuracy: AI: {ai_result['accuracy']:.2f}, Regex: {regex_result['accuracy']:.2f}")
        
        if ai_result['errors']:
            print(f"   ⚠️  AI Errors: {ai_result['errors']}")
        if regex_result['errors']:
            print(f"   ⚠️  Regex Errors: {regex_result['errors']}")
    
    def _print_summary(self, results: Dict):
        """Print overall test summary"""
        perf = results['performance']
        
        print("\n" + "=" * 60)
        print("📊 PERFORMANCE SUMMARY")
        print("=" * 60)
        
        print(f"\n⏱️  Total Processing Time:")
        print(f"   🤖 AI:    {perf['ai_total_time']:.3f}s")
        print(f"   📝 Regex: {perf['regex_total_time']:.3f}s")
        print(f"   📈 Speed: {'AI is faster' if perf['ai_total_time'] < perf['regex_total_time'] else 'Regex is faster'}")
        
        print(f"\n🎯 Overall Accuracy:")
        print(f"   🤖 AI:    {perf['ai_accuracy']:.2%}")
        print(f"   📝 Regex: {perf['regex_accuracy']:.2%}")
        print(f"   📈 Better: {'AI' if perf['ai_accuracy'] > perf['regex_accuracy'] else 'Regex'}")
        
        print(f"\n💡 Recommendations:")
        if perf['ai_accuracy'] > perf['regex_accuracy'] + 0.1:
            print("   ✅ AI parsing shows significantly better accuracy")
            print("   🔄 Consider replacing regex with AI for better results")
        elif perf['regex_accuracy'] > perf['ai_accuracy'] + 0.1:
            print("   ✅ Regex parsing shows better accuracy")
            print("   🔄 Consider keeping regex or improving AI prompts")
        else:
            print("   ⚖️  AI and Regex show similar accuracy")
            print("   🔄 Consider hybrid approach: AI with regex fallback")
        
        if perf['ai_total_time'] > perf['regex_total_time'] * 2:
            print("   ⚡ AI is significantly slower - consider caching or optimization")
        elif perf['ai_total_time'] < perf['regex_total_time']:
            print("   ⚡ AI is faster - good candidate for replacement")

async def main():
    """Run the AI vs Regex comparison tests"""
    tester = PerformanceTester()
    results = await tester.run_tests()
    
    # Save results
    import json
    with open('docs/audit/ai_vs_regex_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n📄 Detailed results saved to docs/audit/ai_vs_regex_results.json")

if __name__ == "__main__":
    asyncio.run(main())
