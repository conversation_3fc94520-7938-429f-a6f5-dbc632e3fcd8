#!/bin/bash
# Script to test Supabase connection from Docker container

echo "🔍 Testing Supabase connection from Docker container..."

# Check if containers are running
if ! docker ps | grep -q "tradingview-discord-bot-dev"; then
    echo "❌ Docker container 'tradingview-discord-bot-dev' is not running."
    echo "Please start the containers first with: docker-compose -f docker/compose/development.yml up -d"
    exit 1
fi

# Create a test script inside the container
echo "📝 Creating test script inside container..."
docker exec tradingview-discord-bot-dev bash -c "cat > /app/test_supabase.py << 'EOF'
import asyncio
import os
import logging
from src.shared.database.supabase_sdk_client import supabase_sdk_client

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_connection():
    # Print environment variables
    print(f\"USE_SUPABASE: {os.getenv('USE_SUPABASE')}\")
    print(f\"SUPABASE_URL: {os.getenv('SUPABASE_URL')}\")
    print(f\"SUPABASE_KEY: {os.getenv('SUPABASE_KEY')[:5]}...{os.getenv('SUPABASE_KEY')[-5:] if len(os.getenv('SUPABASE_KEY', '')) > 10 else ''}\")
    print(f\"SUPABASE_FALLBACK_IP: {os.getenv('SUPABASE_FALLBACK_IP')}\")
    
    # Initialize Supabase client
    print(\"\\n🔄 Initializing Supabase client...\")
    success = await supabase_sdk_client.initialize()
    
    if not success:
        print(\"❌ Failed to initialize Supabase client\")
        return
    
    # Test connection
    print(\"🔄 Testing Supabase connection...\")
    result = await supabase_sdk_client.test_connection()
    
    if result.get(\"connected\"):
        print(\"✅ Supabase connection successful\")
    else:
        print(f\"❌ Supabase connection failed: {result.get('error')}\")
    
    # Try to resolve Supabase hostname
    import socket
    hostname = os.getenv('SUPABASE_URL', '').replace('https://', '').replace('http://', '').split('/')[0]
    print(f\"\\n🔍 Testing DNS resolution for {hostname}...\")
    try:
        ip = socket.gethostbyname(hostname)
        print(f\"✅ Successfully resolved {hostname} to {ip}\")
    except socket.gaierror as e:
        print(f\"❌ Failed to resolve {hostname}: {e}\")

asyncio.run(test_connection())
EOF"

# Run the test script
echo -e "\n🚀 Running Supabase connection test..."
docker exec tradingview-discord-bot-dev python /app/test_supabase.py

# Check if we need to add a hosts entry
echo -e "\n🔍 Checking if we need to add hosts entry..."
hostname=$(grep SUPABASE_URL .env | cut -d'"' -f2 | sed 's|https://||' | sed 's|http://||' | sed 's|/.*||')
fallback_ip=$(grep SUPABASE_FALLBACK_IP .env | cut -d'=' -f2)

if [ -n "$hostname" ] && [ -n "$fallback_ip" ]; then
    echo "You may need to add the following entry to /etc/hosts in the Docker container:"
    echo "$fallback_ip $hostname"
    
    echo -e "\n🔧 Do you want to add this entry to the container's hosts file? (y/n)"
    read -r answer
    if [[ "$answer" =~ ^[Yy]$ ]]; then
        # First, check if the container has sudo
        if docker exec tradingview-discord-bot-dev which sudo >/dev/null 2>&1; then
            docker exec tradingview-discord-bot-dev sudo bash -c "echo '$fallback_ip $hostname' >> /etc/hosts"
        else
            # Try with direct root access
            docker exec --user=root tradingview-discord-bot-dev bash -c "echo '$fallback_ip $hostname' >> /etc/hosts"
        fi
        echo "✅ Hosts entry added. Retesting connection..."
        docker exec tradingview-discord-bot-dev python /app/test_supabase.py
    fi
fi

echo -e "\n✅ Supabase connection test completed." 