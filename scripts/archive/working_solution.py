#!/usr/bin/env python3
"""
Working Supabase Solution
========================

This uses the correct table structure and works with your existing database.
"""

import asyncio
import uuid
from datetime import datetime
from supabase import create_client

# Your credentials
SUPABASE_URL = "https://sgxjackuhalscowqrulv.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNneGphY2t1aGFsc2Nvd3FydWx2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5NjExNDMsImV4cCI6MjA2NTUzNzE0M30.-gBZv9TWmb4nkyqhpaZzRtg6BY1lPArnz7QBOehh8sE"

def get_supabase_client():
    """Get Supabase client"""
    return create_client(SUPABASE_URL, SUPABASE_KEY)

async def test_webhooks_operations():
    """Test operations with webhooks table (this one works!)"""
    print("🔹 Testing Webhooks Table Operations")
    print("-" * 50)
    
    supabase = get_supabase_client()
    
    try:
        # 1. Read existing webhooks
        print("1️⃣  Reading existing webhooks...")
        result = supabase.table('webhooks').select('*').limit(5).execute()
        print(f"   ✅ Found {len(result.data)} webhooks")
        
        # 2. Insert new webhook with correct structure
        print("\n2️⃣  Inserting new webhook...")
        new_webhook = {
            'id': str(uuid.uuid4()),  # Generate UUID
            'webhook_id': str(uuid.uuid4())[:16],  # Shorter ID
            'timestamp': datetime.now().isoformat(),
            'client_ip': '*************',
            'raw_data': {
                'symbol': 'AAPL',
                'price': 150.25,
                'alert_type': 'buy_signal'
            },
            'status': 'pending',
            'processed_data': {},
            'processed_at': None
        }
        
        insert_result = supabase.table('webhooks').insert(new_webhook).execute()
        print(f"   ✅ Successfully inserted webhook")
        print(f"   📊 New webhook ID: {insert_result.data[0]['id']}")
        
        # 3. Update the webhook
        print("\n3️⃣  Updating webhook status...")
        webhook_id = insert_result.data[0]['id']
        update_result = supabase.table('webhooks')\
            .update({'status': 'processed', 'processed_at': datetime.now().isoformat()})\
            .eq('id', webhook_id)\
            .execute()
        print(f"   ✅ Successfully updated webhook")
        
        # 4. Query webhooks by status
        print("\n4️⃣  Querying webhooks by status...")
        processed_webhooks = supabase.table('webhooks')\
            .select('*')\
            .eq('status', 'processed')\
            .limit(3)\
            .execute()
        print(f"   ✅ Found {len(processed_webhooks.data)} processed webhooks")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

async def test_alerts_operations():
    """Test operations with alerts table (using correct structure)"""
    print(f"\n🔹 Testing Alerts Table Operations")
    print("-" * 50)
    
    supabase = get_supabase_client()
    
    try:
        # Try to insert with UUID
        print("1️⃣  Inserting new alert...")
        new_alert = {
            'id': str(uuid.uuid4()),
            'created_at': datetime.now().isoformat(),
            'symbol': 'AAPL',
            'price': 150.25,
            'alert_type': 'buy_signal'
        }
        
        insert_result = supabase.table('alerts').insert(new_alert).execute()
        print(f"   ✅ Successfully inserted alert")
        print(f"   📊 New alert ID: {insert_result.data[0]['id']}")
        
        # Query alerts
        print("\n2️⃣  Querying alerts...")
        alerts = supabase.table('alerts').select('*').execute()
        print(f"   ✅ Found {len(alerts.data)} total alerts")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

async def demonstrate_trading_workflow():
    """Demonstrate a complete trading workflow"""
    print(f"\n🔹 Complete Trading Workflow Demo")
    print("-" * 50)
    
    supabase = get_supabase_client()
    
    try:
        # 1. Receive webhook (simulate TradingView alert)
        print("1️⃣  Simulating TradingView webhook...")
        webhook_data = {
            'id': str(uuid.uuid4()),
            'webhook_id': str(uuid.uuid4())[:16],
            'timestamp': datetime.now().isoformat(),
            'client_ip': '*************',
            'raw_data': {
                'symbol': 'TSLA',
                'price': 250.75,
                'alert_type': 'sell_signal',
                'timeframe': '1h',
                'signal_strength': 'strong'
            },
            'status': 'received',
            'processed_data': {},
            'processed_at': None
        }
        
        webhook_result = supabase.table('webhooks').insert(webhook_data).execute()
        print(f"   ✅ Webhook stored: {webhook_result.data[0]['id']}")
        
        # 2. Process the alert
        print("\n2️⃣  Processing alert...")
        alert_data = {
            'id': str(uuid.uuid4()),
            'created_at': datetime.now().isoformat(),
            'symbol': 'TSLA',
            'price': 250.75,
            'alert_type': 'sell_signal'
        }
        
        alert_result = supabase.table('alerts').insert(alert_data).execute()
        print(f"   ✅ Alert processed: {alert_result.data[0]['id']}")
        
        # 3. Update webhook status
        print("\n3️⃣  Updating webhook status...")
        webhook_id = webhook_result.data[0]['id']
        supabase.table('webhooks')\
            .update({
                'status': 'processed',
                'processed_data': {'alert_id': alert_result.data[0]['id']},
                'processed_at': datetime.now().isoformat()
            })\
            .eq('id', webhook_id)\
            .execute()
        print(f"   ✅ Webhook status updated")
        
        print(f"\n🎉 Complete workflow successful!")
        return True
        
    except Exception as e:
        print(f"❌ Workflow error: {e}")
        return False

def show_usage_examples():
    """Show how to use this in your app"""
    print(f"\n🎯 USAGE EXAMPLES FOR YOUR APP")
    print("=" * 50)
    
    print("""
# 1. Basic Setup
from supabase import create_client

SUPABASE_URL = "https://sgxjackuhalscowqrulv.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNneGphY2t1aGFsc2Nvd3FydWx2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5NjExNDMsImV4cCI6MjA2NTUzNzE0M30.-gBZv9TWmb4nkyqhpaZzRtg6BY1lPArnz7QBOehh8sE"

supabase = create_client(SUPABASE_URL, SUPABASE_KEY)

# 2. Store TradingView Webhook
webhook_data = {
    'id': str(uuid.uuid4()),
    'webhook_id': str(uuid.uuid4())[:16],
    'timestamp': datetime.now().isoformat(),
    'client_ip': request.client.host,
    'raw_data': webhook_payload,
    'status': 'received',
    'processed_data': {},
    'processed_at': None
}

result = supabase.table('webhooks').insert(webhook_data).execute()

# 3. Process Alert
alert_data = {
    'id': str(uuid.uuid4()),
    'created_at': datetime.now().isoformat(),
    'symbol': 'AAPL',
    'price': 150.25,
    'alert_type': 'buy_signal'
}

result = supabase.table('alerts').insert(alert_data).execute()

# 4. Query Data
alerts = supabase.table('alerts').select('*').eq('symbol', 'AAPL').execute()
webhooks = supabase.table('webhooks').select('*').eq('status', 'processed').execute()
""")

async def main():
    """Main function"""
    print("🚀 Working Supabase Solution")
    print("=" * 50)
    
    # Test webhooks (this definitely works)
    webhooks_ok = await test_webhooks_operations()
    
    # Test alerts (might work with correct structure)
    alerts_ok = await test_alerts_operations()
    
    # Demonstrate complete workflow
    workflow_ok = await demonstrate_trading_workflow()
    
    # Show usage examples
    show_usage_examples()
    
    # Summary
    print(f"\n📊 SUMMARY:")
    print("=" * 30)
    print(f"  Webhooks: {'✅ WORKING' if webhooks_ok else '❌ FAILED'}")
    print(f"  Alerts: {'✅ WORKING' if alerts_ok else '❌ FAILED'}")
    print(f"  Workflow: {'✅ WORKING' if workflow_ok else '❌ FAILED'}")
    
    if webhooks_ok:
        print(f"\n🎉 SUCCESS! You can start using Supabase now!")
        print(f"   - Use 'webhooks' table for TradingView data")
        print(f"   - Use 'alerts' table for processed alerts")
        print(f"   - Update your .env file with these credentials")

if __name__ == "__main__":
    asyncio.run(main())
