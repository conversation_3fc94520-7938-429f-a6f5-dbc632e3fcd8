"""
Visualize Recent Query

This script demonstrates how to visualize a recent query using the pipeline debugger.
"""

import asyncio
import time
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("query-visualizer")

# Import the simple pipeline debugger
from test_simple_pipeline_debug import SimplePipelineDebugger

async def visualize_recent_query():
    """Visualize the recent query about pipeline visualization"""
    
    # Create debugger for the query
    debugger = SimplePipelineDebugger(command_name="ask")
    await debugger.start()
    
    try:
        # Register pipeline stages that were likely executed
        await debugger.register_stage("initialization", "Pipeline initialization and context setup")
        await debugger.register_stage("query_processing", "Processing user query")
        await debugger.register_stage("intent_detection", "Detecting query intent")
        await debugger.register_stage("route_selection", "Selecting processing route")
        await debugger.register_stage("visualization_check", "Checking visualization capabilities")
        await debugger.register_stage("response_generation", "Generating response")
        await debugger.register_stage("completion", "Pipeline completion and cleanup")
        
        # Simulate the query flow with actual query data
        
        # Initialization stage
        await debugger.start_stage("initialization", {
            "query": "can we visualize the query i just sent a second ago?",
            "timestamp": datetime.now().isoformat(),
            "user_id": "current_user"
        })
        await asyncio.sleep(0.3)  # Actual time would vary
        await debugger.complete_stage("initialization", {
            "context": {
                "command": "ask",
                "query_type": "visualization_request",
                "timestamp": datetime.now().isoformat()
            }
        })
        
        # Query processing stage
        await debugger.start_stage("query_processing", {
            "raw_query": "can we visualize the query i just sent a second ago?",
        })
        await asyncio.sleep(0.5)
        await debugger.complete_stage("query_processing", {
            "processed_query": "visualize recent query",
            "tokens": ["visualize", "query", "recent"],
            "query_length": 7
        })
        
        # Intent detection stage
        await debugger.start_stage("intent_detection", {
            "processed_query": "visualize recent query"
        })
        await asyncio.sleep(0.7)
        await debugger.complete_stage("intent_detection", {
            "intent": "visualization_request",
            "confidence": 0.92,
            "entities": {
                "target": "query",
                "timeframe": "recent"
            }
        })
        
        # Route selection stage
        await debugger.start_stage("route_selection", {
            "intent": "visualization_request",
            "available_routes": ["standard", "visualization", "debug"]
        })
        await asyncio.sleep(0.4)
        await debugger.complete_stage("route_selection", {
            "selected_route": "visualization",
            "reason": "Query requests visualization of recent interaction"
        })
        
        # Visualization check stage
        await debugger.start_stage("visualization_check", {
            "target": "recent_query",
            "visualization_type": "pipeline"
        })
        await asyncio.sleep(0.6)
        
        # Log data flow for visualization check
        await debugger.log_data_flow("visualization_capabilities", {
            "can_visualize_pipeline": True,
            "can_visualize_discord": False,
            "available_formats": ["console", "debug_report"]
        })
        
        await debugger.complete_stage("visualization_check", {
            "visualization_available": True,
            "recommended_format": "debug_report"
        })
        
        # Response generation stage
        await debugger.start_stage("response_generation", {
            "intent": "visualization_request",
            "visualization_available": True
        })
        await asyncio.sleep(0.8)
        await debugger.complete_stage("response_generation", {
            "response_type": "visualization_instructions",
            "response_length": 250,
            "includes_code_example": True
        })
        
        # Completion stage
        await debugger.start_stage("completion", {
            "status": "completed"
        })
        await asyncio.sleep(0.2)
        await debugger.complete_stage("completion", {
            "total_time": 3.5,
            "stages_completed": 7,
            "errors": 0
        })
        
    finally:
        # Stop debugging and show report
        await debugger.stop()

async def main():
    """Run the visualization"""
    print("\n=== Visualizing Your Recent Query ===\n")
    await visualize_recent_query()
    print("\n=== Visualization Complete ===\n")

if __name__ == "__main__":
    asyncio.run(main())
