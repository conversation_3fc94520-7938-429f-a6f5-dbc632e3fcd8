#!/usr/bin/env python3
"""
Working Solution with Existing Tables
====================================

This uses the tables that already exist in your Supabase database.
"""

import asyncio
from supabase import create_client
from datetime import datetime

# Your credentials
SUPABASE_URL = "https://sgxjackuhalscowqrulv.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNneGphY2t1aGFsc2Nvd3FydWx2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5NjExNDMsImV4cCI6MjA2NTUzNzE0M30.-gBZv9TWmb4nkyqhpaZzRtg6BY1lPArnz7QBOehh8sE"

def get_supabase_client():
    """Get Supabase client"""
    return create_client(SUPABASE_URL, SUPABASE_KEY)

async def test_existing_tables():
    """Test operations with existing tables"""
    print("🔹 Testing Existing Tables")
    print("-" * 40)
    
    supabase = get_supabase_client()
    
    # Test each existing table
    tables_to_test = [
        'trading_halts',
        'alerts', 
        'webhooks',
        'users'
    ]
    
    results = {}
    
    for table_name in tables_to_test:
        print(f"\n📊 Testing {table_name}:")
        try:
            # Try to read from table
            result = supabase.table(table_name).select('*').limit(5).execute()
            print(f"   ✅ Read: {len(result.data)} rows")
            
            # Try to insert a test record
            test_data = {
                'created_at': datetime.now().isoformat(),
                'test_field': f'test_value_{table_name}'
            }
            
            # Only try insert if we can determine the schema
            if table_name == 'alerts':
                test_data.update({
                    'symbol': 'TEST',
                    'alert_type': 'test',
                    'price': 100.0
                })
            elif table_name == 'webhooks':
                test_data.update({
                    'url': 'https://example.com/test',
                    'active': True
                })
            elif table_name == 'users':
                test_data.update({
                    'email': '<EMAIL>',
                    'name': 'Test User'
                })
            
            try:
                insert_result = supabase.table(table_name).insert(test_data).execute()
                print(f"   ✅ Insert: Success")
                results[table_name] = True
            except Exception as insert_error:
                print(f"   ⚠️  Insert: {str(insert_error)[:50]}...")
                results[table_name] = False
                
        except Exception as e:
            print(f"   ❌ Error: {str(e)[:50]}...")
            results[table_name] = False
    
    return results

async def demonstrate_usage():
    """Demonstrate how to use the working tables"""
    print(f"\n🔹 Usage Examples")
    print("-" * 40)
    
    supabase = get_supabase_client()
    
    # Example 1: Working with alerts table
    print("\n1️⃣  Working with 'alerts' table:")
    try:
        # Read existing alerts
        alerts = supabase.table('alerts').select('*').execute()
        print(f"   📊 Found {len(alerts.data)} existing alerts")
        
        # Insert new alert
        new_alert = {
            'symbol': 'AAPL',
            'alert_type': 'buy_signal',
            'price': 150.25,
            'created_at': datetime.now().isoformat()
        }
        
        result = supabase.table('alerts').insert(new_alert).execute()
        print(f"   ✅ Inserted new alert: {result.data}")
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Example 2: Working with webhooks table
    print("\n2️⃣  Working with 'webhooks' table:")
    try:
        webhooks = supabase.table('webhooks').select('*').execute()
        print(f"   📊 Found {len(webhooks.data)} existing webhooks")
        
        for webhook in webhooks.data:
            print(f"   - {webhook.get('url', 'No URL')}")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")

def show_next_steps():
    """Show what to do next"""
    print(f"\n🎯 NEXT STEPS:")
    print("=" * 40)
    
    print("\n1️⃣  Current Status:")
    print("   ✅ Anon key works with existing tables")
    print("   ✅ Can read and write to existing tables")
    print("   ⚠️  Need service role key for table creation")
    
    print("\n2️⃣  What You Can Do Now:")
    print("   - Use 'alerts' table for trading alerts")
    print("   - Use 'webhooks' table for webhook management")
    print("   - Use 'users' table for user management")
    print("   - Use 'trading_halts' table for halt data")
    
    print("\n3️⃣  To Create New Tables:")
    print("   - Get service role key from Supabase Dashboard")
    print("   - Use Supabase Dashboard SQL editor to create tables")
    print("   - Or use the service role key in your app")
    
    print("\n4️⃣  Update Your .env File:")
    print("   SUPABASE_URL=\"https://sgxjackuhalscowqrulv.supabase.co\"")
    print("   SUPABASE_KEY=\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNneGphY2t1aGFsc2Nvd3FydWx2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5NjExNDMsImV4cCI6MjA2NTUzNzE0M30.-gBZv9TWmb4nkyqhpaZzRtg6BY1lPArnz7QBOehh8sE\"")

async def main():
    """Main function"""
    print("🚀 Working with Existing Supabase Tables")
    print("=" * 50)
    
    # Test existing tables
    results = await test_existing_tables()
    
    # Show usage examples
    await demonstrate_usage()
    
    # Show next steps
    show_next_steps()
    
    # Summary
    working_tables = [table for table, works in results.items() if works]
    print(f"\n📊 Summary:")
    print(f"   Working tables: {len(working_tables)}")
    for table in working_tables:
        print(f"   - {table}")

if __name__ == "__main__":
    asyncio.run(main())
