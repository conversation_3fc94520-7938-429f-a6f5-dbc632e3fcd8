#!/usr/bin/env python3
"""
Test Enhanced Symbol Extraction

This script tests the new AI-powered symbol extraction against
the old regex-based approach with real-world queries.
"""

import asyncio
import time
import sys
import os
from typing import List, Dict, Any

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.shared.ai_services.enhanced_symbol_extractor import enhanced_symbol_extractor
from src.shared.utils.symbol_extraction import unified_symbol_extractor

class SymbolExtractionTester:
    """Test AI vs Regex symbol extraction"""
    
    def __init__(self):
        self.test_cases = [
            {
                'query': "What's the current price of Apple stock?",
                'expected': ['AAPL'],
                'description': 'Company name recognition',
                'ai_advantage': 'AI recognizes Apple → AAPL'
            },
            {
                'query': "Should I buy $TSLA or $NVDA?",
                'expected': ['TSLA', 'NVDA'],
                'description': 'Dollar prefix symbols',
                'ai_advantage': 'Both should work equally well'
            },
            {
                'query': "Microsoft Corporation earnings report",
                'expected': ['MSFT'],
                'description': 'Full company name',
                'ai_advantage': 'AI understands Microsoft Corporation → MSFT'
            },
            {
                'query': "Compare Amazon vs Google for investment",
                'expected': ['AMZN', 'GOOGL'],
                'description': 'Multiple company names',
                'ai_advantage': 'AI maps Amazon → AMZN, Google → GOOGL'
            },
            {
                'query': "Tesla is up 15% today",
                'expected': ['TSLA'],
                'description': 'Company name in news context',
                'ai_advantage': 'AI recognizes Tesla → TSLA'
            },
            {
                'query': "Looking at AAPL, MSFT, and Meta",
                'expected': ['AAPL', 'MSFT', 'META'],
                'description': 'Mixed symbols and company names',
                'ai_advantage': 'AI recognizes Meta → META'
            },
            {
                'query': "What do you think about Bitcoin and Ethereum?",
                'expected': ['BTC', 'ETH'],
                'description': 'Cryptocurrency names',
                'ai_advantage': 'AI maps Bitcoin → BTC, Ethereum → ETH'
            },
            {
                'query': "JPMorgan Chase vs Bank of America analysis",
                'expected': ['JPM', 'BAC'],
                'description': 'Financial company names',
                'ai_advantage': 'AI maps JPMorgan Chase → JPM, Bank of America → BAC'
            },
            {
                'query': "Is Disney a good buy at current levels?",
                'expected': ['DIS'],
                'description': 'Single company name query',
                'ai_advantage': 'AI recognizes Disney → DIS'
            },
            {
                'query': "NVDA earnings beat expectations",
                'expected': ['NVDA'],
                'description': 'Direct symbol mention',
                'ai_advantage': 'Both should work equally well'
            },
            {
                'query': "Berkshire Hathaway Class B shares",
                'expected': ['BRK.B'],
                'description': 'Complex ticker with class',
                'ai_advantage': 'AI understands Berkshire Hathaway → BRK.B'
            },
            {
                'query': "The market is volatile today",
                'expected': [],
                'description': 'No symbols mentioned',
                'ai_advantage': 'Both should return empty'
            }
        ]
    
    async def run_tests(self) -> Dict[str, Any]:
        """Run comprehensive symbol extraction tests"""
        print("🧪 ENHANCED SYMBOL EXTRACTION TESTS")
        print("=" * 80)
        print("\nTesting AI-powered vs Regex-based symbol extraction\n")
        
        results = {
            'total_tests': len(self.test_cases),
            'ai_wins': 0,
            'regex_wins': 0,
            'ties': 0,
            'ai_total_time': 0.0,
            'regex_total_time': 0.0,
            'test_results': []
        }
        
        for i, test_case in enumerate(self.test_cases, 1):
            print(f"📝 Test {i}: {test_case['description']}")
            print(f"   Query: \"{test_case['query']}\"")
            print(f"   Expected: {test_case['expected']}")
            print(f"   AI Advantage: {test_case['ai_advantage']}")
            
            # Test AI extraction
            ai_result = await self._test_ai_extraction(test_case)
            results['ai_total_time'] += ai_result['time']
            
            # Test Regex extraction
            regex_result = await self._test_regex_extraction(test_case)
            results['regex_total_time'] += regex_result['time']
            
            # Compare results
            comparison = self._compare_results(ai_result, regex_result, test_case)
            results['test_results'].append(comparison)
            
            print(f"\n   🤖 AI Results: {ai_result['symbols']} (confidence: {ai_result['avg_confidence']:.2f})")
            print(f"   📝 Regex Results: {regex_result['symbols']}")
            print(f"   ⏱️  Time: AI {ai_result['time']:.3f}s, Regex {regex_result['time']:.3f}s")
            print(f"   🎯 Accuracy: AI {comparison['ai_accuracy']:.2f}, Regex {comparison['regex_accuracy']:.2f}")
            
            if comparison['winner'] == 'ai':
                print(f"   🏆 Winner: AI")
                results['ai_wins'] += 1
            elif comparison['winner'] == 'regex':
                print(f"   🏆 Winner: Regex")
                results['regex_wins'] += 1
            else:
                print(f"   🤝 Tie")
                results['ties'] += 1
            
            print()
        
        self._print_summary(results)
        return results
    
    async def _test_ai_extraction(self, test_case: Dict) -> Dict[str, Any]:
        """Test AI-powered extraction"""
        start_time = time.time()
        
        try:
            # Use enhanced AI extractor
            extraction_results = await enhanced_symbol_extractor.extract_symbols(
                test_case['query'], use_ai=True
            )
            
            symbols = [result.symbol for result in extraction_results]
            confidences = [result.confidence for result in extraction_results]
            avg_confidence = sum(confidences) / len(confidences) if confidences else 0.0
            
            return {
                'symbols': symbols,
                'confidences': confidences,
                'avg_confidence': avg_confidence,
                'time': time.time() - start_time,
                'method': 'ai',
                'details': extraction_results
            }
            
        except Exception as e:
            print(f"   ⚠️  AI extraction error: {e}")
            return {
                'symbols': [],
                'confidences': [],
                'avg_confidence': 0.0,
                'time': time.time() - start_time,
                'method': 'ai',
                'error': str(e)
            }
    
    async def _test_regex_extraction(self, test_case: Dict) -> Dict[str, Any]:
        """Test regex-based extraction"""
        start_time = time.time()
        
        try:
            # Use unified symbol extractor (regex-based)
            symbols = unified_symbol_extractor.extract_symbols_simple(test_case['query'])
            
            return {
                'symbols': symbols,
                'time': time.time() - start_time,
                'method': 'regex'
            }
            
        except Exception as e:
            print(f"   ⚠️  Regex extraction error: {e}")
            return {
                'symbols': [],
                'time': time.time() - start_time,
                'method': 'regex',
                'error': str(e)
            }
    
    def _compare_results(self, ai_result: Dict, regex_result: Dict, test_case: Dict) -> Dict[str, Any]:
        """Compare AI vs Regex results"""
        expected = set(test_case['expected'])
        ai_symbols = set(ai_result['symbols'])
        regex_symbols = set(regex_result['symbols'])
        
        # Calculate accuracy
        ai_accuracy = self._calculate_accuracy(ai_symbols, expected)
        regex_accuracy = self._calculate_accuracy(regex_symbols, expected)
        
        # Determine winner
        if ai_accuracy > regex_accuracy:
            winner = 'ai'
        elif regex_accuracy > ai_accuracy:
            winner = 'regex'
        else:
            winner = 'tie'
        
        return {
            'test_case': test_case['description'],
            'expected': list(expected),
            'ai_symbols': list(ai_symbols),
            'regex_symbols': list(regex_symbols),
            'ai_accuracy': ai_accuracy,
            'regex_accuracy': regex_accuracy,
            'winner': winner,
            'ai_time': ai_result['time'],
            'regex_time': regex_result['time']
        }
    
    def _calculate_accuracy(self, extracted: set, expected: set) -> float:
        """Calculate accuracy score"""
        if not expected:
            # If no symbols expected, accuracy is 1.0 if none extracted, 0.0 otherwise
            return 1.0 if not extracted else 0.0
        
        # Calculate precision and recall
        true_positives = len(extracted & expected)
        false_positives = len(extracted - expected)
        false_negatives = len(expected - extracted)
        
        if true_positives == 0:
            return 0.0
        
        precision = true_positives / (true_positives + false_positives) if (true_positives + false_positives) > 0 else 0.0
        recall = true_positives / (true_positives + false_negatives) if (true_positives + false_negatives) > 0 else 0.0
        
        # F1 score
        if precision + recall == 0:
            return 0.0
        
        f1_score = 2 * (precision * recall) / (precision + recall)
        return f1_score
    
    def _print_summary(self, results: Dict):
        """Print test summary"""
        print("=" * 80)
        print("📊 TEST SUMMARY")
        print("=" * 80)
        
        total = results['total_tests']
        ai_wins = results['ai_wins']
        regex_wins = results['regex_wins']
        ties = results['ties']
        
        print(f"\n🏆 Results:")
        print(f"   AI Wins: {ai_wins}/{total} ({ai_wins/total*100:.1f}%)")
        print(f"   Regex Wins: {regex_wins}/{total} ({regex_wins/total*100:.1f}%)")
        print(f"   Ties: {ties}/{total} ({ties/total*100:.1f}%)")
        
        print(f"\n⏱️  Performance:")
        print(f"   Total AI Time: {results['ai_total_time']:.3f}s")
        print(f"   Total Regex Time: {results['regex_total_time']:.3f}s")
        print(f"   Average AI Time: {results['ai_total_time']/total:.3f}s per test")
        print(f"   Average Regex Time: {results['regex_total_time']/total:.3f}s per test")
        
        # Calculate overall accuracy
        ai_accuracies = [r['ai_accuracy'] for r in results['test_results']]
        regex_accuracies = [r['regex_accuracy'] for r in results['test_results']]
        
        avg_ai_accuracy = sum(ai_accuracies) / len(ai_accuracies)
        avg_regex_accuracy = sum(regex_accuracies) / len(regex_accuracies)
        
        print(f"\n🎯 Average Accuracy:")
        print(f"   AI: {avg_ai_accuracy:.2%}")
        print(f"   Regex: {avg_regex_accuracy:.2%}")
        
        print(f"\n💡 Key Findings:")
        if ai_wins > regex_wins:
            print(f"   ✅ AI extraction shows superior performance")
            print(f"   🎯 AI excels at company name recognition")
            print(f"   🧠 AI provides better context understanding")
        elif regex_wins > ai_wins:
            print(f"   ✅ Regex extraction shows competitive performance")
            print(f"   ⚡ Regex is faster and more deterministic")
        else:
            print(f"   ⚖️  AI and Regex show similar performance")
        
        print(f"\n🚀 Recommendations:")
        if ai_wins > regex_wins:
            print(f"   🔄 Migrate to AI-powered extraction for better accuracy")
            print(f"   🛡️  Keep regex as fallback for reliability")
            print(f"   📊 Monitor AI performance and costs")
        else:
            print(f"   🔄 Consider hybrid approach: AI for complex cases, regex for simple")
            print(f"   💰 Evaluate cost vs benefit of AI extraction")

async def main():
    """Run the symbol extraction tests"""
    tester = SymbolExtractionTester()
    results = await tester.run_tests()
    
    # Save results
    import json
    with open('docs/audit/symbol_extraction_test_results.json', 'w') as f:
        # Convert sets to lists for JSON serialization
        json_results = results.copy()
        for test_result in json_results['test_results']:
            for key in ['ai_symbols', 'regex_symbols', 'expected']:
                if isinstance(test_result[key], set):
                    test_result[key] = list(test_result[key])
        
        json.dump(json_results, f, indent=2)
    
    print(f"\n📄 Detailed results saved to docs/audit/symbol_extraction_test_results.json")

if __name__ == "__main__":
    asyncio.run(main())
