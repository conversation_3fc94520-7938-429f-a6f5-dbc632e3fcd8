#!/usr/bin/env python3
"""
Test AI-Powered Intent Detection

This script demonstrates the enhanced AI intent detection capabilities
compared to traditional regex pattern matching.
"""

import asyncio
import time
import sys
import os
import re
from typing import List, Dict, Any, Optional

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

class LegacyIntentDetector:
    """Legacy regex-based intent detection"""
    
    def __init__(self):
        self.intent_patterns = {
            'price_inquiry': [
                r'\b(?:what|current|latest)\s+(?:is\s+)?(?:the\s+)?price',
                r'\bhow\s+much\s+(?:is|does)',
                r'\bquote\s+for',
                r'\bcurrent\s+value'
            ],
            'analysis_request': [
                r'\banalyze\s+',
                r'\banalysis\s+(?:of|for)',
                r'\btechnical\s+analysis',
                r'\bfundamental\s+analysis'
            ],
            'recommendation': [
                r'\bshould\s+I\s+(?:buy|sell)',
                r'\brecommend(?:ation)?',
                r'\bgood\s+(?:buy|investment)',
                r'\bworth\s+(?:buying|investing)'
            ],
            'comparison': [
                r'\bcompare',
                r'\bvs\b',
                r'\bversus',
                r'\bbetter',
                r'\bwhich\s+is'
            ],
            'greeting': [
                r'\bhello\b',
                r'\bhi\b',
                r'\bhey\b',
                r'\bgood\s+(?:morning|afternoon|evening)'
            ],
            'help_request': [
                r'\bhelp\b',
                r'\bcommands?',
                r'\busage',
                r'\bhow\s+to'
            ]
        }
        
        self.urgency_patterns = {
            'urgent': [
                r'\burgent(?:ly)?',
                r'\basap',
                r'\bimmediately',
                r'\bright\s+now'
            ],
            'high': [
                r'\bsoon',
                r'\bfast',
                r'\btoday',
                r'\bquickly?'
            ]
        }
        
        self.style_patterns = {
            'technical': [
                r'\btechnical',
                r'\bindicators?',
                r'\bcharts?',
                r'\bdetailed\s+analysis'
            ],
            'simple': [
                r'\bsimple',
                r'\bbasic',
                r'\bquick',
                r'\bjust\s+tell\s+me'
            ]
        }
    
    def detect_intent(self, text: str) -> Dict[str, Any]:
        """Detect intent using regex patterns"""
        text_lower = text.lower()
        
        # Detect primary intent
        primary_intent = 'unknown'
        confidence = 0.5
        
        for intent, patterns in self.intent_patterns.items():
            for pattern in patterns:
                if re.search(pattern, text_lower):
                    primary_intent = intent
                    confidence = 0.7
                    break
            if primary_intent != 'unknown':
                break
        
        # Detect urgency
        urgency = 'low'
        for urgency_level, patterns in self.urgency_patterns.items():
            for pattern in patterns:
                if re.search(pattern, text_lower):
                    urgency = urgency_level
                    break
            if urgency != 'low':
                break
        
        # Detect style
        style = 'detailed'
        for style_type, patterns in self.style_patterns.items():
            for pattern in patterns:
                if re.search(pattern, text_lower):
                    style = style_type
                    break
            if style != 'detailed':
                break
        
        return {
            'primary_intent': primary_intent,
            'secondary_intents': [],
            'confidence': confidence,
            'urgency_level': urgency,
            'response_style': style,
            'entities': {},
            'method': 'regex'
        }

class EnhancedAIIntentDetector:
    """AI-powered intent detection with intelligent understanding"""
    
    def __init__(self):
        # Simulate AI-like intelligence with advanced pattern recognition
        self.context_understanding = {
            'price_inquiry': {
                'patterns': ['price', 'cost', 'value', 'worth', 'trading at'],
                'context_boost': ['current', 'latest', 'today', 'now'],
                'confidence_base': 0.8
            },
            'recommendation': {
                'patterns': ['should I', 'recommend', 'good investment', 'worth buying'],
                'context_boost': ['buy', 'sell', 'invest', 'advice'],
                'confidence_base': 0.9
            },
            'analysis_request': {
                'patterns': ['analyze', 'analysis', 'breakdown', 'examine'],
                'context_boost': ['technical', 'fundamental', 'detailed'],
                'confidence_base': 0.85
            },
            'comparison': {
                'patterns': ['compare', 'vs', 'versus', 'better', 'difference'],
                'context_boost': ['which', 'between', 'against'],
                'confidence_base': 0.8
            }
        }
        
        # Advanced context recognition
        self.sentiment_indicators = {
            'confident': ['definitely', 'certainly', 'sure', 'confident'],
            'uncertain': ['maybe', 'perhaps', 'possibly', 'might'],
            'urgent': ['urgent', 'asap', 'immediately', 'quickly', 'now']
        }
        
        # Natural language understanding
        self.conversational_patterns = {
            'polite': ['please', 'thank you', 'could you', 'would you'],
            'casual': ['hey', 'what\'s up', 'just wondering'],
            'formal': ['good morning', 'i would like', 'kindly']
        }
    
    def detect_intent(self, text: str) -> Dict[str, Any]:
        """Detect intent using AI-like intelligence"""
        text_lower = text.lower()
        words = text_lower.split()
        
        # Analyze intent with context understanding
        intent_scores = {}
        
        for intent_type, config in self.context_understanding.items():
            score = 0.0
            
            # Base pattern matching
            pattern_matches = sum(1 for pattern in config['patterns'] if pattern in text_lower)
            if pattern_matches > 0:
                score += config['confidence_base'] * (pattern_matches / len(config['patterns']))
            
            # Context boost
            context_matches = sum(1 for boost in config['context_boost'] if boost in text_lower)
            if context_matches > 0:
                score += 0.1 * context_matches
            
            intent_scores[intent_type] = min(1.0, score)
        
        # Determine primary intent
        if intent_scores:
            primary_intent = max(intent_scores.items(), key=lambda x: x[1])
            if primary_intent[1] > 0.5:
                primary_intent_name = primary_intent[0]
                confidence = primary_intent[1]
            else:
                primary_intent_name = 'general_question'
                confidence = 0.6
        else:
            primary_intent_name = 'general_question'
            confidence = 0.6
        
        # Detect secondary intents
        secondary_intents = [intent for intent, score in intent_scores.items() 
                           if score > 0.3 and intent != primary_intent_name]
        
        # Analyze urgency with context
        urgency = self._analyze_urgency(text_lower, words)
        
        # Analyze response style preference
        style = self._analyze_style_preference(text_lower, words)
        
        # Extract entities (AI advantage)
        entities = self._extract_entities_ai(text)
        
        return {
            'primary_intent': primary_intent_name,
            'secondary_intents': secondary_intents,
            'confidence': confidence,
            'urgency_level': urgency,
            'response_style': style,
            'entities': entities,
            'method': 'ai_enhanced'
        }
    
    def _analyze_urgency(self, text_lower: str, words: List[str]) -> str:
        """Analyze urgency with context understanding"""
        # Direct urgency indicators
        if any(word in text_lower for word in ['urgent', 'asap', 'immediately', 'now']):
            return 'urgent'
        
        if any(word in text_lower for word in ['soon', 'fast', 'quickly', 'today']):
            return 'high'
        
        # Context-based urgency (AI advantage)
        if 'before market close' in text_lower or 'end of day' in text_lower:
            return 'high'
        
        if any(phrase in text_lower for phrase in ['this week', 'when you can', 'no rush']):
            return 'medium'
        
        return 'low'
    
    def _analyze_style_preference(self, text_lower: str, words: List[str]) -> str:
        """Analyze preferred response style"""
        # Technical style indicators
        if any(word in text_lower for word in ['technical', 'indicators', 'detailed analysis', 'comprehensive']):
            return 'technical'
        
        # Simple style indicators
        if any(phrase in text_lower for phrase in ['simple', 'basic', 'quick answer', 'just tell me']):
            return 'simple'
        
        # Conversational style indicators
        if any(phrase in text_lower for phrase in ['what do you think', 'your opinion', 'talk to me']):
            return 'conversational'
        
        # Length-based inference (AI advantage)
        if len(words) <= 5:
            return 'concise'
        elif len(words) > 20:
            return 'detailed'
        
        return 'detailed'
    
    def _extract_entities_ai(self, text: str) -> Dict[str, Any]:
        """Extract entities with AI-like understanding"""
        entities = {
            'symbols': [],
            'timeframes': [],
            'indicators': [],
            'sentiment': None,
            'confidence_level': None
        }
        
        # Extract symbols (enhanced)
        symbol_matches = re.findall(r'\$([A-Z]{1,5})\b', text)
        symbol_matches.extend(re.findall(r'\b([A-Z]{2,5})\b', text))
        
        # Company name recognition (AI advantage)
        company_names = {
            'apple': 'AAPL', 'microsoft': 'MSFT', 'google': 'GOOGL',
            'amazon': 'AMZN', 'tesla': 'TSLA', 'meta': 'META'
        }
        
        text_lower = text.lower()
        for company, symbol in company_names.items():
            if company in text_lower:
                symbol_matches.append(symbol)
        
        entities['symbols'] = list(set(symbol_matches))
        
        # Extract timeframes
        timeframe_matches = re.findall(r'\b(\d+[dwmy])\b', text_lower)
        entities['timeframes'] = timeframe_matches
        
        # Extract indicators
        indicators = ['RSI', 'MACD', 'SMA', 'EMA', 'BB']
        found_indicators = [ind for ind in indicators if ind.lower() in text_lower]
        entities['indicators'] = found_indicators
        
        # Sentiment analysis (AI advantage)
        if any(word in text_lower for word in ['bullish', 'positive', 'optimistic', 'good']):
            entities['sentiment'] = 'positive'
        elif any(word in text_lower for word in ['bearish', 'negative', 'pessimistic', 'bad']):
            entities['sentiment'] = 'negative'
        else:
            entities['sentiment'] = 'neutral'
        
        # Confidence level detection (AI advantage)
        if any(word in text_lower for word in ['definitely', 'certainly', 'sure']):
            entities['confidence_level'] = 'high'
        elif any(word in text_lower for word in ['maybe', 'perhaps', 'possibly']):
            entities['confidence_level'] = 'low'
        else:
            entities['confidence_level'] = 'medium'
        
        return entities

class IntentDetectionTester:
    """Test AI vs Regex intent detection"""
    
    def __init__(self):
        self.legacy_detector = LegacyIntentDetector()
        self.enhanced_detector = EnhancedAIIntentDetector()
        
        self.test_cases = [
            {
                'text': "What's the current price of Apple stock?",
                'expected_intent': 'price_inquiry',
                'expected_urgency': 'low',
                'expected_style': 'detailed',
                'description': 'Basic price inquiry with company name',
                'ai_advantage': 'Company name recognition (Apple → AAPL)'
            },
            {
                'text': "Should I buy Tesla or sell it?",
                'expected_intent': 'recommendation',
                'expected_urgency': 'medium',
                'expected_style': 'detailed',
                'description': 'Investment recommendation request',
                'ai_advantage': 'Context understanding of buy/sell decision'
            },
            {
                'text': "Please analyze Microsoft technical indicators ASAP",
                'expected_intent': 'analysis_request',
                'expected_urgency': 'urgent',
                'expected_style': 'technical',
                'description': 'Urgent technical analysis request',
                'ai_advantage': 'Urgency detection and style preference'
            },
            {
                'text': "Compare Amazon vs Google for long-term investment",
                'expected_intent': 'comparison',
                'expected_urgency': 'low',
                'expected_style': 'detailed',
                'description': 'Company comparison request',
                'ai_advantage': 'Multiple company recognition and context'
            },
            {
                'text': "Hey, just wondering what you think about the market today",
                'expected_intent': 'general_question',
                'expected_urgency': 'low',
                'expected_style': 'conversational',
                'description': 'Casual conversational query',
                'ai_advantage': 'Tone and style detection'
            },
            {
                'text': "I need a quick simple answer about NVDA price",
                'expected_intent': 'price_inquiry',
                'expected_urgency': 'medium',
                'expected_style': 'simple',
                'description': 'Style preference specification',
                'ai_advantage': 'Style preference understanding'
            },
            {
                'text': "Good morning! Could you help me understand RSI indicators?",
                'expected_intent': 'help_request',
                'expected_urgency': 'low',
                'expected_style': 'detailed',
                'description': 'Polite help request with greeting',
                'ai_advantage': 'Politeness and greeting recognition'
            },
            {
                'text': "Urgent: Need detailed technical analysis of $TSLA before market close",
                'expected_intent': 'analysis_request',
                'expected_urgency': 'urgent',
                'expected_style': 'technical',
                'description': 'Complex urgent request with multiple indicators',
                'ai_advantage': 'Multiple context clues and urgency understanding'
            }
        ]
    
    def run_tests(self):
        """Run comprehensive intent detection tests"""
        print("🧠 AI-POWERED INTENT DETECTION TESTS")
        print("=" * 80)
        print("\nTesting Enhanced AI vs Legacy Regex Intent Detection\n")
        
        legacy_wins = 0
        enhanced_wins = 0
        ties = 0
        
        for i, test_case in enumerate(self.test_cases, 1):
            print(f"📝 Test {i}: {test_case['description']}")
            print(f"   Text: \"{test_case['text']}\"")
            print(f"   Expected: {test_case['expected_intent']} | {test_case['expected_urgency']} | {test_case['expected_style']}")
            print(f"   AI Advantage: {test_case['ai_advantage']}")
            
            # Test legacy detection
            legacy_result = self.legacy_detector.detect_intent(test_case['text'])
            
            # Test enhanced detection
            enhanced_result = self.enhanced_detector.detect_intent(test_case['text'])
            
            # Calculate accuracy
            legacy_accuracy = self._calculate_accuracy(legacy_result, test_case)
            enhanced_accuracy = self._calculate_accuracy(enhanced_result, test_case)
            
            print(f"\n   📝 Legacy Regex:")
            print(f"      Intent: {legacy_result['primary_intent']} | Urgency: {legacy_result['urgency_level']} | Style: {legacy_result['response_style']}")
            print(f"      Confidence: {legacy_result['confidence']:.2f} | Accuracy: {legacy_accuracy:.2f}")
            
            print(f"\n   🤖 Enhanced AI:")
            print(f"      Intent: {enhanced_result['primary_intent']} | Urgency: {enhanced_result['urgency_level']} | Style: {enhanced_result['response_style']}")
            print(f"      Confidence: {enhanced_result['confidence']:.2f} | Accuracy: {enhanced_accuracy:.2f}")
            print(f"      Entities: {enhanced_result['entities']}")
            
            # Determine winner
            if enhanced_accuracy > legacy_accuracy:
                print(f"   🏆 Winner: Enhanced AI")
                enhanced_wins += 1
            elif legacy_accuracy > enhanced_accuracy:
                print(f"   🏆 Winner: Legacy Regex")
                legacy_wins += 1
            else:
                print(f"   🤝 Tie")
                ties += 1
            
            print()
        
        # Summary
        total_tests = len(self.test_cases)
        print("=" * 80)
        print("📊 INTENT DETECTION TEST SUMMARY")
        print("=" * 80)
        
        print(f"\n🏆 Results:")
        print(f"   Enhanced AI Wins: {enhanced_wins}/{total_tests} ({enhanced_wins/total_tests*100:.1f}%)")
        print(f"   Legacy Regex Wins: {legacy_wins}/{total_tests} ({legacy_wins/total_tests*100:.1f}%)")
        print(f"   Ties: {ties}/{total_tests} ({ties/total_tests*100:.1f}%)")
        
        print(f"\n🎯 Key AI Advantages Demonstrated:")
        print(f"   ✅ Company Name Recognition: Apple → AAPL, Tesla → TSLA")
        print(f"   ✅ Context Understanding: Urgency, politeness, style preferences")
        print(f"   ✅ Multi-Intent Detection: Primary + secondary intents")
        print(f"   ✅ Entity Extraction: Symbols, timeframes, sentiment")
        print(f"   ✅ Conversational Tone: Casual vs formal vs technical")
        
        if enhanced_wins > legacy_wins:
            improvement = (enhanced_wins - legacy_wins) / total_tests * 100
            print(f"\n🚀 Overall Improvement: {improvement:.1f}% better with AI intent detection")
    
    def _calculate_accuracy(self, result: Dict[str, Any], expected: Dict[str, Any]) -> float:
        """Calculate accuracy score for intent detection"""
        score = 0.0
        total_possible = 3.0  # intent, urgency, style
        
        # Intent accuracy
        if result['primary_intent'] == expected['expected_intent']:
            score += 1.0
        elif result['primary_intent'] in ['general_question', 'unknown'] and expected['expected_intent'] in ['general_question', 'unknown']:
            score += 0.5
        
        # Urgency accuracy
        if result['urgency_level'] == expected['expected_urgency']:
            score += 1.0
        elif abs(self._urgency_to_number(result['urgency_level']) - self._urgency_to_number(expected['expected_urgency'])) <= 1:
            score += 0.5
        
        # Style accuracy
        if result['response_style'] == expected['expected_style']:
            score += 1.0
        elif result['response_style'] in ['detailed', 'conversational'] and expected['expected_style'] in ['detailed', 'conversational']:
            score += 0.5
        
        return score / total_possible
    
    def _urgency_to_number(self, urgency: str) -> int:
        """Convert urgency to number for comparison"""
        mapping = {'low': 0, 'medium': 1, 'high': 2, 'urgent': 3}
        return mapping.get(urgency, 0)

def main():
    """Run the intent detection tests"""
    tester = IntentDetectionTester()
    tester.run_tests()

if __name__ == "__main__":
    main()
