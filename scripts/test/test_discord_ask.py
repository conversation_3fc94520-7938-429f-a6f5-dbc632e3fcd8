#!/usr/bin/env python3
"""
Test the actual Discord ask command system
"""

import sys
import os
import asyncio
from datetime import datetime

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

async def test_discord_ask_command():
    """Test the actual Discord ask command"""
    print("🔍 Testing Actual Discord Ask Command")
    print("=" * 60)
    
    try:
        from src.bot.pipeline.commands.ask.executor import execute_ask_pipeline
        
        # Test queries that you would actually ask
        test_queries = [
            "What are the key support and resistance levels for NVDA?",
            "Is NVDA oversold or overbought based on RSI?",
            "What does the MACD indicator tell us about momentum?",
            "Should I buy or sell NVDA right now?",
            "What are the risks with NVDA at current levels?"
        ]
        
        print("🧪 Testing Real Discord Ask Pipeline...")
        print("=" * 60)
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n📝 Query {i}: {query}")
            print("-" * 50)
            
            try:
                # Call the actual ask pipeline
                result = await execute_ask_pipeline(query, user_id="test_user", username="test_user")
                
                print(f"📊 Pipeline Result:")
                print(f"  Type: {type(result)}")
                print(f"  Content: {str(result)[:400]}...")
                
                # Check for common issues
                issues = []
                if 'RSI 72' in str(result) or 'RSI 70' in str(result):
                    issues.append("❌ Contains fabricated RSI values")
                if '$875' in str(result) or '$950' in str(result):
                    issues.append("❌ Contains fabricated prices")
                if 'will reach' in str(result) or 'targets' in str(result):
                    issues.append("❌ Contains predictions")
                if str(result).endswith('...'):
                    issues.append("❌ Response appears truncated")
                if 'Unable to retrieve technical data' in str(result):
                    issues.append("❌ Data retrieval failed")
                if 'Error occurred' in str(result):
                    issues.append("❌ System error occurred")
                
                if issues:
                    print(f"  ⚠️ Issues found:")
                    for issue in issues:
                        print(f"    {issue}")
                else:
                    print(f"  ✅ No obvious issues detected")
                
            except Exception as e:
                print(f"❌ Error processing query: {e}")
                import traceback
                traceback.print_exc()
        
        print("\n✅ Discord Ask Command Test Completed")
        return True
        
    except Exception as e:
        print(f"❌ Discord Ask Command Test Failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the Discord ask command test"""
    print("🚀 Testing Actual Discord Ask Command System")
    print("=" * 80)
    
    # Run the async test
    success = asyncio.run(test_discord_ask_command())
    
    print("=" * 80)
    if success:
        print("🎉 Discord ask command test completed!")
        print("This shows us what the actual system produces when you ask questions")
    else:
        print("❌ Discord ask command test failed - this shows us what's wrong!")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
