#!/usr/bin/env python3
"""
Test Suite for AI Intent Detection System

This script tests the unified intent detection system and context-aware processing
to ensure natural language understanding works correctly.
"""

import asyncio
import sys
import os
import time
from typing import Dict, List, Any

# Add src to path
sys.path.append('src')

from src.shared.error_handling.logging import get_logger
from src.bot.ai.unified_intent_system import (
    unified_intent_detector, analyze_user_intent, get_processing_pipeline,
    UnifiedIntent, ProcessingPipeline
)
from src.bot.ai.context_aware_processor import (
    context_aware_processor, process_with_context
)

logger = get_logger(__name__)

class IntentDetectionTester:
    """Comprehensive tester for AI intent detection system"""
    
    def __init__(self):
        self.test_cases = [
            # Basic Intent Detection
            {
                'query': 'What is the price of Apple stock?',
                'expected_intent': UnifiedIntent.PRICE_CHECK,
                'expected_pipeline': ProcessingPipeline.ASK_PIPELINE,
                'expected_symbols': ['AAPL']
            },
            {
                'query': 'Analyze Tesla technical indicators',
                'expected_intent': UnifiedIntent.TECHNICAL_ANALYSIS,
                'expected_pipeline': ProcessingPipeline.ANALYZE_PIPELINE,
                'expected_symbols': ['TSLA']
            },
            {
                'query': 'Compare Apple and Microsoft',
                'expected_intent': UnifiedIntent.COMPARISON,
                'expected_pipeline': ProcessingPipeline.BATCH_PIPELINE,
                'expected_symbols': ['AAPL', 'MSFT']
            },
            
            # Natural Language Variations
            {
                'query': 'How is GOOGL doing today?',
                'expected_intent': UnifiedIntent.STOCK_ANALYSIS,
                'expected_pipeline': ProcessingPipeline.ANALYZE_PIPELINE,
                'expected_symbols': ['GOOGL']
            },
            {
                'query': 'I want to know about Amazon earnings',
                'expected_intent': UnifiedIntent.FUNDAMENTAL_ANALYSIS,
                'expected_pipeline': ProcessingPipeline.ANALYZE_PIPELINE,
                'expected_symbols': ['AMZN']
            },
            {
                'query': 'Show me support and resistance levels for NVDA',
                'expected_intent': UnifiedIntent.ZONES_ANALYSIS,
                'expected_pipeline': ProcessingPipeline.ZONES_PIPELINE,
                'expected_symbols': ['NVDA']
            },
            
            # Context-Dependent Queries
            {
                'query': 'What about Microsoft?',
                'expected_intent': UnifiedIntent.CONTEXT_CONTINUATION,
                'context_dependent': True
            },
            {
                'query': 'How does it compare?',
                'expected_intent': UnifiedIntent.COMPARISON,
                'context_dependent': True
            },
            
            # Utility and Help
            {
                'query': 'Help me understand technical analysis',
                'expected_intent': UnifiedIntent.HELP_REQUEST,
                'expected_pipeline': ProcessingPipeline.HELP_PIPELINE
            },
            {
                'query': 'Is the bot working?',
                'expected_intent': UnifiedIntent.STATUS_CHECK,
                'expected_pipeline': ProcessingPipeline.UTILITY_PIPELINE
            },
            
            # Complex Queries
            {
                'query': 'Analyze AAPL, MSFT, and GOOGL RSI and MACD for the past week',
                'expected_intent': UnifiedIntent.BATCH_ANALYSIS,
                'expected_pipeline': ProcessingPipeline.BATCH_PIPELINE,
                'expected_symbols': ['AAPL', 'MSFT', 'GOOGL'],
                'expected_indicators': ['RSI', 'MACD'],
                'expected_timeframe': '1w'
            }
        ]
        
        self.context_test_scenarios = [
            {
                'name': 'Symbol Context Continuation',
                'queries': [
                    'Analyze Apple stock',
                    'What about the technical indicators?',
                    'How does it compare to Microsoft?'
                ],
                'expected_context_flow': True
            },
            {
                'name': 'Multi-Turn Analysis',
                'queries': [
                    'Show me Tesla price',
                    'What are the support levels?',
                    'Any recent news?'
                ],
                'expected_context_flow': True
            }
        ]
    
    async def run_all_tests(self):
        """Run comprehensive test suite"""
        print("🚀 Starting AI Intent Detection System Tests")
        print("=" * 60)
        
        # Test basic intent detection
        await self.test_basic_intent_detection()
        
        # Test context-aware processing
        await self.test_context_aware_processing()
        
        # Test parameter extraction
        await self.test_parameter_extraction()
        
        # Test conversation flow
        await self.test_conversation_flow()
        
        # Test performance
        await self.test_performance()
        
        print("\n" + "=" * 60)
        print("🎉 AI Intent Detection System Tests Complete!")
    
    async def test_basic_intent_detection(self):
        """Test basic intent detection capabilities"""
        print("\n🧪 Testing Basic Intent Detection...")
        
        passed = 0
        total = 0
        
        for test_case in self.test_cases:
            if test_case.get('context_dependent'):
                continue  # Skip context-dependent tests for now
            
            total += 1
            query = test_case['query']
            expected_intent = test_case['expected_intent']
            
            try:
                # Test intent analysis
                result = await analyze_user_intent(query, "test_user_1")
                
                # Check intent
                intent_correct = result.primary_intent == expected_intent
                
                # Check pipeline
                pipeline_correct = True
                if 'expected_pipeline' in test_case:
                    pipeline_correct = result.recommended_pipeline == test_case['expected_pipeline']
                
                # Check symbols
                symbols_correct = True
                if 'expected_symbols' in test_case:
                    extracted_symbols = []
                    if 'symbols' in result.parameters:
                        extracted_symbols = result.parameters['symbols'].value
                    symbols_correct = set(extracted_symbols) >= set(test_case['expected_symbols'])
                
                if intent_correct and pipeline_correct and symbols_correct:
                    print(f"  ✅ {query[:50]}... -> {result.primary_intent.value}")
                    passed += 1
                else:
                    print(f"  ❌ {query[:50]}...")
                    print(f"     Expected: {expected_intent.value}, Got: {result.primary_intent.value}")
                    if not pipeline_correct:
                        print(f"     Pipeline mismatch: expected {test_case.get('expected_pipeline')}, got {result.recommended_pipeline}")
                    if not symbols_correct:
                        print(f"     Symbol mismatch: expected {test_case.get('expected_symbols')}, got {extracted_symbols}")
                
            except Exception as e:
                print(f"  ❌ {query[:50]}... -> ERROR: {e}")
                total += 1
        
        print(f"\n📊 Basic Intent Detection: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    async def test_context_aware_processing(self):
        """Test context-aware processing capabilities"""
        print("\n🧪 Testing Context-Aware Processing...")
        
        passed = 0
        total = len(self.context_test_scenarios)
        
        for scenario in self.context_test_scenarios:
            try:
                user_id = f"test_user_{scenario['name'].replace(' ', '_').lower()}"
                session_id = f"session_{int(time.time())}"
                
                print(f"\n  📝 Scenario: {scenario['name']}")
                
                context_maintained = True
                previous_symbols = []
                
                for i, query in enumerate(scenario['queries']):
                    result, processing_context = await process_with_context(
                        query, user_id, session_id
                    )
                    
                    print(f"    Query {i+1}: {query}")
                    print(f"    Intent: {result.primary_intent.value} (confidence: {result.confidence:.2f})")
                    print(f"    Context confidence: {processing_context.context_confidence:.2f}")
                    
                    # Check if context is being maintained
                    if i > 0:  # After first query
                        if processing_context.conversation_context.last_symbols:
                            current_symbols = processing_context.conversation_context.last_symbols
                            if previous_symbols and not any(sym in current_symbols for sym in previous_symbols):
                                context_maintained = False
                    
                    if 'symbols' in result.parameters:
                        previous_symbols = result.parameters['symbols'].value
                
                if context_maintained:
                    print(f"  ✅ Context maintained throughout scenario")
                    passed += 1
                else:
                    print(f"  ❌ Context not properly maintained")
                
            except Exception as e:
                print(f"  ❌ Scenario failed: {e}")
        
        print(f"\n📊 Context-Aware Processing: {passed}/{total} scenarios passed ({passed/total*100:.1f}%)")
    
    async def test_parameter_extraction(self):
        """Test parameter extraction capabilities"""
        print("\n🧪 Testing Parameter Extraction...")
        
        test_queries = [
            {
                'query': 'Analyze AAPL RSI and MACD for 1 week',
                'expected_params': {
                    'symbols': ['AAPL'],
                    'indicators': ['RSI', 'MACD'],
                    'timeframe': '1w'
                }
            },
            {
                'query': 'Compare Apple and Microsoft daily performance',
                'expected_params': {
                    'symbols': ['AAPL', 'MSFT'],
                    'timeframe': 'daily'
                }
            }
        ]
        
        passed = 0
        total = len(test_queries)
        
        for test in test_queries:
            try:
                result = await analyze_user_intent(test['query'], "test_user_params")
                
                params_correct = True
                for param_name, expected_value in test['expected_params'].items():
                    if param_name in result.parameters:
                        actual_value = result.parameters[param_name].value
                        if isinstance(expected_value, list):
                            if not set(actual_value) >= set(expected_value):
                                params_correct = False
                        else:
                            if actual_value != expected_value:
                                params_correct = False
                    else:
                        params_correct = False
                
                if params_correct:
                    print(f"  ✅ {test['query'][:50]}...")
                    passed += 1
                else:
                    print(f"  ❌ {test['query'][:50]}...")
                    print(f"     Expected: {test['expected_params']}")
                    print(f"     Got: {[{name: param.value for name, param in result.parameters.items()}]}")
                
            except Exception as e:
                print(f"  ❌ Parameter extraction failed: {e}")
        
        print(f"\n📊 Parameter Extraction: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    async def test_conversation_flow(self):
        """Test natural conversation flow"""
        print("\n🧪 Testing Conversation Flow...")
        
        conversation = [
            "What's the price of Apple?",
            "How about Microsoft?",
            "Compare them",
            "What are the technical indicators?",
            "Any recent news?"
        ]
        
        user_id = "test_conversation_user"
        session_id = f"conversation_session_{int(time.time())}"
        
        try:
            for i, query in enumerate(conversation):
                result, context = await process_with_context(query, user_id, session_id)
                
                print(f"  {i+1}. User: {query}")
                print(f"     Intent: {result.primary_intent.value}")
                print(f"     Pipeline: {result.recommended_pipeline.value}")
                print(f"     Context confidence: {context.context_confidence:.2f}")
                
                if result.suggestions:
                    print(f"     Suggestions: {result.suggestions[:2]}")
                print()
            
            print("  ✅ Conversation flow test completed successfully")
            
        except Exception as e:
            print(f"  ❌ Conversation flow test failed: {e}")
    
    async def test_performance(self):
        """Test performance of intent detection"""
        print("\n🧪 Testing Performance...")
        
        test_queries = [
            "What is AAPL price?",
            "Analyze Tesla technical indicators",
            "Compare Apple and Microsoft",
            "Show me Google earnings",
            "Help with portfolio analysis"
        ]
        
        total_time = 0
        iterations = 10
        
        for query in test_queries:
            start_time = time.time()
            
            for _ in range(iterations):
                await analyze_user_intent(query, "perf_test_user")
            
            elapsed = time.time() - start_time
            avg_time = elapsed / iterations
            total_time += elapsed
            
            print(f"  Query: {query[:30]}... -> {avg_time:.3f}s avg")
        
        overall_avg = total_time / (len(test_queries) * iterations)
        print(f"\n📊 Performance: {overall_avg:.3f}s average per query")
        
        if overall_avg < 0.5:
            print("  ✅ Performance is excellent (< 0.5s)")
        elif overall_avg < 1.0:
            print("  ✅ Performance is good (< 1.0s)")
        else:
            print("  ⚠️ Performance could be improved (> 1.0s)")

async def main():
    """Run the intent detection test suite"""
    tester = IntentDetectionTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
