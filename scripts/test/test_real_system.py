#!/usr/bin/env python3
"""
Test the REAL enhanced AI analysis system with actual queries
"""

import sys
import os
from datetime import datetime

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_real_ai_system():
    """Test the actual enhanced AI analysis system"""
    print("🔍 Testing REAL Enhanced AI Analysis System")
    print("=" * 60)
    
    try:
        from src.bot.pipeline.commands.ask.stages.enhanced_ai_analysis import (
            EnhancedAIControlledAnalyzer,
            LockedTechnicalData
        )
        
        # Create real test data
        locked_data = LockedTechnicalData(
            symbol='NVDA',
            current_price=177.82,
            change_percent=0.37,
            volume=45000000,
            timestamp=datetime.now(),
            locked_indicators={
                'support_levels': [165.00, 160.50],
                'resistance_levels': [185.00, 190.50],
                'rsi': 45.2,
                'macd': {'macd': 2.1, 'signal': 1.8, 'histogram': 0.3},
                'sma_20': 175.50,
                'sma_50': 170.25,
                'bollinger_bands': {'upper': 190.00, 'middle': 175.50, 'lower': 161.00}
            },
            data_quality={
                'quality_score': 85,
                'data_points': 50,
                'issues': []
            },
            available_analyses=[
                'price_analysis',
                'rsi_analysis',
                'macd_analysis',
                'support_resistance_analysis',
                'trend_analysis',
                'momentum_analysis',
                'bollinger_bands_analysis'
            ]
        )
        
        analyzer = EnhancedAIControlledAnalyzer()
        
        # Test real queries
        test_queries = [
            "What are the key support and resistance levels for NVDA?",
            "Is NVDA oversold or overbought based on RSI?",
            "What does the MACD indicator tell us about momentum?",
            "Should I buy or sell NVDA right now?",
            "What are the risks with NVDA at current levels?"
        ]
        
        print("🧪 Testing Real AI Analysis...")
        print("=" * 60)
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n📝 Query {i}: {query}")
            print("-" * 50)
            
            try:
                # This should call the actual AI analysis
                result = analyzer.analyze(locked_data, query)
                
                print(f"📊 Analysis Result:")
                print(f"  Type: {type(result)}")
                print(f"  Content: {str(result)[:200]}...")
                
                # Check if result has expected structure
                if hasattr(result, 'ai_response'):
                    print(f"  AI Response: {result.ai_response[:200]}...")
                if hasattr(result, 'confidence'):
                    print(f"  Confidence: {result.confidence}")
                if hasattr(result, 'is_valid'):
                    print(f"  Valid: {result.is_valid}")
                
            except Exception as e:
                print(f"❌ Error analyzing query: {e}")
                import traceback
                traceback.print_exc()
        
        print("\n✅ Real System Test Completed")
        return True
        
    except Exception as e:
        print(f"❌ Real System Test Failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_actual_pipeline():
    """Test the actual ask pipeline"""
    print("\n🔍 Testing Actual Ask Pipeline")
    print("=" * 60)
    
    try:
        # Try to import and test the actual ask command
        from src.bot.pipeline.commands.ask.ask_command import AskCommand
        
        ask_command = AskCommand()
        
        # Test with a real query
        test_query = "What are the key levels for NVDA?"
        
        print(f"🧪 Testing Ask Command with: {test_query}")
        print("-" * 50)
        
        # This should trigger the actual pipeline
        result = ask_command.execute(test_query)
        
        print(f"📊 Pipeline Result:")
        print(f"  Type: {type(result)}")
        print(f"  Content: {str(result)[:300]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Pipeline Test Failed: {e}")
        print("This might be expected if the pipeline needs specific setup")
        return False

def main():
    """Run the real system tests"""
    print("🚀 Testing REAL Enhanced AI Analysis System")
    print("=" * 80)
    
    tests = [
        test_real_ai_system,
        test_actual_pipeline
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 80)
    print(f"📊 Real System Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 Real system tests passed!")
    else:
        print("❌ Some real system tests failed - this shows us what's wrong!")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
