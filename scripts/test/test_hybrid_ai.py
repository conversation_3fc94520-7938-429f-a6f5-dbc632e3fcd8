#!/usr/bin/env python3
"""
Test the hybrid AI approach - enhanced for specific symbols, legacy for general queries
"""

import asyncio
import sys
sys.path.insert(0, '/home/<USER>/Desktop/tradingview-automatio')

async def test_hybrid_ai():
    """Test both enhanced and legacy modes"""
    
    from src.shared.ai_services.unified_ai_processor import UnifiedAIProcessor as AIChatProcessorWrapper
    
    processor = AIChatProcessorWrapper()
    
    print("🧪 Testing Hybrid AI Approach\n")
    
    # Test 1: General market query (should use legacy)
    print("1️⃣ Testing general market query:")
    print("Query: 'find me a daytrade for today'")
    result1 = await processor.process_query("find me a daytrade for today")
    print(f"Status: {result1['status']}")
    print(f"Intent: {result1['intent']}")
    print(f"Response preview: {result1['response'][:150]}...")
    print()
    
    # Test 2: Specific symbol query (should use enhanced)
    print("2️⃣ Testing specific symbol query:")
    print("Query: 'What is the technical analysis for $NVDA?'")
    result2 = await processor.process_query("What is the technical analysis for $NVDA?")
    print(f"Status: {result2['status']}")
    print(f"Intent: {result2['intent']}")
    print(f"Response preview: {result2['response'][:150]}...")
    print()
    
    # Test 3: Non-market query (should ask for symbols)
    print("3️⃣ Testing non-market query:")
    print("Query: 'What is the weather today?'")
    result3 = await processor.process_query("What is the weather today?")
    print(f"Status: {result3['status']}")
    print(f"Intent: {result3['intent']}")
    print(f"Response: {result3['response']}")

if __name__ == "__main__":
    asyncio.run(test_hybrid_ai())
