#!/usr/bin/env python3
"""
Simple test to verify the pipeline timing fix
"""

import time

def test_timing_fix():
    """Test that the timing fix is working"""
    print("🧪 Testing pipeline timing fix...")
    
    # Read the fixed pipeline grader file
    with open('src/shared/monitoring/pipeline_grader.py', 'r') as f:
        content = f.read()
    
    # Check that the fix was applied
    if 'self.start_time = time.time()' in content:
        print("✅ Pipeline timing fix applied correctly")
    else:
        print("❌ Pipeline timing fix not applied")
        return False
    
    # Check that execution time calculation is fixed
    if 'execution_time = end_time - self.start_time' in content:
        print("✅ Execution time calculation fix applied correctly")
    else:
        print("❌ Execution time calculation fix not applied")
        return False
    
    # Check that datetime.now() was replaced
    if 'datetime.now()' not in content or 'end_time=time.time()' in content:
        print("✅ datetime.now() replacement applied correctly")
    else:
        print("❌ datetime.now() replacement not applied")
        return False
    
    return True

def test_discord_fix():
    """Test that the Discord interaction fix was applied"""
    print("�� Testing Discord interaction fix...")
    
    # Read the fixed client.py file
    with open('src/bot/client.py', 'r') as f:
        content = f.read()
    
    # Check that the fix was applied
    if 'already_deferred = interaction.response.is_done()' in content:
        print("✅ Discord interaction fix applied correctly")
    else:
        print("❌ Discord interaction fix not applied")
        return False
    
    # Check that error handling was improved
    if 'try to send error response if interaction not already responded' in content:
        print("✅ Discord error handling fix applied correctly")
    else:
        print("❌ Discord error handling fix not applied")
        return False
    
    return True

def main():
    """Run all tests"""
    print("🚀 Running simple fix verification tests...\n")
    
    tests = [
        test_timing_fix,
        test_discord_fix
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}\n")
    
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All fixes applied successfully!")
    else:
        print("⚠️ Some fixes may not have been applied correctly.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
