#!/usr/bin/env python3
"""
Test the hallucinated response you provided against our validation system
"""

import sys
import os
from datetime import datetime

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_hallucinated_response():
    """Test the actual hallucinated response you provided"""
    print("🔍 Testing Your Hallucinated Response")
    print("=" * 60)
    
    try:
        from src.bot.pipeline.commands.ask.stages.enhanced_ai_analysis import AIResponseValidator
        
        # Create mock locked data (minimal for this test)
        locked_data = type('MockData', (), {
            'current_price': 177.82,  # NVDA current price
            'locked_indicators': {
                'rsi': 45.2,  # Actual RSI from our data
                'volume': 45000000  # Actual volume
            }
        })()
        
        validator = AIResponseValidator()
        
        # The actual hallucinated response you provided
        hallucinated_response = """I need to scan for high-momentum day-trade setups. Let me pull the most volatile names with unusual volume in the first hour:

🔥 Top 3 Day-Trade Candidates right now:

1. NVDA – gapped +3.2 % on 2.1× normal volume; 5-min RSI 72 but holding VWAP; next catalyst is 10:30 am supplier call; tight 1.2 % ATR for quick scalps.

2. SMCI – upgraded at 08:45, front-month 30 % OI in 800 calls; stock grinding through pre-market high $795; micro-pullback to $792 gives 1:3 risk/reward long for $800 breakout.

3. PLTR – government contract headline at 09:15, 3.8× UVOL, daily range already 68 % fulfilled; 15-min chart showing flag at $24.20; break above $24.30 targets $24.70 with stop $24.05.

Quick checklist for each:
- Set 1 % hard stop, 2–3 % target.
- Use 1-min 9/20 EMA cross for entry timing.
- Be flat before 11:00 am if momentum fades.

Need deeper stats on any of these?

*This information is for educational purposes only and does not constitute financial advice. Always do your own research and consult with a financial advisor before making investment decisions.*"""
        
        print("🧪 Testing Hallucinated Response...")
        print("-" * 50)
        
        is_valid, issues = validator.validate_response(hallucinated_response, locked_data)
        
        print(f"✅ Valid: {is_valid}")
        print(f"📊 Issues Found: {len(issues)}")
        
        if issues:
            print("\n⚠️ Validation Issues:")
            for i, issue in enumerate(issues, 1):
                print(f"  {i}. {issue}")
        
        print(f"\n📄 Response Analysis:")
        print(f"  Length: {len(hallucinated_response)} characters")
        print(f"  Contains specific prices: {'$795' in hallucinated_response}")
        print(f"  Contains RSI values: {'RSI 72' in hallucinated_response}")
        print(f"  Contains predictions: {'targets' in hallucinated_response}")
        print(f"  Contains fabricated data: {'2.1× normal volume' in hallucinated_response}")
        
        print(f"\n🎯 Professional Assessment:")
        if is_valid:
            print("  ❌ VALIDATION FAILED - This response should be rejected!")
        else:
            print("  ✅ VALIDATION WORKING - Correctly caught hallucination!")
        
        # Calculate grade based on issues
        if len(issues) >= 8:
            grade = "F"
        elif len(issues) >= 6:
            grade = "D"
        elif len(issues) >= 4:
            grade = "C"
        elif len(issues) >= 2:
            grade = "B"
        else:
            grade = "A"
        
        print(f"  📊 Professional Grade: {grade}")
        
        print(f"\n🚨 Critical Issues Identified:")
        print(f"  1. ❌ Fabricated RSI: Claims 'RSI 72' when actual is 45.2")
        print(f"  2. ❌ Fabricated volume: Claims '2.1× normal volume' without data")
        print(f"  3. ❌ Fabricated prices: $795, $792, $800, $24.20, $24.30, $24.70")
        print(f"  4. ❌ Fabricated events: 'upgraded at 08:45', 'supplier call at 10:30'")
        print(f"  5. ❌ Predictions: 'targets $24.70', 'break above $24.30'")
        print(f"  6. ❌ Specific trading advice: 'Set 1% hard stop', 'Use 1-min EMA'")
        
        print(f"\n✅ Our Validation System Status:")
        if not is_valid:
            print("  ✅ CORRECTLY REJECTED this hallucinated response")
            print("  ✅ Would prevent this from being sent to users")
            print("  ✅ System is working as intended")
        else:
            print("  ❌ INCORRECTLY ACCEPTED this hallucinated response")
            print("  ❌ System needs further improvement")
        
        return not is_valid  # Return True if correctly rejected
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the hallucination test"""
    print("🚀 Testing Hallucinated Response Detection")
    print("=" * 80)
    
    success = test_hallucinated_response()
    
    if success:
        print("\n🎉 Validation system correctly caught the hallucination!")
        print("✅ The Enhanced Hybrid Approach would prevent this response from being sent")
        print("🚀 System is working as intended - ready for production!")
    else:
        print("\n❌ Validation system failed to catch the hallucination")
        print("⚠️ Further improvements needed")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
