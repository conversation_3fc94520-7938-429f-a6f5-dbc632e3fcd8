#!/usr/bin/env python3
"""
Comprehensive test for the enhanced trading analysis engine.

Tests the complete integration of:
- Enhanced sentiment analysis
- Enhanced risk assessment
- ML-powered recommendations
- Market context analysis
"""

import asyncio
import sys
import os
import logging
from datetime import datetime, timedelta

# Add src to path
sys.path.append('src')

from src.analysis.ai.recommendation_engine import AIRecommendationEngine
from src.analysis.sentiment.enhanced_sentiment_analyzer import enhanced_sentiment_analyzer
from src.analysis.risk.enhanced_risk_assessment import enhanced_risk_assessment
from src.analysis.ai.ml_models import trading_ml_model
from src.data.models.stock_data import AnalysisResult, StockQuote, TechnicalIndicators, FundamentalMetrics, RiskAssessment, HistoricalData, MarketContext

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def create_mock_analysis_result(symbol: str) -> AnalysisResult:
    """Create a mock analysis result for testing"""

    # Mock quote data
    quote = StockQuote(
        symbol=symbol,
        price=150.0,
        volume=1000000,
        timestamp=datetime.now(),
        change=2.5,
        change_percent=1.69,
        open=149.0,
        high=152.0,
        low=148.0,
        close=150.0,
        market_cap=2500000000,
        pe_ratio=25.5,
        eps=6.0
    )

    # Mock technical indicators
    technical = TechnicalIndicators(
        rsi=65.5,
        macd={'macd': 1.2, 'signal': 0.8, 'histogram': 0.4},
        sma_50=145.0,
        sma_200=140.0,
        ema_12=151.0,
        ema_26=147.0,
        bollinger_bands={'upper': 155.0, 'middle': 150.0, 'lower': 145.0},
        support_level=145.0,
        resistance_level=155.0,
        volume_sma=950000
    )

    # Mock fundamental metrics
    fundamental = FundamentalMetrics(
        pe_ratio=25.5,
        eps=6.0,
        revenue_growth=0.12,
        profit_margin=0.22,
        debt_to_equity=0.45,
        return_on_equity=0.18,
        price_to_book=3.2,
        peg_ratio=1.8,
        dividend_yield=0.025,
        free_cash_flow=15000000000
    )

    # Mock risk assessment
    risk = RiskAssessment(
        volatility=0.25,
        beta=1.15,
        risk_warnings=["Moderate volatility"],
        stop_loss_recommendation=135.0,
        risk_level="MEDIUM"
    )

    # Mock historical data
    historical = HistoricalData(
        symbol=symbol,
        dates=[datetime.now() - timedelta(days=i) for i in range(4, -1, -1)],
        prices=[140, 142, 145, 148, 150],  # 5 days of prices
        volumes=[900000, 950000, 1100000, 980000, 1000000],
        highs=[141, 144, 147, 149, 152],
        lows=[139, 141, 144, 147, 149],
        opens=[140, 142, 145, 148, 150],
        closes=[140, 142, 145, 148, 150]
    )

    # Mock market context
    market_context = MarketContext(
        sector="Technology",
        industry="Software",
        market_trend="BULLISH",
        sector_performance=0.08,
        market_sentiment="POSITIVE"
    )

    return AnalysisResult(
        symbol=symbol,
        timestamp=datetime.now(),
        quote=quote,
        historical=historical,
        technical=technical,
        fundamental=fundamental,
        risk=risk,
        market_context=market_context,
        recommendation="HOLD",  # Default recommendation
        confidence=75,
        time_horizon="MEDIUM",
        key_insights=["Strong technical indicators", "Moderate risk profile"],
        summary=f"Analysis for {symbol} shows balanced risk-reward profile",
        data_sources=["yfinance", "enhanced_analysis"],
        analysis_quality="HIGH",
        confidence_factors={"technical": 80, "fundamental": 70, "risk": 75}
    )


async def test_enhanced_sentiment_analysis():
    """Test enhanced sentiment analysis"""
    print("\n🧪 Testing Enhanced Sentiment Analysis...")
    
    try:
        # Test comprehensive sentiment analysis
        sentiment_result = await enhanced_sentiment_analyzer.analyze_comprehensive_sentiment(
            symbol="AAPL",
            include_sources=['news', 'social', 'market', 'technical']
        )
        
        print(f"✅ Overall Sentiment: {sentiment_result.sentiment_label} ({sentiment_result.overall_score:.2f})")
        print(f"   Confidence: {sentiment_result.overall_confidence:.2f}")
        print(f"   News Sentiment: {sentiment_result.news_sentiment:.2f}")
        print(f"   Social Sentiment: {sentiment_result.social_sentiment:.2f}")
        print(f"   Market Sentiment: {sentiment_result.market_sentiment:.2f}")
        print(f"   Technical Sentiment: {sentiment_result.technical_sentiment:.2f}")
        
        # Test individual source analysis
        for source, score in sentiment_result.source_scores.items():
            print(f"   {source.title()}: Score={score.score:.2f}, Confidence={score.confidence:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced sentiment analysis test failed: {e}")
        return False


async def test_enhanced_risk_assessment():
    """Test enhanced risk assessment"""
    print("\n🧪 Testing Enhanced Risk Assessment...")
    
    try:
        # Create mock historical data
        historical_data = {
            'close': [140, 142, 145, 148, 150, 152, 149, 151, 153, 150],
            'volume': [900000, 950000, 1100000, 980000, 1000000, 1050000, 920000, 980000, 1100000, 1000000],
            'high': [141, 144, 147, 149, 152, 154, 151, 153, 155, 152],
            'low': [139, 141, 144, 147, 149, 151, 148, 150, 152, 149],
            'open': [140, 142, 145, 148, 150, 152, 149, 151, 153, 150]
        }
        
        # Test comprehensive risk assessment
        risk_result = await enhanced_risk_assessment.assess_comprehensive_risk(
            symbol="AAPL",
            historical_data=historical_data,
            period="1y"
        )
        
        print(f"✅ Risk Level: {risk_result.risk_level} (Score: {risk_result.risk_score})")
        print(f"   Volatility: {risk_result.volatility:.2%}" if risk_result.volatility else "   Volatility: N/A")
        print(f"   Beta: {risk_result.beta:.2f}" if risk_result.beta else "   Beta: N/A")
        print(f"   Alpha: {risk_result.alpha:.2%}" if risk_result.alpha else "   Alpha: N/A")
        print(f"   Sharpe Ratio: {risk_result.sharpe_ratio:.2f}" if risk_result.sharpe_ratio else "   Sharpe Ratio: N/A")
        print(f"   VaR (95%): {risk_result.var_95:.2%}" if risk_result.var_95 else "   VaR (95%): N/A")
        print(f"   Max Drawdown: {risk_result.max_drawdown:.2%}" if risk_result.max_drawdown else "   Max Drawdown: N/A")
        
        if risk_result.risk_warnings:
            print(f"   Warnings: {', '.join(risk_result.risk_warnings)}")

        if risk_result.risk_level == "UNKNOWN" or (risk_result.risk_warnings and "failed" in ', '.join(risk_result.risk_warnings)):
            print("❌ Risk assessment returned UNKNOWN or failed status.")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced risk assessment test failed: {e}")
        return False


async def test_ml_models():
    """Test ML models"""
    print("\n🧪 Testing ML Models...")

    try:
        # Create mock historical data
        historical_data = {
            'prices': [140, 142, 145, 148, 150],
            'volumes': [900000, 950000, 1100000, 980000, 1000000],
            'highs': [141, 144, 147, 149, 152],
            'lows': [139, 141, 144, 147, 149],
            'opens': [140, 142, 145, 148, 150]
        }

        # Create mock technical indicators
        technical_indicators = {
            'rsi': 65.5,
            'macd': {'macd': 1.2, 'signal': 0.8, 'histogram': 0.4},
            'sma_50': 145.0,
            'sma_200': 140.0,
            'bollinger_bands': {'upper': 155.0, 'middle': 150.0, 'lower': 145.0},
            'volume_sma': 950000
        }

        # Test ML prediction
        prediction = trading_ml_model.predict(historical_data, technical_indicators)
        
        if prediction:
            print(f"✅ ML Prediction: {prediction.direction} (Confidence: {prediction.confidence:.2f})")
            print(f"   Predicted Price: ${prediction.predicted_price:.2f}")
            print(f"   Probability: {prediction.probability:.2%}")
            print(f"   Model Used: {prediction.model_used}")
            if prediction.predicted_price == 0 or prediction.model_used == "heuristic_fallback":
                print("❌ ML model prediction is invalid or used a fallback.")
                return False
        else:
            print("⚠️  ML prediction returned None (using fallback)")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ ML models test failed: {e}")
        return False


async def test_integrated_recommendation_engine():
    """Test the complete integrated recommendation engine"""
    print("\n🧪 Testing Integrated Recommendation Engine...")
    
    try:
        # Create recommendation engine
        engine = AIRecommendationEngine()
        
        # Test with multiple symbols
        test_symbols = ["AAPL", "MSFT", "GOOGL"]
        
        for symbol in test_symbols:
            print(f"\n📊 Testing {symbol}...")
            
            # Create mock analysis
            analysis = create_mock_analysis_result(symbol)
            
            # Generate recommendation
            recommendation = await engine.generate_recommendation(analysis)
            
            print(f"   Action: {recommendation.action}")
            print(f"   Confidence: {recommendation.confidence}%")
            print(f"   Time Horizon: {recommendation.time_horizon}")

            if hasattr(recommendation, 'target_price') and recommendation.target_price:
                print(f"   Target Price: ${recommendation.target_price:.2f}")

            if hasattr(recommendation, 'stop_loss') and recommendation.stop_loss:
                print(f"   Stop Loss: ${recommendation.stop_loss:.2f}")

            print(f"   Reasoning:")
            for reason in recommendation.reasoning[:3]:  # Show first 3 reasons
                print(f"     • {reason}")

            ml_reasoning_found = any("🤖" in reason for reason in recommendation.reasoning)
            print(f"DEBUG: ml_reasoning_found = {ml_reasoning_found}")
            if not ml_reasoning_found:
                print(f"❌ Recommendation for {symbol} did not include ML-based reasoning, indicating a fallback.")
                print("DEBUG: Returning False")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Integrated recommendation engine test failed: {e}")
        return False


async def test_performance_benchmarks():
    """Test performance benchmarks"""
    print("\n🧪 Testing Performance Benchmarks...")
    
    try:
        import time
        
        # Test sentiment analysis performance
        start_time = time.time()
        await enhanced_sentiment_analyzer.analyze_comprehensive_sentiment("AAPL")
        sentiment_time = time.time() - start_time
        
        # Test risk assessment performance
        start_time = time.time()
        await enhanced_risk_assessment.assess_comprehensive_risk("AAPL", {'close': [150, 151, 152]})
        risk_time = time.time() - start_time
        
        # Test ML prediction performance
        start_time = time.time()
        trading_ml_model.predict(
            {'prices': [150, 151, 152]},
            {'rsi': 65, 'macd': {'macd': 1.2, 'signal': 0.8, 'histogram': 0.4}}
        )
        ml_time = time.time() - start_time
        
        # Test full recommendation performance
        start_time = time.time()
        engine = AIRecommendationEngine()
        analysis = create_mock_analysis_result("AAPL")
        await engine.generate_recommendation(analysis)
        full_time = time.time() - start_time
        
        print(f"✅ Performance Benchmarks:")
        print(f"   Sentiment Analysis: {sentiment_time:.3f}s")
        print(f"   Risk Assessment: {risk_time:.3f}s")
        print(f"   ML Prediction: {ml_time:.3f}s")
        print(f"   Full Recommendation: {full_time:.3f}s")
        
        # Performance thresholds
        if full_time < 5.0:
            print(f"   🚀 Excellent performance (< 5s)")
        elif full_time < 10.0:
            print(f"   ✅ Good performance (< 10s)")
        else:
            print(f"   ⚠️  Slow performance (> 10s)")
        
        return True
        
    except Exception as e:
        print(f"❌ Performance benchmark test failed: {e}")
        return False


async def main():
    """Run all tests"""
    print("🚀 Starting Enhanced Trading Analysis Engine Tests")
    print("=" * 60)
    
    tests = [
        ("Enhanced Sentiment Analysis", test_enhanced_sentiment_analysis),
        ("Enhanced Risk Assessment", test_enhanced_risk_assessment),
        ("ML Models", test_ml_models),
        ("Integrated Recommendation Engine", test_integrated_recommendation_engine),
        ("Performance Benchmarks", test_performance_benchmarks)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! Enhanced Trading Analysis Engine is ready!")
    else:
        print("⚠️  Some tests failed. Please review the issues above.")
    
    # Cleanup
    try:
        await enhanced_sentiment_analyzer.close()
    except:
        pass


if __name__ == "__main__":
    asyncio.run(main())
