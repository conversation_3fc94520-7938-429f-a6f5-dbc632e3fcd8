"""
Test script demonstrating AI-based symbol extraction vs. primitive regex
"""

import asyncio
import re
from typing import List, Tuple

# Import our new AI-based symbol extractor
from src.bot.pipeline.commands.ask.stages.ai_symbol_extractor import extract_symbols_intelligently, AISymbolResult

def primitive_regex_extraction(query: str) -> List[Tuple[str, str]]:
    """
    Primitive regex-based symbol extraction (traditional approach)
    """
    results = []
    
    # 1. Extract $ prefixed symbols
    dollar_matches = re.findall(r'\$([A-Z]{1,5})\b', query)
    for symbol in dollar_matches:
        results.append((symbol, "dollar_prefixed"))
    
    # 2. Extract capitalized words that might be tickers
    capitalized_words = re.findall(r'\b([A-Z]{2,5})\b', query)
    for word in capitalized_words:
        # Filter out common non-tickers
        non_tickers = {'THE', 'AND', 'FOR', 'YOU', 'ARE', 'USD', 'CAD', 'EUR', 'GBP', 'JPY'}
        if word not in non_tickers:
            results.append((word, "capitalized_word"))
    
    # Remove duplicates
    seen = set()
    unique_results = []
    for symbol, context in results:
        if symbol not in seen:
            unique_results.append((symbol, context))
            seen.add(symbol)
    
    return unique_results

async def compare_extraction_methods():
    """
    Compare AI-based extraction with primitive regex
    """
    test_queries = [
        "What is the price of $AAPL stock?",
        "I'm thinking about buying Tesla or Microsoft shares",
        "$TSLA vs $NVDA - which is better for long term?",
        "The USD currency is strong today",
        "What do you think about the THE stock?",
        "I need help with my portfolio",
        "Google and Amazon are tech giants",
        "$GOOGL is trading at a high valuation",
        "Should I buy $MSFT or $AAPL for dividends?",
        "The market is volatile today"
    ]
    
    print("🔍 Comparing Symbol Extraction Methods")
    print("=" * 60)
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n🧪 Test {i}: '{query}'")
        print("-" * 40)
        
        # Primitive regex approach
        regex_results = primitive_regex_extraction(query)
        print(f"📝 Primitive Regex ({len(regex_results)} results):")
        for symbol, context in regex_results:
            print(f"   • {symbol} (context: {context})")
        
        # AI-based approach
        print("🧠 AI-Based Extraction:")
        try:
            ai_results = await extract_symbols_intelligently(query)
            print(f"   Found {len(ai_results)} symbols:")
            for result in ai_results:
                print(f"   • {result.text} (confidence: {result.confidence:.2f}, context: {result.context})")
                if result.alternatives:
                    print(f"     Alternatives: {', '.join(result.alternatives)}")
        except Exception as e:
            print(f"   ❌ Error: {e}")

if __name__ == "__main__":
    print("🚀 Starting Symbol Extraction Comparison Test...")
    asyncio.run(compare_extraction_methods())
    print("\n✅ Test completed!")