#!/usr/bin/env python3
"""
Test script to verify QueryResult formatting fix
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.shared.ai_services.unified_ai_processor import ProcessingResult as QueryResult

def test_query_result_formatting():
    """Test that QueryResult objects are properly formatted"""
    
    # Create a sample QueryResult like the one in the user's example
    query_result = QueryResult(
        intent='stock_analysis',
        symbols=['GME'],
        needs_data=True,
        response='## GME Analysis\n# Technical Analysis: GME\n*Analysis generated at 2025-09-19 13:14:19*\n\n## Current Market Data\n**Price:** $25.88\n**Change:** 📈 +0.00%\n**Volume:** 7,806,246.0\n\n## Technical Indicators\n**Rsi:** 60.92\n**Macd:** macd: 0.94, signal: 0.85, histogram: 0.09\n**Sma 20:** $26.41\n**Sma 50:** $24.97\n**Support Levels:** $24.97, $25.52\n**Resistance Levels:** $27.72, $28.20\n**Bollinger Upper:** $27.88\n**Bollinger Lower:** $24.94\n**Volume Avg:** 7806246.00\n\n## Analysis\n### Price Analysis\nThe stock is trading flat from the previous close.\n\n### Trend Analysis\nThe 20-day SMA ($26.41) is bullish relative to the 50-day SMA ($24.97).\nCurrent price is below the 20-day moving average.\n\n### Momentum Analysis\nRSI is 60.9, indicating the stock is neutral and showing balanced momentum.\n\n### Support & Resistance Analysis\nKey support level identified at $25.52.\nKey resistance level identified at $27.72.\n\n## Analysis Metadata\n**Data Quality:** High\n**Analyses Performed:** Price Analysis, Trend Analysis, Momentum Analysis, Support Resistance Analysis\n\n*This analysis is based on calculated technical indicators. Past performance does not guarantee future results.*',
        confidence=0.95,
        data={'GME': {'symbol': 'GME', 'current_price': 25.88}}
    )
    
    print("🔍 Testing QueryResult formatting...")
    print("=" * 50)
    
    # Test the old way (what was happening before the fix)
    print("❌ OLD WAY (broken):")
    print(str(query_result))
    print()
    
    # Test the new way (what should happen after the fix)
    print("✅ NEW WAY (fixed):")
    if hasattr(query_result, 'response'):
        print(query_result.response)
    else:
        print(str(query_result))
    print()
    
    print("🎯 The fix ensures that only the 'response' field is used for Discord output,")
    print("   not the entire QueryResult object representation.")

if __name__ == "__main__":
    test_query_result_formatting()
