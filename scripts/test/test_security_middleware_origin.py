#!/usr/bin/env python3
"""
Comprehensive tests for SecurityMiddleware origin/referer validation.

This test suite covers all paths in the _validate_origin() method:
1. Valid/invalid Origin header validation
2. Valid/invalid Referer header validation (when no Origin)
3. Wildcard "*" allowed_origins behavior (bypass validation)
4. No Origin/Referer headers behavior (allowed by default)
5. Origin precedence over Referer when both are present

These run in-process (no external server needed) using FastAPI + httpx AsyncClient.
Addresses the security testing gap identified for CSRF protection validation.
"""

import asyncio
import os
import sys
from typing import <PERSON>ple

from fastapi import FastAPI
from fastapi.responses import JSONResponse
import httpx

# Ensure src is importable
ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if ROOT not in sys.path:
    sys.path.append(ROOT)

from src.security.middleware import SecurityMiddleware  # noqa: E402


def build_app(allowed_origins) -> FastAPI:
    app = FastAPI()

    # Add our SecurityMiddleware with a specific allowed_origins config
    app.add_middleware(
        SecurityMiddleware,
        rate_limit=100,
        window=60,
        max_content_length=1024 * 1024,
        webhook_secret=None,
        allowed_origins=allowed_origins,
        exempt_routes=['/health']
    )

    @app.get('/health')
    async def health():
        return {'status': 'ok'}

    @app.get('/echo')
    async def echo():
        return JSONResponse({'ok': True})

    return app


async def run_case(app: FastAPI, headers: dict) -> Tuple[int, str]:
    transport = httpx.ASGITransport(app=app)
    async with httpx.AsyncClient(transport=transport, base_url='http://test') as client:
        r = await client.get('/echo', headers=headers)
        return r.status_code, r.text


async def test_valid_origin():
    app = build_app(allowed_origins=['good.com'])
    status, body = await run_case(app, headers={'Origin': 'https://good.com'})
    print(f"Valid Origin -> status={status} body={body[:80]}")
    return status == 200


async def test_invalid_origin():
    app = build_app(allowed_origins=['good.com'])
    status, body = await run_case(app, headers={'Origin': 'https://evil.com'})
    print(f"Invalid Origin -> status={status} body={body[:80]}")
    return status == 403


async def test_valid_referer_without_origin():
    app = build_app(allowed_origins=['good.com'])
    status, body = await run_case(app, headers={'Referer': 'https://good.com/some/page'})
    print(f"Valid Referer (no Origin) -> status={status} body={body[:80]}")
    return status == 200


async def test_wildcard_allowed_origins():
    """Test that wildcard '*' in allowed_origins bypasses origin validation."""
    app = build_app(allowed_origins=['*'])

    # Test with any origin - should be allowed
    status1, body1 = await run_case(app, headers={'Origin': 'https://evil.com'})
    print(f"Wildcard Origins (evil.com) -> status={status1} body={body1[:80]}")

    # Test with no origin/referer - should be allowed
    status2, body2 = await run_case(app, headers={})
    print(f"Wildcard Origins (no headers) -> status={status2} body={body2[:80]}")

    # Both should pass with wildcard
    return status1 == 200 and status2 == 200


async def test_no_origin_or_referer_headers():
    """Test that requests without Origin or Referer headers are allowed."""
    app = build_app(allowed_origins=['good.com'])
    status, body = await run_case(app, headers={})
    print(f"No Origin/Referer headers -> status={status} body={body[:80]}")
    return status == 200


async def test_invalid_referer_without_origin():
    """Test that invalid Referer (when no Origin) is blocked."""
    app = build_app(allowed_origins=['good.com'])
    status, body = await run_case(app, headers={'Referer': 'https://evil.com/some/page'})
    print(f"Invalid Referer (no Origin) -> status={status} body={body[:80]}")
    return status == 403


async def test_origin_takes_precedence_over_referer():
    """Test that Origin header takes precedence over Referer when both are present."""
    app = build_app(allowed_origins=['good.com'])

    # Valid Origin, invalid Referer - should pass (Origin wins)
    status1, body1 = await run_case(app, headers={
        'Origin': 'https://good.com',
        'Referer': 'https://evil.com/page'
    })
    print(f"Valid Origin + Invalid Referer -> status={status1} body={body1[:80]}")

    # Invalid Origin, valid Referer - should fail (Origin wins)
    status2, body2 = await run_case(app, headers={
        'Origin': 'https://evil.com',
        'Referer': 'https://good.com/page'
    })
    print(f"Invalid Origin + Valid Referer -> status={status2} body={body2[:80]}")

    return status1 == 200 and status2 == 403


async def main():
    print('🔒 SecurityMiddleware Origin/Referer validation tests (in-process)')
    print('=' * 70)

    tests = [
        # Basic origin validation
        ("Valid Origin", test_valid_origin),
        ("Invalid Origin", test_invalid_origin),

        # Referer validation (when no Origin)
        ("Valid Referer (no Origin)", test_valid_referer_without_origin),
        ("Invalid Referer (no Origin)", test_invalid_referer_without_origin),

        # Wildcard behavior
        ("Wildcard Allowed Origins", test_wildcard_allowed_origins),

        # No headers behavior
        ("No Origin/Referer Headers", test_no_origin_or_referer_headers),

        # Precedence testing
        ("Origin Precedence Over Referer", test_origin_takes_precedence_over_referer),
    ]

    passed = 0
    print(f"\n🧪 Running {len(tests)} comprehensive origin validation tests...\n")

    for name, fn in tests:
        try:
            ok = await fn()
            status = '✅ PASS' if ok else '❌ FAIL'
            print(f"   {status} {name}")
            if ok:
                passed += 1
        except Exception as e:
            print(f"   ❌ FAIL {name} (exception): {e}")
        print()  # Add spacing between tests

    print('=' * 70)
    print(f"📊 Test Results Summary:")
    print(f"   Passed: {passed}/{len(tests)} ({passed/len(tests)*100:.1f}%)")

    if passed == len(tests):
        print("🎉 All origin validation tests passed!")
        print("✅ SecurityMiddleware origin/referer validation is working correctly")
    else:
        print("⚠️  Some tests failed - check SecurityMiddleware implementation")

    print(f"\n🎯 Overall: {passed}/{len(tests)} passed")


if __name__ == '__main__':
    asyncio.run(main())

