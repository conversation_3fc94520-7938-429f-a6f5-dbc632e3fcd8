#!/usr/bin/env python3
"""
Test script for Codebase Explorer components.

This script tests the integration between all Codebase Explorer components
by simulating pipeline events and verifying that they can be received.
"""

import asyncio
import json
import websockets
import aiohttp
import time
from datetime import datetime

async def test_configuration_api():
    """Test the configuration API."""
    print("Testing Configuration API...")
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get('http://localhost:8001/api/system/config') as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✓ Configuration API working. Found {data['total_items']} items ({data['sensitive_items_masked']} masked)")
                    return True
                else:
                    print(f"✗ Configuration API returned status {response.status}")
                    return False
    except Exception as e:
        print(f"✗ Configuration API test failed: {e}")
        return False

async def test_system_api():
    """Test the system monitoring API."""
    print("Testing System Monitoring API...")
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get('http://localhost:8003/api/system/health') as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✓ System API working. Status: {data['status']}")
                    return True
                else:
                    print(f"✗ System API returned status {response.status}")
                    return False
    except Exception as e:
        print(f"✗ System API test failed: {e}")
        return False

async def test_websocket_events():
    """Test the WebSocket events system."""
    print("Testing WebSocket Events...")
    try:
        uri = "ws://localhost:8002/ws"
        async with websockets.connect(uri) as websocket:
            # Subscribe to a test pipeline
            subscribe_msg = {
                "type": "subscribe",
                "correlation_id": "test_12345"
            }
            await websocket.send(json.dumps(subscribe_msg))
            
            # Wait for subscription confirmation
            response = await websocket.recv()
            data = json.loads(response)
            if data.get("type") == "subscribed":
                print("✓ WebSocket connection established and subscribed")
                
                # Test receiving an event (this would normally come from the pipeline)
                # For demo purposes, we'll simulate sending an event via the API
                try:
                    async with aiohttp.ClientSession() as session:
                        event_data = {
                            "event_type": "pipeline_started",
                            "pipeline_name": "test_pipeline",
                            "correlation_id": "test_12345",
                            "data": {"test": "data"}
                        }
                        async with session.post('http://localhost:8002/api/events/emit', json=event_data) as response:
                            if response.status == 200:
                                # Wait for the event to be received
                                try:
                                    event_response = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                                    event_data = json.loads(event_response)
                                    if event_data.get("event_type") == "pipeline_started":
                                        print("✓ WebSocket event received successfully")
                                        return True
                                    else:
                                        print("✗ Unexpected event received")
                                        return False
                                except asyncio.TimeoutError:
                                    print("✗ Timeout waiting for WebSocket event")
                                    return False
                            else:
                                print(f"✗ Failed to emit event via API: {response.status}")
                                return False
                except Exception as e:
                    print(f"✗ Failed to emit test event: {e}")
                    return False
            else:
                print("✗ WebSocket subscription failed")
                return False
    except Exception as e:
        print(f"✗ WebSocket test failed: {e}")
        return False

async def main():
    """Run all tests."""
    print("Codebase Explorer Component Tests")
    print("=" * 40)
    
    # Test each component
    tests = [
        test_configuration_api,
        test_system_api,
        test_websocket_events
    ]
    
    results = []
    for test in tests:
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
            results.append(False)
        print()
    
    # Summary
    print("Test Summary")
    print("=" * 40)
    passed = sum(results)
    total = len(results)
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! The Codebase Explorer is working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the output above.")
    
    return passed == total

if __name__ == "__main__":
    asyncio.run(main())