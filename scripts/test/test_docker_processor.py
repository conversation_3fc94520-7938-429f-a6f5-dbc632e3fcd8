i d#!/usr/bin/env python3
"""
Test the robust processor in Docker environment
"""

import asyncio
import sys
sys.path.insert(0, '/home/<USER>/Desktop/tradingview-automatio')

async def test_docker_processor():
    """Test the processor in Docker environment"""
    
    try:
        from src.shared.ai_services.ai_processor_robust import CleanAIProcessor
        
        print("🧪 Testing Robust Processor in Docker Environment")
        print("=" * 60)
        
        processor = CleanAIProcessor()
        
        # Test general questions
        questions = [
            "what time is it",
            "what's the weather like", 
            "I'm looking for trading music",
            "find me a daytrade for today"
        ]
        
        for question in questions:
            print(f"\n❓ Question: {question}")
            result = await processor.process_query(question)
            print(f"✅ Response: {result.response}")
            print(f"📊 Intent: {result.intent}, Confidence: {result.confidence}")
            print("-" * 40)
        
        # Test stock analysis
        print(f"\n📈 Testing Stock Analysis: What is the technical analysis for $NVDA?")
        result = await processor.process_query("What is the technical analysis for $NVDA?")
        print(f"✅ Response Length: {len(result.response)} characters")
        print(f"📊 Intent: {result.intent}, Confidence: {result.confidence}")
        print(f"🔍 Preview: {result.response[:200]}...")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_docker_processor())
