#!/usr/bin/env python3
"""
Test the robust financial processor to ensure it's working correctly
"""

import asyncio
import sys
import os

# Add the project root to the path
sys.path.insert(0, '/home/<USER>/Desktop/tradingview-automatio')

async def test_robust_processor():
    """Test the robust processor with various queries"""
    
    try:
        from src.shared.ai_services.unified_ai_processor import UnifiedAIProcessor as CleanAIProcessor
        
        print("🧪 Testing Robust Financial Processor\n")
        
        # Create the processor
        processor = CleanAIProcessor()
        
        # Test queries
        test_queries = [
            "What is the technical analysis for $NVDA?",
            "Find me a daytrade for today",
            "What's the trend analysis for $AAPL?",
            "How is $TSLA doing?",
            "What is the weather today?"  # Non-market query
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"{i}️⃣ Testing: '{query}'")
            print("-" * 50)
            
            try:
                result = await processor.process_query(query)
                
                print(f"✅ Intent: {result.intent}")
                print(f"✅ Symbols: {result.symbols}")
                print(f"✅ Confidence: {result.confidence}")
                print(f"✅ Response Length: {len(result.response)} characters")
                print(f"✅ Response Preview: {result.response[:150]}...")
                
                # Check for validation features
                if "Technical Analysis:" in result.response:
                    print("✅ Contains structured analysis")
                if "Analysis Metadata" in result.response:
                    print("✅ Contains metadata section")
                if "Data Quality:" in result.response:
                    print("✅ Contains data quality info")
                
                print()
                
            except Exception as e:
                print(f"❌ Error: {e}")
                print()
        
        print("🎯 Testing Complete!")
        
    except Exception as e:
        print(f"❌ Failed to import or initialize processor: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_robust_processor())
