#!/usr/bin/env python3
"""
Fixed Test Suite for AI Intent Detection System with Mocked Dependencies

This script tests the unified intent detection system without external API dependencies.
"""

import asyncio
import sys
import os
import time
from typing import Dict, List, Any
from unittest.mock import AsyncMock, patch, MagicMock

# Add src to path
sys.path.append('src')

from src.shared.error_handling.logging import get_logger
from src.bot.ai.unified_intent_system import (
    unified_intent_detector, analyze_user_intent, get_processing_pipeline,
    UnifiedIntent, ProcessingPipeline
)

logger = get_logger(__name__)

class MockedIntentDetectionTester:
    """Comprehensive tester for AI intent detection system with mocked dependencies"""
    
    def __init__(self):
        self.test_results = []
        self.total_tests = 0
        self.passed_tests = 0
        
    async def run_all_tests(self):
        """Run all intent detection tests with mocked dependencies"""
        logger.info("🚀 Starting Fixed AI Intent Detection Tests")
        
        # Mock external dependencies
        with patch('src.shared.ai_services.query_cache.simple_query_cache.get', new_callable=AsyncMock) as mock_cache_get, \
             patch('src.shared.ai_services.query_cache.simple_query_cache.set', new_callable=AsyncMock) as mock_cache_set, \
             patch('src.shared.ai_services.unified_ai_processor.unified_ai_processor.process', new_callable=AsyncMock) as mock_ai_process:
            
            # Configure mocks
            mock_cache_get.return_value = None  # Cache miss
            mock_cache_set.return_value = True
            mock_ai_process.return_value = {
                'intent': 'price_check',
                'confidence': 0.85,
                'symbols': ['AAPL'],
                'parameters': {'symbol': 'AAPL'},
                'response': 'Mock AI response'
            }
            
            await self.test_basic_intent_detection()
            await self.test_parameter_extraction()
            await self.test_conversation_flow()
            await self.test_performance_metrics()
            
        self.print_summary()
        
    async def test_basic_intent_detection(self):
        """Test basic intent detection functionality"""
        logger.info("📋 Testing Basic Intent Detection")
        
        test_cases = [
            ("What's the price of AAPL?", "price_check", ["AAPL"]),
            ("Analyze TSLA stock", "technical_analysis", ["TSLA"]),
            ("Show me MSFT chart", "chart_request", ["MSFT"]),
            ("Set alert for GOOGL at $150", "alert_setup", ["GOOGL"]),
            ("What's happening in the market?", "market_overview", []),
        ]
        
        for query, expected_intent, expected_symbols in test_cases:
            self.total_tests += 1
            try:
                result = await analyze_user_intent(query, user_id="test_user")
                
                # Validate intent detection
                detected_intent = result.intent_type
                detected_symbols = result.symbols
                
                if detected_intent == expected_intent and set(detected_symbols) == set(expected_symbols):
                    self.passed_tests += 1
                    logger.info(f"✅ Intent detection passed: {query} -> {detected_intent}")
                else:
                    logger.warning(f"❌ Intent detection failed: {query}")
                    logger.warning(f"   Expected: {expected_intent}, {expected_symbols}")
                    logger.warning(f"   Got: {detected_intent}, {detected_symbols}")
                    
            except Exception as e:
                logger.error(f"❌ Intent detection error for '{query}': {e}")
                
    async def test_parameter_extraction(self):
        """Test parameter extraction from user queries"""
        logger.info("📋 Testing Parameter Extraction")
        
        test_cases = [
            ("Set alert for AAPL above $150", {"symbol": "AAPL", "price": 150, "condition": "above"}),
            ("Buy 100 shares of TSLA", {"symbol": "TSLA", "quantity": 100, "action": "buy"}),
            ("Show MSFT 1 hour chart", {"symbol": "MSFT", "timeframe": "1h"}),
            ("Analyze GOOGL for next week", {"symbol": "GOOGL", "period": "1w"}),
        ]
        
        for query, expected_params in test_cases:
            self.total_tests += 1
            try:
                result = await analyze_user_intent(query, user_id="test_user")
                
                # Check if key parameters were extracted
                extracted_params = result.parameters
                
                # Validate at least the symbol was extracted correctly
                if extracted_params.get('symbol') == expected_params.get('symbol'):
                    self.passed_tests += 1
                    logger.info(f"✅ Parameter extraction passed: {query}")
                else:
                    logger.warning(f"❌ Parameter extraction failed: {query}")
                    logger.warning(f"   Expected symbol: {expected_params.get('symbol')}")
                    logger.warning(f"   Got: {extracted_params}")
                    
            except Exception as e:
                logger.error(f"❌ Parameter extraction error for '{query}': {e}")
                
    async def test_conversation_flow(self):
        """Test conversation flow and context awareness"""
        logger.info("📋 Testing Conversation Flow")
        
        conversation_steps = [
            ("What's the price of AAPL?", "price_check"),
            ("How about TSLA?", "price_check"),  # Context should carry over
            ("Analyze it", "technical_analysis"),  # Should refer to TSLA
            ("Set an alert at $200", "alert_setup"),  # Should use TSLA context
        ]
        
        user_id = "conversation_test_user"
        
        for query, expected_intent in conversation_steps:
            self.total_tests += 1
            try:
                result = await analyze_user_intent(query, user_id=user_id)
                
                if result.intent_type == expected_intent:
                    self.passed_tests += 1
                    logger.info(f"✅ Conversation flow passed: {query} -> {expected_intent}")
                else:
                    logger.warning(f"❌ Conversation flow failed: {query}")
                    logger.warning(f"   Expected: {expected_intent}, Got: {result.intent_type}")
                    
            except Exception as e:
                logger.error(f"❌ Conversation flow error for '{query}': {e}")
                
    async def test_performance_metrics(self):
        """Test performance and response times"""
        logger.info("📋 Testing Performance Metrics")
        
        test_queries = [
            "What's AAPL price?",
            "Analyze TSLA",
            "Show MSFT chart",
            "Market overview",
            "Set GOOGL alert"
        ]
        
        total_time = 0
        successful_queries = 0
        
        for query in test_queries:
            self.total_tests += 1
            try:
                start_time = time.time()
                result = await analyze_user_intent(query, user_id="perf_test_user")
                end_time = time.time()
                
                response_time = end_time - start_time
                total_time += response_time
                
                if result and response_time < 5.0:  # Should respond within 5 seconds
                    self.passed_tests += 1
                    successful_queries += 1
                    logger.info(f"✅ Performance test passed: {query} ({response_time:.3f}s)")
                else:
                    logger.warning(f"❌ Performance test failed: {query} ({response_time:.3f}s)")
                    
            except Exception as e:
                logger.error(f"❌ Performance test error for '{query}': {e}")
                
        if successful_queries > 0:
            avg_response_time = total_time / successful_queries
            logger.info(f"📊 Average response time: {avg_response_time:.3f}s")
            
    def print_summary(self):
        """Print test summary"""
        success_rate = (self.passed_tests / self.total_tests * 100) if self.total_tests > 0 else 0
        
        logger.info("=" * 60)
        logger.info("🎯 FIXED AI INTENT DETECTION TEST SUMMARY")
        logger.info("=" * 60)
        logger.info(f"Total Tests: {self.total_tests}")
        logger.info(f"Passed: {self.passed_tests}")
        logger.info(f"Failed: {self.total_tests - self.passed_tests}")
        logger.info(f"Success Rate: {success_rate:.1f}%")
        
        if success_rate >= 80:
            logger.info("🎉 AI Intent Detection System: PRODUCTION READY")
        elif success_rate >= 60:
            logger.info("⚠️ AI Intent Detection System: NEEDS IMPROVEMENT")
        else:
            logger.info("❌ AI Intent Detection System: REQUIRES MAJOR FIXES")
            
        logger.info("=" * 60)

async def main():
    """Main test execution"""
    tester = MockedIntentDetectionTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
