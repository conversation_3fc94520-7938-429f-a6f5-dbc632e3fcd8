#!/usr/bin/env python3
"""
Demo script to show Monte Carlo probability analysis capabilities
"""

import asyncio
import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.analysis.probability.probability_response_service import ProbabilityResponseService

async def demo_monte_carlo_analysis():
    """Demonstrate Monte Carlo analysis capabilities"""
    print("🎲 Monte Carlo Probability Analysis Demo")
    print("=" * 50)
    
    # Create sample data
    symbol = "AAPL"
    current_price = 150.0
    
    # Generate sample historical data
    dates = pd.date_range(end=datetime.now(), periods=100, freq='D')
    np.random.seed(42)  # For reproducible results
    
    # Generate realistic price data with slight upward trend
    returns = np.random.normal(0.0008, 0.02, 100)  # 0.08% daily return, 2% volatility
    prices = [current_price]
    
    for ret in returns:
        prices.append(prices[-1] * (1 + ret))
    
    historical_data = pd.DataFrame({
        'date': dates,
        'close': prices[1:],
        'open': [p * (1 + np.random.normal(0, 0.005)) for p in prices[1:]],
        'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices[1:]],
        'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices[1:]],
        'volume': np.random.randint(1000000, 10000000, 100)
    })
    
    print(f"📊 Analyzing {symbol} at ${current_price:.2f}")
    print(f"📈 Historical data: {len(historical_data)} days")
    print()
    
    # Test different scenarios
    scenarios = [
        {"target_percentage": 5.0, "description": "5% gain in 5 days"},
        {"target_percentage": 10.0, "description": "10% gain in 5 days"},
        {"target_percentage": -5.0, "description": "5% loss in 5 days"},
        {"target_price": 160.0, "description": "Reach $160 in 5 days"},
    ]
    
    service = ProbabilityResponseService()
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"🎯 Scenario {i}: {scenario['description']}")
        print("-" * 40)
        
        try:
            result = await service.analyze_price_probability(
                symbol=symbol,
                current_price=current_price,
                target_price=scenario.get('target_price'),
                target_percentage=scenario.get('target_percentage'),
                time_horizon_days=5,
                historical_data=historical_data
            )
            
            print(f"💰 Target Analysis: {result.get('target_analysis', 'N/A')}")
            print(f"🎲 Probability of reaching target: {result.get('confidence', 'N/A')}")
            print(f"📊 Bullish: {result.get('bullish_probability', 'N/A')}")
            print(f"📉 Bearish: {result.get('bearish_probability', 'N/A')}")
            print(f"➡️ Neutral: {result.get('neutral_probability', 'N/A')}")
            print(f"📈 Expected price: ${result.get('mean_expected_price', 0):.2f}")
            print(f"📊 Volatility forecast: {result.get('volatility_forecast', 0):.1%}")
            print(f"⚠️ Max drawdown risk: {result.get('max_drawdown_probability', 0):.1%}")
            
            # Show key insights
            insights = result.get('key_insights', '')
            if insights:
                print(f"💡 Key Insights:")
                for insight in insights.split('\n'):
                    if insight.strip():
                        print(f"   {insight}")
            
            print()
            
        except Exception as e:
            print(f"❌ Error in scenario {i}: {e}")
            print()
    
    print("🎉 Demo completed!")
    print("\nThis demonstrates how the system can now provide:")
    print("• Monte Carlo simulations for price probability")
    print("• Risk assessment with drawdown analysis")
    print("• Volatility forecasting")
    print("• Technical indicator integration")
    print("• Probabilistic trading recommendations")

if __name__ == "__main__":
    asyncio.run(demo_monte_carlo_analysis())
