#!/usr/bin/env python3
"""
Test script to identify the data fabrication issue
"""

import asyncio
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_data_issue():
    """Test where the data fabrication is happening"""
    
    try:
        from src.shared.ai_services.unified_ai_processor import UnifiedAIProcessor as CleanAIProcessor
        
        # Create processor
        processor = CleanAIProcessor()
        
        # Test the same query that was generating fabricated data
        test_query = "what stock are you extremely bullish on for the next 2 days?"
        
        print("🧪 Testing Data Fabrication Issue")
        print("=" * 50)
        print(f"Query: {test_query}")
        print()
        
        # Process the query and examine the flow
        print("Processing query...")
        result = await processor.process_query(test_query)
        
        print(f"Intent: {result.intent}")
        print(f"Symbols: {result.symbols}")
        print(f"Needs Data: {result.needs_data}")
        print(f"Confidence: {result.confidence}")
        print()
        
        # Check if it's using real data or fabricated data
        if result.data:
            print("✅ Real market data found:")
            for item in result.data:
                symbol = item.get('symbol', 'Unknown')
                data = item.get('data', {})
                price = data.get('current_price', 'N/A')
                print(f"  {symbol}: ${price}")
        else:
            print("❌ No real market data - this explains the fabrication!")
        
        print()
        print("Response (first 500 chars):")
        print("-" * 30)
        print(result.response[:500] + "..." if len(result.response) > 500 else result.response)
        
        return result
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    asyncio.run(test_data_issue())
