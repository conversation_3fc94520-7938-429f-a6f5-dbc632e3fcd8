#!/usr/bin/env python3
"""
Test the core ask pipeline without Discord dependencies
"""

import sys
import os
import asyncio
from datetime import datetime

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

async def test_core_pipeline():
    """Test the core ask pipeline"""
    print("🔍 Testing Core Ask Pipeline")
    print("=" * 60)
    
    try:
        # Import the core pipeline components
        from src.bot.pipeline.commands.ask.stages.data_provider import DataProvider
        from src.bot.pipeline.commands.ask.stages.enhanced_ai_analysis import EnhancedAIControlledAnalyzer
        
        # Test queries
        test_queries = [
            "What are the key support and resistance levels for NVDA?",
            "Is NVDA oversold or overbought based on RSI?",
            "What does the MACD indicator tell us about momentum?",
            "Should I buy or sell NVDA right now?",
            "What are the risks with NVDA at current levels?"
        ]
        
        print("🧪 Testing Core Pipeline Components...")
        print("=" * 60)
        
        # Test data provider
        print("\n📊 Testing Data Provider...")
        data_provider = DataProvider()
        
        try:
            market_data = await data_provider.get_market_data(['NVDA'])
            print(f"  ✅ Data Provider: {type(market_data)}")
            print(f"  📊 Data keys: {list(market_data.keys()) if market_data else 'None'}")
            
            if market_data and 'NVDA' in market_data:
                nvda_data = market_data['NVDA']
                print(f"  📈 NVDA data keys: {list(nvda_data.keys()) if nvda_data else 'None'}")
                print(f"  💰 Current price: {nvda_data.get('current_price', 'N/A')}")
                print(f"  📊 Status: {nvda_data.get('status', 'N/A')}")
        except Exception as e:
            print(f"  ❌ Data Provider Error: {e}")
            market_data = None
        
        # Test AI analyzer
        print("\n🤖 Testing AI Analyzer...")
        analyzer = EnhancedAIControlledAnalyzer()
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n📝 Query {i}: {query}")
            print("-" * 40)
            
            try:
                if market_data:
                    result = await analyzer.generate_ai_controlled_analysis('NVDA', market_data, query)
                    
                    print(f"📊 Analysis Result:")
                    print(f"  Length: {len(result)} characters")
                    print(f"  Content: {result[:300]}...")
                    
                    # Check for common issues
                    issues = []
                    if 'RSI 72' in result or 'RSI 70' in result:
                        issues.append("❌ Contains fabricated RSI values")
                    if '$875' in result or '$950' in result:
                        issues.append("❌ Contains fabricated prices")
                    if 'will reach' in result or 'targets' in result:
                        issues.append("❌ Contains predictions")
                    if result.endswith('...'):
                        issues.append("❌ Response appears truncated")
                    if 'Unable to retrieve technical data' in result:
                        issues.append("❌ Data retrieval failed")
                    if 'Error occurred' in result:
                        issues.append("❌ System error occurred")
                    
                    if issues:
                        print(f"  ⚠️ Issues found:")
                        for issue in issues:
                            print(f"    {issue}")
                    else:
                        print(f"  ✅ No obvious issues detected")
                else:
                    print("  ⚠️ Skipping - no market data available")
                
            except Exception as e:
                print(f"❌ Error analyzing query: {e}")
                import traceback
                traceback.print_exc()
        
        print("\n✅ Core Pipeline Test Completed")
        return True
        
    except Exception as e:
        print(f"❌ Core Pipeline Test Failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the core pipeline test"""
    print("🚀 Testing Core Ask Pipeline System")
    print("=" * 80)
    
    # Run the async test
    success = asyncio.run(test_core_pipeline())
    
    print("=" * 80)
    if success:
        print("🎉 Core pipeline test completed!")
        print("This shows us what the actual system produces when you ask questions")
    else:
        print("❌ Core pipeline test failed - this shows us what's wrong!")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
