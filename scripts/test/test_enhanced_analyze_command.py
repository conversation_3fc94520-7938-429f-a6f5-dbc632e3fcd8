#!/usr/bin/env python3
"""
Test script for the enhanced multi-timeframe analyze command

This script tests the new comprehensive analyze command that automatically
performs multi-timeframe analysis with all indicators.
"""

import asyncio
import sys
import os
import time
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from src.bot.pipeline.commands.analyze.parallel_pipeline import execute_parallel_analyze_pipeline

async def test_enhanced_analyze_command():
    """Test the enhanced analyze command with comprehensive multi-timeframe analysis"""
    
    print("🚀 **TESTING ENHANCED ANALYZE COMMAND**")
    print("=" * 60)
    
    test_symbols = ['AAPL', 'MSFT', 'TSLA']
    
    for symbol in test_symbols:
        print(f"\n📊 **Testing {symbol}**")
        print("-" * 40)
        
        start_time = time.time()
        
        try:
            # Test the enhanced analyze command with comprehensive analysis
            context = await execute_parallel_analyze_pipeline(
                ticker=symbol,
                analysis_type="comprehensive_multi_timeframe",
                timeframes=['1d', '1w', '1m', '3m', '6m', '1y'],
                include_all_indicators=True,
                include_support_resistance=True,
                include_volume_analysis=True,
                include_risk_assessment=True,
                include_price_targets=True,
                user_id="test_user",
                guild_id="test_guild",
                correlation_id=f"test_{symbol}_{int(time.time())}",
                strict_mode=False
            )
            
            execution_time = time.time() - start_time
            
            # Check results
            print(f"✅ **Analysis completed in {execution_time:.2f}s**")
            print(f"📈 **Status:** {context.status}")
            
            if 'response' in context.processing_results:
                response = context.processing_results['response']
                print(f"📝 **Response length:** {len(response)} characters")

                # Validate response quality first
                quality_issues = []

                # Check for placeholder values
                if "$0.00" in response:
                    quality_issues.append("Contains placeholder $0.00 values")

                # Check for fallback indicators
                if "fallback due to processing errors" in response.lower():
                    quality_issues.append("Response indicates fallback mode")

                # Check for minimum content length
                if len(response) < 200:
                    quality_issues.append("Response too short")

                # Report quality issues
                if quality_issues:
                    print(f"❌ **QUALITY ISSUES DETECTED:**")
                    for issue in quality_issues:
                        print(f"   - {issue}")
                    print()
                else:
                    print(f"✅ **Response quality validation passed**")

                # Show key sections of the response
                lines = response.split('\n')
                
                # Find and display key sections
                sections = {
                    'EXECUTIVE SUMMARY': [],
                    'MULTI-TIMEFRAME TECHNICAL ANALYSIS': [],
                    'RISK ASSESSMENT': [],
                    'PRICE TARGETS': []
                }
                
                current_section = None
                for line in lines:
                    if '**EXECUTIVE SUMMARY**' in line:
                        current_section = 'EXECUTIVE SUMMARY'
                    elif '**MULTI-TIMEFRAME TECHNICAL ANALYSIS**' in line:
                        current_section = 'MULTI-TIMEFRAME TECHNICAL ANALYSIS'
                    elif '**RISK ASSESSMENT**' in line:
                        current_section = 'RISK ASSESSMENT'
                    elif '**PRICE TARGETS**' in line:
                        current_section = 'PRICE TARGETS'
                    elif line.startswith('##') and current_section:
                        current_section = None
                    elif current_section and line.strip():
                        sections[current_section].append(line)
                
                # Display key information
                for section_name, section_lines in sections.items():
                    if section_lines:
                        print(f"\n🔍 **{section_name}:**")
                        for line in section_lines[:3]:  # Show first 3 lines
                            print(f"   {line}")
                        if len(section_lines) > 3:
                            print(f"   ... ({len(section_lines) - 3} more lines)")
                
                # Check for comprehensive analysis indicators
                analysis_quality_indicators = {
                    'Multi-timeframe': any('timeframe' in line.lower() for line in lines),
                    'Technical Analysis': any('rsi' in line.lower() or 'macd' in line.lower() for line in lines),
                    'Risk Assessment': any('risk' in line.lower() for line in lines),
                    'Price Targets': any('target' in line.lower() for line in lines),
                    'Trend Analysis': any('trend' in line.lower() for line in lines)
                }
                
                print(f"\n📋 **Analysis Quality Check:**")
                for indicator, present in analysis_quality_indicators.items():
                    status = "✅" if present else "❌"
                    print(f"   {status} {indicator}")
                
                # Overall quality score
                quality_score = sum(analysis_quality_indicators.values()) / len(analysis_quality_indicators)
                print(f"\n🎯 **Overall Quality Score:** {quality_score:.1%}")
                
            else:
                print("❌ **No response generated**")
                print(f"📊 **Available results:** {list(context.processing_results.keys())}")
            
            # Check for errors
            if hasattr(context, 'error_log') and context.error_log:
                print(f"\n⚠️ **Errors encountered:** {len(context.error_log)}")
                for error in context.error_log[:3]:  # Show first 3 errors
                    print(f"   • {error.get('error_message', 'Unknown error')}")
            
        except Exception as e:
            execution_time = time.time() - start_time
            print(f"❌ **Test failed after {execution_time:.2f}s:** {e}")
            import traceback
            traceback.print_exc()
        
        print("-" * 40)
    
    print(f"\n🎉 **ENHANCED ANALYZE COMMAND TESTING COMPLETE**")
    print("=" * 60)

async def test_backward_compatibility():
    """Test that the standard analyze command still works"""
    
    print("\n🔄 **TESTING BACKWARD COMPATIBILITY**")
    print("=" * 60)
    
    try:
        # Test standard analysis (backward compatibility)
        context = await execute_parallel_analyze_pipeline(
            ticker="AAPL",
            analysis_type="standard",  # This should use the old pipeline
            user_id="test_user",
            guild_id="test_guild"
        )
        
        print(f"✅ **Backward compatibility test passed**")
        print(f"📈 **Status:** {context.status}")
        
        if 'response' in context.processing_results:
            response = context.processing_results['response']
            print(f"📝 **Response length:** {len(response)} characters")
        
    except Exception as e:
        print(f"❌ **Backward compatibility test failed:** {e}")

async def main():
    """Main test function"""
    
    print(f"🧪 **ENHANCED ANALYZE COMMAND TEST SUITE**")
    print(f"⏰ **Started at:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # Test enhanced analyze command
    await test_enhanced_analyze_command()
    
    # Test backward compatibility
    await test_backward_compatibility()
    
    print(f"\n✨ **ALL TESTS COMPLETED**")
    print(f"⏰ **Finished at:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    asyncio.run(main())
