#!/usr/bin/env python3
"""
Simple database connection test
"""

import asyncio
import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def test_supabase():
    """Test Supabase connection"""
    print("🔹 Testing Supabase SDK")
    print("-" * 30)
    
    try:
        from supabase import create_client
        
        url = os.getenv('SUPABASE_URL')
        key = os.getenv('SUPABASE_KEY')
        
        print(f"URL: {url}")
        print(f"Key prefix: {key[:10]}..." if key else "None")
        
        if not url or not key:
            print("❌ Missing Supabase credentials")
            return False
        
        # Create client
        supabase = create_client(url, key)
        
        # Test with a simple query
        result = supabase.table('health_check').select('*').limit(1).execute()
        print(f"✅ Supabase connection successful: {result}")
        return True
        
    except Exception as e:
        print(f"❌ Supabase error: {e}")
        return False

async def test_postgres():
    """Test Postgres connection"""
    print("\n🔹 Testing Postgres Connection")
    print("-" * 30)
    
    try:
        from sqlalchemy.ext.asyncio import create_async_engine
        from sqlalchemy import text
        
        db_url = os.getenv('DATABASE_URL')
        print(f"DB URL prefix: {db_url[:30]}..." if db_url else "None")
        
        if not db_url:
            print("❌ Missing DATABASE_URL")
            return False
        
        # Ensure asyncpg driver
        if not db_url.startswith('postgresql+asyncpg://'):
            if db_url.startswith('postgresql://'):
                db_url = db_url.replace('postgresql://', 'postgresql+asyncpg://')
        
        # Create engine
        engine = create_async_engine(db_url, echo=False)
        
        # Test connection
        async with engine.connect() as conn:
            result = await conn.execute(text("SELECT NOW() as current_time"))
            time = result.scalar()
            print(f"✅ Postgres connection successful: {time}")
            return True
        
    except Exception as e:
        print(f"❌ Postgres error: {e}")
        return False

async def main():
    """Run tests"""
    print("🚀 Simple Database Connection Test")
    print("=" * 50)
    
    # Load environment
    from dotenv import load_dotenv
    load_dotenv()
    
    # Test connections
    supabase_ok = await test_supabase()
    postgres_ok = await test_postgres()
    
    print(f"\n📊 Results:")
    print(f"  Supabase: {'✅ READY' if supabase_ok else '❌ FAILED'}")
    print(f"  Postgres: {'✅ READY' if postgres_ok else '❌ FAILED'}")
    
    if supabase_ok or postgres_ok:
        print("\n🎉 At least one connection is working!")
    else:
        print("\n💥 No connections are working!")

if __name__ == "__main__":
    asyncio.run(main())
