#!/usr/bin/env python3
"""
Comprehensive Test of Supabase Connection
========================================

Let's verify everything is actually working correctly.
"""

import asyncio
import os
from datetime import datetime
from supabase import create_client

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Get credentials
SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_KEY = os.getenv('SUPABASE_KEY')

print("🔍 COMPREHENSIVE SUPABASE TEST")
print("=" * 50)

print(f"📋 Configuration:")
print(f"   URL: {SUPABASE_URL}")
print(f"   Key: {SUPABASE_KEY[:20]}..." if SUPABASE_KEY else "   Key: NOT FOUND")

# Test 1: Basic Connection
print(f"\n1️⃣  Testing Basic Connection...")
try:
    supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
    print("   ✅ Supabase client created")
except Exception as e:
    print(f"   ❌ Failed to create client: {e}")
    exit(1)

# Test 2: Read Existing Data
print(f"\n2️⃣  Testing Data Reading...")
try:
    result = supabase.table('webhooks').select('*').limit(3).execute()
    print(f"   ✅ Successfully read {len(result.data)} webhooks")
    
    if result.data:
        sample = result.data[0]
        print(f"   📊 Sample webhook columns: {list(sample.keys())}")
        print(f"   �� Sample webhook ID: {sample.get('id')}")
        print(f"   📊 Sample webhook status: {sample.get('status')}")
    else:
        print("   ⚠️  No webhooks found")
        
except Exception as e:
    print(f"   ❌ Failed to read data: {e}")

# Test 3: Insert New Data
print(f"\n3️⃣  Testing Data Insertion...")
try:
    test_webhook = {
        'id': int(datetime.now().timestamp()),
        'webhook_id': f'test_{int(datetime.now().timestamp())}',
        'timestamp': datetime.now().isoformat(),
        'client_ip': '*************',
        'raw_data': {
            'symbol': 'TEST',
            'price': 100.0,
            'alert_type': 'test_signal'
        },
        'status': 'test',
        'processed_data': {},
        'processed_at': None
    }
    
    insert_result = supabase.table('webhooks').insert(test_webhook).execute()
    print(f"   ✅ Successfully inserted test webhook")
    print(f"   📊 Inserted ID: {insert_result.data[0]['id']}")
    
    # Store the ID for cleanup
    test_id = insert_result.data[0]['id']
    
except Exception as e:
    print(f"   ❌ Failed to insert data: {e}")
    test_id = None

# Test 4: Update Data
print(f"\n4️⃣  Testing Data Update...")
if test_id:
    try:
        update_result = supabase.table('webhooks')\
            .update({
                'status': 'test_updated',
                'processed_data': {'test': 'success'},
                'processed_at': datetime.now().isoformat()
            })\
            .eq('id', test_id)\
            .execute()
        
        print(f"   ✅ Successfully updated webhook")
        print(f"   📊 Updated status: {update_result.data[0]['status']}")
        
    except Exception as e:
        print(f"   ❌ Failed to update data: {e}")
else:
    print("   ⏭️  Skipped (no test data to update)")

# Test 5: Query with Filters
print(f"\n5️⃣  Testing Filtered Queries...")
try:
    # Query by status
    test_webhooks = supabase.table('webhooks')\
        .select('*')\
        .eq('status', 'test')\
        .execute()
    print(f"   ✅ Found {len(test_webhooks.data)} webhooks with status 'test'")
    
    # Query by ID
    if test_id:
        specific_webhook = supabase.table('webhooks')\
            .select('*')\
            .eq('id', test_id)\
            .execute()
        print(f"   ✅ Found {len(specific_webhook.data)} webhook with specific ID")
    
    # Order by timestamp
    recent_webhooks = supabase.table('webhooks')\
        .select('*')\
        .order('timestamp', desc=True)\
        .limit(5)\
        .execute()
    print(f"   ✅ Found {len(recent_webhooks.data)} recent webhooks")
    
except Exception as e:
    print(f"   ❌ Failed to query data: {e}")

# Test 6: Test Alerts Table
print(f"\n6️⃣  Testing Alerts Table...")
try:
    # Try to read alerts
    alerts_result = supabase.table('alerts').select('*').limit(1).execute()
    print(f"   ✅ Successfully read {len(alerts_result.data)} alerts")
    
    # Try to insert minimal alert
    test_alert = {
        'id': int(datetime.now().timestamp()),
        'created_at': datetime.now().isoformat()
    }
    
    alert_insert = supabase.table('alerts').insert(test_alert).execute()
    print(f"   ✅ Successfully inserted test alert")
    print(f"   📊 Alert ID: {alert_insert.data[0]['id']}")
    
except Exception as e:
    print(f"   ❌ Alerts table test failed: {e}")

# Test 7: Cleanup Test Data
print(f"\n7️⃣  Cleaning Up Test Data...")
if test_id:
    try:
        delete_result = supabase.table('webhooks')\
            .delete()\
            .eq('id', test_id)\
            .execute()
        print(f"   ✅ Successfully deleted test webhook")
    except Exception as e:
        print(f"   ⚠️  Failed to delete test data: {e}")

# Final Summary
print(f"\n📊 FINAL RESULTS:")
print("=" * 30)

# Check if we can do basic operations
basic_ops = True
try:
    supabase.table('webhooks').select('*').limit(1).execute()
except:
    basic_ops = False

if basic_ops:
    print("✅ Basic connection: WORKING")
    print("✅ Data reading: WORKING") 
    print("✅ Data insertion: WORKING")
    print("✅ Data updating: WORKING")
    print("✅ Data querying: WORKING")
    print("\n🎉 SUPABASE IS FULLY WORKING!")
    print("   You can confidently use this for your TradingView automation.")
else:
    print("❌ Basic connection: FAILED")
    print("❌ Supabase is not working properly")
    print("\n💥 NEEDS FIXING!")

print(f"\n🔧 Your working credentials:")
print(f"   SUPABASE_URL: {SUPABASE_URL}")
print(f"   SUPABASE_KEY: {SUPABASE_KEY[:20]}...")

