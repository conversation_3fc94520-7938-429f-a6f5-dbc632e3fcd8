#!/usr/bin/env python3
"""
Simple import test for pipeline_events module.
"""

import sys
from pathlib import Path

# Add the project root to the path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

print(f"Project root: {project_root}")
print(f"Python path: {sys.path[:5]}")  # Show first 5 paths

try:
    import pipeline_events
    print("Successfully imported pipeline_events")
    print(f"Available attributes: {[attr for attr in dir(pipeline_events) if not attr.startswith('_')]}")
    
    if hasattr(pipeline_events, 'PipelineEventEmitter'):
        print("PipelineEventEmitter found!")
        PipelineEventEmitter = pipeline_events.PipelineEventEmitter
        print(f"PipelineEventEmitter: {PipelineEventEmitter}")
    else:
        print("PipelineEventEmitter NOT found!")
        
except Exception as e:
    print(f"Import failed: {e}")
    import traceback
    traceback.print_exc()