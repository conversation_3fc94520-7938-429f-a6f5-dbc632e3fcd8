#!/usr/bin/env python3
"""
Test the simple AI system without complex dependencies
"""

import sys
import os
import asyncio
from datetime import datetime

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

async def test_simple_ai():
    """Test the simple AI system"""
    print("🔍 Testing Simple AI System")
    print("=" * 60)
    
    try:
        # Test the AI chat processor directly
        from src.shared.ai_services.unified_ai_processor import UnifiedAIProcessor as AIChatProcessorWrapper
        
        ai_processor = AIChatProcessorWrapper()
        
        # Test queries
        test_queries = [
            "What are the key support and resistance levels for NVDA?",
            "Is NVDA oversold or overbought based on RSI?",
            "What does the MACD indicator tell us about momentum?",
            "Should I buy or sell NVDA right now?",
            "What are the risks with NVDA at current levels?"
        ]
        
        print("🧪 Testing AI Chat Processor...")
        print("=" * 60)
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n📝 Query {i}: {query}")
            print("-" * 50)
            
            try:
                # Create a simple prompt
                prompt = f"""You are a financial analyst. Answer this question about NVDA stock:

Question: {query}

Please provide a professional analysis based on general market knowledge. Do not make specific price predictions or guarantees.

Current NVDA context:
- Current price: ~$177.82
- Recent performance: Mixed
- Market conditions: Volatile

Provide a structured response with:
1. Key technical levels
2. Risk assessment
3. Trading recommendation
4. Disclaimer about not being financial advice"""
                
                result = await ai_processor.process(prompt)
                
                print(f"📊 AI Response:")
                print(f"  Type: {type(result)}")
                print(f"  Content: {str(result)[:400]}...")
                
                # Check for common issues
                issues = []
                if 'RSI 72' in str(result) or 'RSI 70' in str(result):
                    issues.append("❌ Contains fabricated RSI values")
                if '$875' in str(result) or '$950' in str(result):
                    issues.append("❌ Contains fabricated prices")
                if 'will reach' in str(result) or 'targets' in str(result):
                    issues.append("❌ Contains predictions")
                if str(result).endswith('...'):
                    issues.append("❌ Response appears truncated")
                
                if issues:
                    print(f"  ⚠️ Issues found:")
                    for issue in issues:
                        print(f"    {issue}")
                else:
                    print(f"  ✅ No obvious issues detected")
                
            except Exception as e:
                print(f"❌ Error processing query: {e}")
                import traceback
                traceback.print_exc()
        
        print("\n✅ Simple AI Test Completed")
        return True
        
    except Exception as e:
        print(f"❌ Simple AI Test Failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the simple AI test"""
    print("🚀 Testing Simple AI System")
    print("=" * 80)
    
    # Run the async test
    success = asyncio.run(test_simple_ai())
    
    print("=" * 80)
    if success:
        print("🎉 Simple AI test completed!")
        print("This shows us what the AI system produces when you ask questions")
    else:
        print("❌ Simple AI test failed - this shows us what's wrong!")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
