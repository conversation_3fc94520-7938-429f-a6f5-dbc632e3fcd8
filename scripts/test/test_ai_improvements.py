#!/usr/bin/env python3
"""
Test script to verify AI processing improvements
Tests the same query that was getting a C grade to see if we can achieve an A grade.
"""

import asyncio
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_ai_improvements():
    """Test the improved AI processing"""
    
    try:
        from src.shared.ai_services.unified_ai_processor import UnifiedAIProcessor as CleanAIProcessor
        
        # Create processor
        processor = CleanAIProcessor()
        
        # Test the same query that was getting a C grade
        test_query = "what stock are you extremely bullish on for the next 2 days?"
        
        print("🧪 Testing AI Improvements")
        print("=" * 50)
        print(f"Query: {test_query}")
        print()
        
        # Process the query
        result = await processor.process_query(test_query)
        
        print("📊 AI Analysis Result:")
        print(f"Intent: {result.intent}")
        print(f"Symbols: {result.symbols}")
        print(f"Needs Data: {result.needs_data}")
        print(f"Confidence: {result.confidence}")
        print()
        
        print("💬 AI Response:")
        print("-" * 30)
        print(result.response)
        print("-" * 30)
        print()
        
        # Analyze the response quality
        response_text = result.response.lower()
        
        # Check for improvements
        improvements = []
        
        if "bullish" in response_text or "recommend" in response_text:
            improvements.append("✅ Provides specific recommendations")
        
        if any(symbol in response_text for symbol in ["nvda", "tsla", "amd", "aapl", "gme"]):
            improvements.append("✅ Mentions specific stocks")
        
        if any(word in response_text for word in ["price", "target", "entry", "stop", "risk"]):
            improvements.append("✅ Includes specific trading details")
        
        if "disclaimer" not in response_text and "not personalized" not in response_text:
            improvements.append("✅ Avoids generic disclaimers")
        
        if len(result.response) > 200:
            improvements.append("✅ Provides comprehensive response")
        
        print("🎯 Response Quality Analysis:")
        for improvement in improvements:
            print(improvement)
        
        if not improvements:
            print("❌ No significant improvements detected")
        
        print()
        print(f"📈 Overall Grade Estimate: {'A' if len(improvements) >= 4 else 'B' if len(improvements) >= 2 else 'C'}")
        
        return result
        
    except Exception as e:
        print(f"❌ Error testing AI improvements: {e}")
        import traceback
        traceback.print_exc()
        return None

async def main():
    """Main test function"""
    print("🚀 Starting AI Improvements Test")
    print()
    
    result = await test_ai_improvements()
    
    if result:
        print("✅ Test completed successfully")
    else:
        print("❌ Test failed")

if __name__ == "__main__":
    asyncio.run(main())
