#!/usr/bin/env python3
"""
Test Data Binding Fix

This script tests the data binding validation to ensure the AI uses calculated indicators
instead of generating its own price levels and technical values.
"""

import json
from src.shared.ai_chat.response_formatter import ResponseFormatter

def test_data_binding_validation():
    """Test that AI uses calculated indicators instead of generating its own"""
    
    # Test data with calculated indicators
    test_data = {
        'NVDA': {
            'current_price': 177.82,
            'change_percent': 0.37,
            'volume': 45000000,
            'indicators': {
                'rsi': 45.2,
                'macd': {'macd': 2.1, 'signal': 1.8, 'histogram': 0.3},
                'sma_20': 175.50,
                'sma_50': 170.25,
                'support_level': 165.00,
                'resistance_level': 185.00,
                'bollinger_bands': {'upper': 190.00, 'middle': 175.50, 'lower': 161.00}
            }
        }
    }
    
    # AI response that ignores calculated data (the problem we're fixing)
    ai_response_with_hallucination = """
    Based on current market data for NVIDIA (NVDA), here are key entry levels to watch this week:
    
    Current Technical Picture:
    - NVDA is trading near its 52-week high range
    - Key support levels: $875-885 (recent consolidation zone)
    - Resistance: $950-970 (all-time high area)
    - RSI is at 70 (overbought)
    
    Entry Strategy Options:
    1. Aggressive: $890-900 range if we see a pullback to the 20-day MA
    2. Conservative: $850-870 on any deeper pullback to the 50-day MA
    3. Momentum: Break above $970 with volume could signal continuation
    """
    
    print("🔍 Testing Data Binding Validation Fix")
    print("=" * 50)
    
    print(f"📊 Calculated Indicators:")
    print(f"  - Current Price: ${test_data['NVDA']['current_price']}")
    print(f"  - Support Level: ${test_data['NVDA']['indicators']['support_level']}")
    print(f"  - Resistance Level: ${test_data['NVDA']['indicators']['resistance_level']}")
    print(f"  - RSI: {test_data['NVDA']['indicators']['rsi']}")
    print(f"  - SMA 20: ${test_data['NVDA']['indicators']['sma_20']}")
    
    print(f"\n❌ AI Response (with hallucination):")
    print(ai_response_with_hallucination)
    
    # Apply the fix
    formatter = ResponseFormatter()
    fixed_response = formatter._validate_price_accuracy(ai_response_with_hallucination, test_data)
    
    print(f"\n✅ Fixed Response:")
    print(fixed_response)
    
    # Validate the fix
    print(f"\n🧪 Validation Results:")
    
    # Check if support levels were corrected
    if "$165" in fixed_response and "$875" not in fixed_response:
        print("  ✅ Support levels corrected: $875 → $165")
    else:
        print("  ❌ Support levels not corrected")
    
    # Check if resistance levels were corrected
    if "$185" in fixed_response and "$950" not in fixed_response:
        print("  ✅ Resistance levels corrected: $950 → $185")
    else:
        print("  ❌ Resistance levels not corrected")
    
    # Check if RSI was corrected
    if "RSI 45.2" in fixed_response and "RSI 70" not in fixed_response:
        print("  ✅ RSI corrected: 70 → 45.2")
    else:
        print("  ❌ RSI not corrected")
    
    # Check if price levels are realistic
    unrealistic_prices = ["$875", "$885", "$890", "$900", "$950", "$970"]
    has_unrealistic = any(price in fixed_response for price in unrealistic_prices)
    
    if not has_unrealistic:
        print("  ✅ No unrealistic price levels detected")
    else:
        print("  ❌ Still contains unrealistic price levels")
    
    return fixed_response

def test_edge_cases():
    """Test edge cases for data binding validation"""
    
    print(f"\n🧪 Testing Edge Cases")
    print("=" * 30)
    
    # Test with missing indicators
    incomplete_data = {
        'AAPL': {
            'current_price': 150.00,
            'indicators': {
                'rsi': 55.0,
                # Missing support/resistance levels
            }
        }
    }
    
    ai_response = "Support at $875, resistance at $950, RSI at 70"
    
    formatter = ResponseFormatter()
    fixed_response = formatter._validate_price_accuracy(ai_response, incomplete_data)
    
    print(f"📊 Incomplete Data Test:")
    print(f"  - Current Price: ${incomplete_data['AAPL']['current_price']}")
    print(f"  - RSI: {incomplete_data['AAPL']['indicators']['rsi']}")
    print(f"  - Missing: support/resistance levels")
    
    print(f"\n❌ AI Response: {ai_response}")
    print(f"✅ Fixed Response: {fixed_response}")
    
    # Test with zero/negative prices
    invalid_data = {
        'INVALID': {
            'current_price': 0,  # Invalid price
            'indicators': {}
        }
    }
    
    ai_response = "Support at $100, resistance at $200"
    fixed_response = formatter._validate_price_accuracy(ai_response, invalid_data)
    
    print(f"\n📊 Invalid Data Test:")
    print(f"  - Current Price: ${invalid_data['INVALID']['current_price']} (invalid)")
    print(f"  - AI Response: {ai_response}")
    print(f"  - Fixed Response: {fixed_response}")

if __name__ == "__main__":
    print("🚀 Data Binding Validation Test Suite")
    print("=" * 50)
    
    # Run main test
    fixed_response = test_data_binding_validation()
    
    # Run edge case tests
    test_edge_cases()
    
    print(f"\n🎯 Summary:")
    print(f"  - Data binding validation implemented")
    print(f"  - AI forced to use calculated indicators")
    print(f"  - Price hallucination prevented")
    print(f"  - Real-time validation active")
    
    print(f"\n✅ Test Complete!")
