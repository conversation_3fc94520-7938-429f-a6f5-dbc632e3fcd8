#!/usr/bin/env python3
"""
Test script to debug AST parsing of command decorators.
"""

import ast

# Sample code to parse
sample_code = '''
import discord
from discord.ext import commands
from discord import app_commands

class OptimizedAskCommand(commands.Cog):
    """Optimized AI-powered ask command with real performance improvements."""

    @app_commands.command(name="ask", description="Ask the AI about trading and markets (Optimized)")
    @app_commands.describe(
        query="Your question about trading and markets",
        attachment="Voice message attachment (optional)"
    )
    async def ask_command(self, interaction: discord.Interaction, query: str = None, attachment: discord.Attachment = None):
        """Ask the AI about trading and markets with real performance optimizations"""
        pass

async def setup(bot: commands.Bot):
    """Setup the optimized ask command extension"""
    await bot.add_cog(OptimizedAskCommand(bot))
'''

def extract_command_metadata(content):
    """Extract command metadata from Python code."""
    metadata = []
    
    try:
        tree = ast.parse(content)
        
        # Look for command classes (inheriting from commands.Cog)
        class_count = 0
        function_count = 0
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                class_count += 1
                print(f"Found class: {node.name}")
                print(f"  Bases: {[ast.dump(base) for base in node.bases]}")
                
                # Check if class inherits from commands.Cog
                is_cog = False
                for base in node.bases:
                    if isinstance(base, ast.Attribute) and base.attr == 'Cog':
                        print(f"  Class inherits from commands.Cog via Attribute")
                        is_cog = True
                        break
                    elif isinstance(base, ast.Name) and base.id == 'Cog':
                        print(f"  Class inherits from commands.Cog via Name")
                        is_cog = True
                        break
                
                if not is_cog:
                    print(f"  Class does not inherit from commands.Cog")
                    continue
                
                class_name = node.name
                class_docstring = ast.get_docstring(node) or ""
                print(f"  Class docstring: {class_docstring}")
                
                # Look for command methods in this class
                print(f"  Class body items: {len(node.body)}")
                for item in node.body:
                    print(f"  Body item type: {type(item)}")
                    # Handle both FunctionDef and AsyncFunctionDef
                    if isinstance(item, (ast.FunctionDef, ast.AsyncFunctionDef)):
                        function_count += 1
                        print(f"  Found function: {item.name}")
                        print(f"    Decorators: {len(item.decorator_list)}")
                        
                        # Check for @app_commands.command decorator
                        command_name = None
                        command_description = ""
                        command_params = []
                        
                        # Look for decorators
                        for i, decorator in enumerate(item.decorator_list):
                            print(f"    Decorator {i}: {ast.dump(decorator)}")
                            
                            # Handle @app_commands.command() with arguments
                            if isinstance(decorator, ast.Call) and isinstance(decorator.func, ast.Attribute):
                                if decorator.func.attr == 'command':
                                    print(f"    Found @app_commands.command decorator")
                                    # Extract command name from decorator arguments
                                    for keyword in decorator.keywords:
                                        print(f"      Keyword: {keyword.arg}")
                                        if keyword.arg == 'name':
                                            if isinstance(keyword.value, ast.Constant):
                                                command_name = keyword.value.value
                                                print(f"      Command name: {command_name}")
                                        elif keyword.arg == 'description':
                                            if isinstance(keyword.value, ast.Constant):
                                                command_description = keyword.value.value
                                                print(f"      Command description: {command_description}")
                                    # If no name was specified, use the function name
                                    if command_name is None:
                                        command_name = item.name
                                        print(f"      Using function name as command name: {command_name}")
                            # Handle @app_commands.command (without parentheses)
                            elif isinstance(decorator, ast.Attribute) and decorator.attr == 'command':
                                print(f"    Found @app_commands.command (no args) decorator")
                                command_name = item.name  # Use function name as command name
                                print(f"      Command name: {command_name}")
                        
                        # If we found a command, extract parameters
                        if command_name:
                            print(f"    Found command: {command_name}")
                            # Extract function parameters
                            for arg in item.args.args:
                                if arg.arg != 'self' and arg.arg != 'interaction':
                                    param_name = arg.arg
                                    param_annotation = ""
                                    if arg.annotation:
                                        param_annotation = ast.unparse(arg.annotation) if hasattr(ast, 'unparse') else str(arg.annotation)
                                    command_params.append({
                                        'name': param_name,
                                        'type': param_annotation
                                    })
                                    print(f"      Parameter: {param_name} ({param_annotation})")
                            
                            # Get function docstring
                            func_docstring = ast.get_docstring(item) or ""
                            print(f"    Function docstring: {func_docstring}")
                            
                            metadata.append({
                                'name': command_name,
                                'class': class_name,
                                'description': command_description,
                                'parameters': command_params,
                                'docstring': func_docstring
                            })
        
        print(f"Total classes found: {class_count}")
        print(f"Total functions found: {function_count}")
    except Exception as e:
        print(f"Error parsing: {e}")
        import traceback
        traceback.print_exc()
    
    return metadata

# Test the function
metadata = extract_command_metadata(sample_code)
print("\nExtracted metadata:")
for item in metadata:
    print(f"  Command: {item['name']}")
    print(f"  Class: {item['class']}")
    print(f"  Description: {item['description']}")
    print(f"  Parameters: {item['parameters']}")
    print(f"  Docstring: {item['docstring']}")