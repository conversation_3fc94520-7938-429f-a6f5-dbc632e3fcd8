#!/usr/bin/env python3
"""
Test Enhanced Data Provider System

Tests the new enhanced error handling, health monitoring, and fallback mechanisms.
"""

import asyncio
import sys
import os
import logging
from pathlib import Path

# Add root to path
ROOT = Path(__file__).parent.parent.parent
sys.path.insert(0, str(ROOT))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_enhanced_data_source_manager():
    """Test the enhanced data source manager"""
    print("🔧 Testing Enhanced Data Source Manager")
    print("=" * 50)
    
    try:
        from src.api.data.providers.data_source_manager import DataSourceManager
        
        # Create manager instance
        manager = DataSourceManager()
        print("✅ DataSourceManager created successfully")
        
        # Test basic health status
        print("\n🏥 Basic Health Status:")
        health = manager.get_health_status()
        
        print(f"  Config Valid: {'✅' if health['config_valid'] else '❌'}")
        print(f"  Providers: {list(health['providers'].keys())}")
        
        for provider, status in health['providers'].items():
            emoji = '✅' if status else '❌'
            print(f"    {provider}: {emoji}")
        
        # Test enhanced metrics if available
        if 'enhanced_metrics' in health:
            print("\n📊 Enhanced Metrics Available:")
            enhanced = health['enhanced_metrics']
            summary = enhanced.get('summary', {})
            print(f"  Total Providers: {summary.get('total_providers', 0)}")
            print(f"  Healthy Providers: {summary.get('healthy_providers', 0)}")
            print(f"  Circuit Breakers Open: {summary.get('circuit_breaker_open', 0)}")
        
        # Test data fetch with enhanced error handling
        print("\n📊 Testing Enhanced Data Fetch:")
        symbols = ['AAPL', 'GOOGL', 'MSFT']
        
        for symbol in symbols:
            try:
                result = await manager.get_market_data(symbol)
                
                if result['status'] == 'success':
                    provider = result.get('provider', 'unknown')
                    price = result.get('data', {}).get('price', 'N/A')
                    response_time = result.get('response_time', 0)
                    print(f"  ✅ {symbol}: ${price} via {provider} ({response_time:.2f}s)")
                else:
                    error = result.get('error', 'Unknown error')
                    print(f"  ❌ {symbol}: Failed - {error}")
                    
            except Exception as e:
                print(f"  ❌ {symbol}: Exception - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced DataSourceManager test failed: {e}")
        return False

async def test_error_handler_directly():
    """Test the enhanced error handler directly"""
    print("\n🛡️ Testing Enhanced Error Handler")
    print("=" * 50)
    
    try:
        from src.shared.data_providers.enhanced_error_handler import enhanced_error_handler
        
        # Test provider health report
        health_report = enhanced_error_handler.get_provider_health_report()
        print(f"✅ Error handler health report generated")
        print(f"  Timestamp: {health_report['timestamp']}")
        print(f"  Total Providers: {health_report['summary']['total_providers']}")
        print(f"  Healthy Providers: {health_report['summary']['healthy_providers']}")
        
        # Test fallback execution with mock function
        async def mock_provider_operation(provider_name: str):
            """Mock provider operation for testing"""
            if provider_name == 'yfinance':
                return {'price': 245.5, 'symbol': 'AAPL'}
            elif provider_name == 'polygon':
                raise Exception("API key not configured")
            elif provider_name == 'finnhub':
                raise Exception("Rate limit exceeded")
            else:
                raise Exception("Provider not available")
        
        print("\n🔄 Testing Fallback Mechanism:")
        result = await enhanced_error_handler.execute_with_fallback(
            operation="test_operation",
            providers=['polygon', 'finnhub', 'yfinance', 'fallback'],
            operation_func=mock_provider_operation
        )
        
        if result['success']:
            print(f"  ✅ Fallback successful via {result['provider']}")
            print(f"  Data: {result['data']}")
        else:
            print(f"  ❌ Fallback failed: {result['error']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error handler test failed: {e}")
        return False

async def test_health_monitor():
    """Test the health monitoring system"""
    print("\n🔍 Testing Health Monitor")
    print("=" * 50)
    
    try:
        from src.shared.data_providers.health_monitor import health_monitor
        
        # Test health report
        health_report = health_monitor.get_health_report()
        print(f"✅ Health monitor report generated")
        print(f"  Overall Status: {health_report['overall_status']}")
        print(f"  Monitoring Active: {health_report['monitoring_active']}")
        print(f"  Total Providers: {health_report['summary']['total_providers']}")
        
        # Test manual health check if providers are registered
        if health_report['summary']['total_providers'] > 0:
            print("\n🔍 Running Manual Health Check:")
            manual_result = await health_monitor.manual_health_check()
            print(f"  ✅ Manual health check completed")
            print(f"  Available Providers: {manual_result['summary']['available_providers']}")
        else:
            print("  ⚠️ No providers registered for health monitoring")
        
        return True
        
    except Exception as e:
        print(f"❌ Health monitor test failed: {e}")
        return False

async def test_individual_providers():
    """Test individual providers with enhanced error handling"""
    print("\n🔌 Testing Individual Providers with Enhanced Handling")
    print("=" * 50)
    
    providers_to_test = [
        ('yfinance', 'src.shared.data_providers.yfinance_provider', 'YFinanceProvider'),
        ('polygon', 'src.shared.data_providers.polygon_provider', 'PolygonProvider'),
        ('finnhub', 'src.shared.data_providers.finnhub_provider', 'FinnhubProvider'),
        ('alpaca', 'src.shared.data_providers.alpaca_provider', 'AlpacaProvider')
    ]
    
    results = {}
    
    for provider_name, module_path, class_name in providers_to_test:
        print(f"\n📊 Testing {provider_name.title()} Provider:")
        
        try:
            # Dynamic import
            module = __import__(module_path, fromlist=[class_name])
            provider_class = getattr(module, class_name)
            provider = provider_class()
            
            print(f"  ✅ {provider_name} imported successfully")
            
            # Test with enhanced error handling
            from src.shared.data_providers.enhanced_error_handler import enhanced_error_handler
            
            async def provider_operation(prov_name):
                return await provider.get_current_price('AAPL')
            
            result = await enhanced_error_handler.execute_with_fallback(
                operation=f"test_{provider_name}",
                providers=[provider_name],
                operation_func=provider_operation
            )
            
            if result['success']:
                price = result['data'].get('price', 'N/A')
                print(f"  ✅ {provider_name} working: AAPL = ${price}")
                results[provider_name] = 'working'
            else:
                print(f"  ❌ {provider_name} failed: {result['error']}")
                results[provider_name] = 'failed'
                
        except Exception as e:
            print(f"  ❌ {provider_name} import/test failed: {e}")
            results[provider_name] = 'import_failed'
    
    return results

async def main():
    """Run all enhanced data provider tests"""
    print("🚀 ENHANCED DATA PROVIDER SYSTEM TESTS")
    print("=" * 60)
    
    # Run all tests
    test_results = {}
    
    test_results['data_source_manager'] = await test_enhanced_data_source_manager()
    test_results['error_handler'] = await test_error_handler_directly()
    test_results['health_monitor'] = await test_health_monitor()
    test_results['individual_providers'] = await test_individual_providers()
    
    # Generate summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    for test_name, result in test_results.items():
        if isinstance(result, bool):
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{test_name}: {status}")
        elif isinstance(result, dict):
            print(f"{test_name}:")
            for sub_test, sub_result in result.items():
                print(f"  {sub_test}: {sub_result}")
    
    # Overall assessment
    all_passed = all(
        result if isinstance(result, bool) else any(
            status == 'working' for status in result.values()
        ) for result in test_results.values()
    )
    
    print(f"\n🎯 OVERALL RESULT: {'✅ ENHANCED SYSTEM WORKING' if all_passed else '⚠️ SOME ISSUES DETECTED'}")
    
    if all_passed:
        print("🚀 Enhanced data provider system is ready for production!")
    else:
        print("🔧 Some components need attention before full deployment.")

if __name__ == "__main__":
    asyncio.run(main())
