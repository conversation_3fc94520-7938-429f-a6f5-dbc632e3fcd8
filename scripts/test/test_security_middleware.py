#!/usr/bin/env python3
"""
Test script for the new SecurityMiddleware implementation.
This script tests the security middleware functionality in a Docker environment.
"""

import asyncio
import httpx
import json
import time
import os
from typing import Dict, Any

# Test configuration
TEST_BASE_URL = "http://localhost:8001"
WEBHOOK_SECRET = "test-webhook-secret-123"
TEST_ALERT = {
    "symbol": "AAPL",
    "alert_type": "buy",
    "price": 150.00,
    "timestamp": int(time.time())
}

async def test_health_endpoint():
    """Test that health endpoint is accessible without authentication."""
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(f"{TEST_BASE_URL}/health")
            print(f"✅ Health endpoint: {response.status_code}")
            if response.status_code == 200:
                print(f"   Response: {response.json()}")
            return response.status_code == 200
        except Exception as e:
            print(f"❌ Health endpoint failed: {e}")
            return False

async def test_webhook_without_signature():
    """Test webhook endpoint without signature (should fail)."""
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                f"{TEST_BASE_URL}/webhook",
                json=TEST_ALERT,
                headers={"Content-Type": "application/json"}
            )
            print(f"🔒 Webhook without signature: {response.status_code}")
            if response.status_code == 401:
                print("   ✅ Correctly rejected unauthorized request")
                return True
            else:
                print(f"   ❌ Expected 401, got {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Webhook test failed: {e}")
            return False

async def test_webhook_with_signature():
    """Test webhook endpoint with valid signature."""
    import hmac
    import hashlib
    
    async with httpx.AsyncClient() as client:
        try:
            # Create valid signature
            body = json.dumps(TEST_ALERT).encode()
            signature = hmac.new(
                WEBHOOK_SECRET.encode(),
                body,
                hashlib.sha256
            ).hexdigest()
            
            response = await client.post(
                f"{TEST_BASE_URL}/webhook",
                json=TEST_ALERT,
                headers={
                    "Content-Type": "application/json",
                    "x-tradingview-signature": signature
                }
            )
            print(f"🔐 Webhook with signature: {response.status_code}")
            if response.status_code == 200:
                print("   ✅ Successfully processed webhook")
                return True
            else:
                print(f"   ❌ Expected 200, got {response.status_code}")
                print(f"   Response: {response.text}")
                return False
        except Exception as e:
            print(f"❌ Webhook with signature test failed: {e}")
            return False

async def test_rate_limiting():
    """Test rate limiting functionality."""
    async with httpx.AsyncClient() as client:
        try:
            # Make multiple requests quickly to trigger rate limiting
            responses = []
            for i in range(5):
                response = await client.get(f"{TEST_BASE_URL}/bot/status")
                responses.append(response.status_code)
                await asyncio.sleep(0.1)  # Small delay between requests
            
            print(f"🚦 Rate limiting test: {responses}")
            # Should have some successful requests before rate limiting kicks in
            success_count = sum(1 for status in responses if status == 200)
            print(f"   Successful requests: {success_count}/5")
            return success_count > 0
        except Exception as e:
            print(f"❌ Rate limiting test failed: {e}")
            return False

async def test_security_headers():
    """Test that security headers are present in responses."""
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(f"{TEST_BASE_URL}/health")
            headers = response.headers
            
            security_headers = [
                'X-Content-Type-Options',
                'X-Frame-Options',
                'X-XSS-Protection',
                'Referrer-Policy'
            ]
            
            print("🛡️ Security headers test:")
            all_present = True
            for header in security_headers:
                if header in headers:
                    print(f"   ✅ {header}: {headers[header]}")
                else:
                    print(f"   ❌ {header}: Missing")
                    all_present = False
            
            return all_present
        except Exception as e:
            print(f"❌ Security headers test failed: {e}")
            return False

async def main():
    """Run all security middleware tests."""
    print("🔒 Testing Security Middleware Implementation")
    print("=" * 50)
    
    # Set environment variable for webhook secret
    os.environ['TRADINGVIEW_WEBHOOK_SECRET'] = WEBHOOK_SECRET
    
    tests = [
        ("Health Endpoint", test_health_endpoint),
        ("Webhook without signature", test_webhook_without_signature),
        ("Webhook with signature", test_webhook_with_signature),
        ("Rate Limiting", test_rate_limiting),
        ("Security Headers", test_security_headers)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name}...")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All security middleware tests passed!")
    else:
        print("⚠️  Some tests failed. Check the implementation.")

if __name__ == "__main__":
    asyncio.run(main())
