#!/usr/bin/env python3
"""
Test the REAL enhanced AI analysis system with actual queries
"""

import sys
import os
import asyncio
from datetime import datetime

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

async def test_real_ai_system():
    """Test the actual enhanced AI analysis system"""
    print("🔍 Testing REAL Enhanced AI Analysis System")
    print("=" * 60)
    
    try:
        from src.bot.pipeline.commands.ask.stages.enhanced_ai_analysis import EnhancedAIControlledAnalyzer
        
        analyzer = EnhancedAIControlledAnalyzer()
        
        # Create real market data in the expected format
        market_data = {
            'NVDA': {
                'status': 'success',
                'current_price': 177.82,
                'change_percent': 0.37,
                'volume': 45000000,
                'price_data': {
                    'close': [170.0, 171.0, 172.0, 173.0, 174.0, 175.0, 176.0, 177.0, 178.0, 179.0] * 5,
                    'high': [171.0, 172.0, 173.0, 174.0, 175.0, 176.0, 177.0, 178.0, 179.0, 180.0] * 5,
                    'low': [169.0, 170.0, 171.0, 172.0, 173.0, 174.0, 175.0, 176.0, 177.0, 178.0] * 5,
                    'timestamp': datetime.now()
                }
            }
        }
        
        # Test real queries
        test_queries = [
            "What are the key support and resistance levels for NVDA?",
            "Is NVDA oversold or overbought based on RSI?",
            "What does the MACD indicator tell us about momentum?",
            "Should I buy or sell NVDA right now?",
            "What are the risks with NVDA at current levels?"
        ]
        
        print("🧪 Testing Real AI Analysis...")
        print("=" * 60)
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n📝 Query {i}: {query}")
            print("-" * 50)
            
            try:
                # Call the actual AI analysis method
                result = await analyzer.generate_ai_controlled_analysis('NVDA', market_data, query)
                
                print(f"📊 Analysis Result:")
                print(f"  Length: {len(result)} characters")
                print(f"  Content: {result[:300]}...")
                
                # Check for common issues
                issues = []
                if 'RSI 72' in result or 'RSI 70' in result:
                    issues.append("❌ Contains fabricated RSI values")
                if '$875' in result or '$950' in result:
                    issues.append("❌ Contains fabricated prices")
                if 'will reach' in result or 'targets' in result:
                    issues.append("❌ Contains predictions")
                if result.endswith('...'):
                    issues.append("❌ Response appears truncated")
                
                if issues:
                    print(f"  ⚠️ Issues found:")
                    for issue in issues:
                        print(f"    {issue}")
                else:
                    print(f"  ✅ No obvious issues detected")
                
            except Exception as e:
                print(f"❌ Error analyzing query: {e}")
                import traceback
                traceback.print_exc()
        
        print("\n✅ Real System Test Completed")
        return True
        
    except Exception as e:
        print(f"❌ Real System Test Failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the real system tests"""
    print("🚀 Testing REAL Enhanced AI Analysis System")
    print("=" * 80)
    
    # Run the async test
    success = asyncio.run(test_real_ai_system())
    
    print("=" * 80)
    if success:
        print("🎉 Real system test completed!")
        print("This shows us what the actual system is producing")
    else:
        print("❌ Real system test failed - this shows us what's wrong!")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
