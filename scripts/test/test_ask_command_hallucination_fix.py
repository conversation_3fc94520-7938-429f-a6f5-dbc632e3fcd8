#!/usr/bin/env python3
"""
Test the ask command with the hallucination fix
Tests that problematic queries no longer cause hallucination
"""

import asyncio
import sys
import os
import pytest

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def process_query(query, user_id="test_user", username="test_user"):
    """Process a query through the ask command pipeline"""
    try:
        from src.bot.pipeline.commands.ask.executor import execute_ask_pipeline
        return await execute_ask_pipeline(query, user_id, username)
    except Exception as e:
        return {"error": str(e), "status": "error"}

@pytest.mark.asyncio
async def test_ask_command_hallucination_fix():
    """Test that the ask command no longer hallucinates"""
    
    print("🧪 Testing Ask Command Hallucination Fix")
    print("=" * 60)
    
    # Test queries that previously caused hallucination
    problematic_queries = [
        "what stock are you extremely bullish on",
        "best stocks for day trading", 
        "trading recommendations",
        "what stocks should I buy",
        "market outlook",
        "bullish picks",
        "day trading advice",
        "find me a stock you are bullish on this week"
    ]
    
    print("Testing problematic queries that previously caused hallucination...")
    print()
    
    for i, query in enumerate(problematic_queries, 1):
        print(f"Test {i}: '{query}'")
        print("-" * 40)
        
        try:
            # Process the query with timeout
            result = await asyncio.wait_for(
                process_query(query),
                timeout=30.0
            )
            
            response = result.get('response', '')
            status = result.get('status', 'unknown')
            
            print(f"Status: {status}")
            print(f"Response: {response[:200]}{'...' if len(response) > 200 else ''}")
            
            # Check for problematic content
            problematic_phrases = [
                "extremely bullish",
                "specific trading recommendations", 
                "high-momentum stocks",
                "AI leader",
                "EV momentum",
                "semiconductor play",
                "tech stability",
                "bullish on for the next few days",
                "I'm extremely bullish on",
                "I recommend focusing on",
                "here are my top picks"
            ]
            
            has_problematic_content = any(phrase.lower() in response.lower() for phrase in problematic_phrases)
            
            if has_problematic_content:
                print("❌ FAIL: Response contains problematic hallucinated content")
            else:
                print("✅ PASS: Response is conservative and appropriate")
                
        except asyncio.TimeoutError:
            print("❌ TIMEOUT: Query took longer than 30 seconds")
        except Exception as e:
            print(f"❌ ERROR: {type(e).__name__}: {e}")
        
        print()
    
    print("=" * 60)
    print("Test completed!")

if __name__ == "__main__":
    asyncio.run(test_ask_command_hallucination_fix())
