#!/usr/bin/env python3
"""
Test script for the new UnifiedAIProcessor

Validates that the unified processor provides all the functionality
expected by the existing codebase before migration.
"""

import os
import sys
import asyncio
import logging

# Ensure src is importable
ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if ROOT not in sys.path:
    sys.path.append(ROOT)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_unified_processor():
    """Test the unified AI processor functionality"""
    print("🧪 Testing Unified AI Processor")
    print("=" * 50)
    
    try:
        # Import the unified processor
        from src.shared.ai_services.unified_ai_processor import (
            UnifiedAIProcessor, 
            create_unified_processor,
            unified_ai_processor,
            ProcessingResult
        )
        print("✅ Successfully imported UnifiedAIProcessor")
        
        # Test 1: Create processor instance
        print("\n🔧 Test 1: Creating processor instance")
        processor = UnifiedAIProcessor()
        print(f"✅ Created processor with pipeline_id: {processor.pipeline_id}")
        
        # Test 2: Factory function
        print("\n🔧 Test 2: Testing factory function")
        factory_processor = create_unified_processor()
        print("✅ Factory function works")
        
        # Test 3: Global instance
        print("\n🔧 Test 3: Testing global instance")
        global_processor = unified_ai_processor
        print(f"✅ Global instance available: {type(global_processor).__name__}")
        
        # Test 4: Primary process method
        print("\n🔧 Test 4: Testing primary process method")
        result = await processor.process("What is the current market sentiment?")
        print(f"✅ Process method returned: {result['status']}")
        print(f"   Response preview: {result['response'][:100]}...")
        
        # Test 5: Answer general question
        print("\n🔧 Test 5: Testing answer_general_question method")
        answer = await processor.answer_general_question("What is RSI?")
        print(f"✅ General question answered")
        print(f"   Answer preview: {answer[:100]}...")
        
        # Test 6: Process query method (backward compatibility)
        print("\n🔧 Test 6: Testing process_query method")
        query_result = await processor.process_query("Tell me about AAPL")
        print(f"✅ Process query returned: {type(query_result).__name__}")
        print(f"   Response preview: {query_result.response[:100]}...")
        
        # Test 7: Market data analysis
        print("\n🔧 Test 7: Testing market data analysis")
        mock_market_data = {
            'symbol': 'AAPL',
            'current_price': 150.0,
            'volume': 1000000,
            'change': 2.5
        }
        market_analysis = await processor.analyze_market_data(mock_market_data)
        print(f"✅ Market analysis completed")
        print(f"   Analysis preview: {market_analysis[:100]}...")
        
        # Test 8: Symbol extraction
        print("\n🔧 Test 8: Testing symbol extraction")
        symbols = await processor.extract_symbols("I want to buy $AAPL and $TSLA", use_ai=False)
        print(f"✅ Symbol extraction: {symbols}")
        
        # Test 9: Intent detection
        print("\n🔧 Test 9: Testing intent detection")
        intent = await processor.detect_intent("What is the price of Apple stock?")
        print(f"✅ Intent detection: {intent}")
        
        # Test 10: Backward compatibility aliases
        print("\n🔧 Test 10: Testing backward compatibility aliases")
        from src.shared.ai_services.unified_ai_processor import (
            AIChatProcessor,
            AIChatProcessorWrapper,
            CleanAIProcessor,
            create_processor,
            process_query
        )
        
        # Test aliases
        alias_processor = AIChatProcessor()
        wrapper_processor = AIChatProcessorWrapper()
        clean_processor = CleanAIProcessor()
        factory_processor = create_processor()
        
        print("✅ All backward compatibility aliases work")
        
        # Test legacy async function
        legacy_result = await process_query("Test legacy function")
        print(f"✅ Legacy process_query function works: {legacy_result['status']}")
        
        print("\n" + "=" * 50)
        print("🎉 ALL TESTS PASSED!")
        print("✅ UnifiedAIProcessor is ready for migration")
        
        return True
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_interface_compatibility():
    """Test that the unified processor maintains interface compatibility"""
    print("\n🔍 Testing Interface Compatibility")
    print("=" * 50)
    
    try:
        from src.shared.ai_services.unified_ai_processor import UnifiedAIProcessor
        
        processor = UnifiedAIProcessor()
        
        # Test all expected methods exist
        expected_methods = [
            'process',
            'analyze_market_data', 
            'answer_general_question',
            'process_query',
            'extract_symbols',
            'detect_intent'
        ]
        
        for method_name in expected_methods:
            if hasattr(processor, method_name):
                print(f"✅ Method '{method_name}' exists")
            else:
                print(f"❌ Method '{method_name}' missing")
                return False
        
        # Test expected attributes
        expected_attributes = [
            'core_engine',
            'timeout_manager',
            'circuit_breaker',
            'cache',
            'model_router',
            'symbol_extractor',
            'intent_detector',
            'text_parser'
        ]
        
        for attr_name in expected_attributes:
            if hasattr(processor, attr_name):
                print(f"✅ Attribute '{attr_name}' exists")
            else:
                print(f"⚠️  Attribute '{attr_name}' missing (may be optional)")
        
        print("✅ Interface compatibility verified")
        return True
        
    except Exception as e:
        print(f"❌ Interface compatibility test failed: {e}")
        return False

async def main():
    """Run all tests"""
    print("🚀 Starting Unified AI Processor Tests")
    
    # Test 1: Basic functionality
    basic_test = await test_unified_processor()
    
    # Test 2: Interface compatibility
    interface_test = await test_interface_compatibility()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    print(f"Basic Functionality: {'✅ PASS' if basic_test else '❌ FAIL'}")
    print(f"Interface Compatibility: {'✅ PASS' if interface_test else '❌ FAIL'}")
    
    if basic_test and interface_test:
        print("\n🎉 ALL TESTS PASSED - READY FOR MIGRATION!")
        print("🚀 You can now run the migration with:")
        print("   python scripts/migration/migrate_ai_imports.py --execute")
    else:
        print("\n❌ TESTS FAILED - FIX ISSUES BEFORE MIGRATION")
    
    return basic_test and interface_test

if __name__ == "__main__":
    success = asyncio.run(main())
