#!/usr/bin/env python3
"""
Regex Usage Analysis Script

This script analyzes all regex patterns in the codebase and categorizes them
by complexity and AI replacement potential.
"""

import os
import re
import ast
import json
from pathlib import Path
from typing import Dict, List, Tuple, Set
from dataclasses import dataclass
from enum import Enum

class RegexComplexity(Enum):
    """Complexity levels for regex patterns"""
    SIMPLE = "simple"           # Basic patterns easily replaced by AI
    MODERATE = "moderate"       # Patterns that could benefit from AI
    COMPLEX = "complex"         # Complex patterns that might need hybrid approach
    CRITICAL = "critical"       # Security/validation patterns to keep

class AIReplacementPriority(Enum):
    """Priority levels for AI replacement"""
    HIGH = "high"               # Should be replaced with AI
    MEDIUM = "medium"           # Could benefit from AI replacement
    LOW = "low"                 # Minimal benefit from AI replacement
    KEEP = "keep"               # Should remain as regex

@dataclass
class RegexPattern:
    """Represents a regex pattern found in the code"""
    pattern: str
    file_path: str
    line_number: int
    context: str
    function_name: str
    complexity: RegexComplexity
    ai_priority: AIReplacementPriority
    category: str
    description: str

class RegexAnalyzer:
    """Analyzes regex usage in the codebase"""
    
    def __init__(self):
        self.patterns: List[RegexPattern] = []
        self.categories = {
            'symbol_extraction': 'Stock symbol and ticker extraction',
            'price_extraction': 'Price and numerical value extraction',
            'intent_detection': 'Intent and pattern detection',
            'validation': 'Input validation and security',
            'formatting': 'Text formatting and cleaning',
            'parsing': 'Data parsing and extraction',
            'security': 'Security and injection prevention',
            'configuration': 'Configuration and environment parsing'
        }
        
        # Patterns that should definitely be replaced with AI
        self.ai_replacement_candidates = {
            'symbol_extraction': [
                r'\$([A-Z]{1,5})\b',
                r'\b([A-Z]{2,5})\b',
                r'^[A-Z]{1,10}$',
                r'^[A-Z0-9\.\-]{1,10}$'
            ],
            'price_extraction': [
                r'\$(\d+(?:\.\d{2})?)',
                r'(\d+(?:\.\d+)?)\s*%',
                r'(\d+\.?\d*)%',
                r'^\d+(\.\d{1,4})?$'
            ],
            'intent_detection': [
                r'\b(price|current price|quote|value)\b',
                r'\b(indicator values?|indicators?|technical indicators?)\b',
                r'\b(market|sector|broad|overall)\b',
                r'\b(buy|sell|invest|trade|recommendation)\b'
            ],
            'parsing': [
                r'STAGES\s*=\s*\[(.*?)\]',
                r'from\s+\.stages\s+import\s+(.*?)$',
                r'class\s+(\w+)Stage'
            ]
        }
        
        # Patterns that should remain as regex (security, validation)
        self.keep_as_regex = {
            'security': [
                r'(ignore previous instructions|ignore all instructions)',
                r'(\b(select|insert|update|delete|drop|alter)\b.*\b(from|into|table)\b)',
                r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]',
                r'^\d{17,20}$',  # Discord ID validation
                r'^\d{3}[\-\s]?\d{2}[\-\s]?\d{4}$'  # SSN pattern
            ],
            'validation': [
                r'^(postgresql|mysql|sqlite)://',
                r'^redis://',
                r'^[a-zA-Z0-9_]{1,50}$'
            ]
        }
    
    def analyze_codebase(self, root_dir: str = ".") -> List[RegexPattern]:
        """Analyze all Python files in the codebase for regex usage"""
        print("🔍 Analyzing regex usage in codebase...")
        
        python_files = []
        for root, dirs, files in os.walk(root_dir):
            # Skip virtual environment and other non-source directories
            dirs[:] = [d for d in dirs if d not in ['venv', '__pycache__', '.git', 'node_modules']]
            
            for file in files:
                if file.endswith('.py'):
                    python_files.append(os.path.join(root, file))
        
        print(f"📁 Found {len(python_files)} Python files to analyze")
        
        for file_path in python_files:
            self._analyze_file(file_path)
        
        print(f"📊 Found {len(self.patterns)} regex patterns")
        return self.patterns
    
    def _analyze_file(self, file_path: str):
        """Analyze a single Python file for regex patterns"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            # Find all re.* calls
            regex_calls = [
                're.search', 're.match', 're.findall', 're.finditer', 
                're.sub', 're.split', 're.compile'
            ]
            
            for line_num, line in enumerate(lines, 1):
                for call in regex_calls:
                    if call in line:
                        pattern = self._extract_pattern(line)
                        if pattern:
                            context = self._get_context(lines, line_num)
                            function_name = self._get_function_name(lines, line_num)
                            
                            regex_obj = RegexPattern(
                                pattern=pattern,
                                file_path=file_path,
                                line_number=line_num,
                                context=context,
                                function_name=function_name,
                                complexity=self._assess_complexity(pattern),
                                ai_priority=self._assess_ai_priority(pattern, file_path),
                                category=self._categorize_pattern(pattern, file_path),
                                description=self._describe_pattern(pattern)
                            )
                            
                            self.patterns.append(regex_obj)
        
        except Exception as e:
            print(f"⚠️  Error analyzing {file_path}: {e}")
    
    def _extract_pattern(self, line: str) -> str:
        """Extract regex pattern from a line of code"""
        # Look for string literals in regex calls
        patterns = [
            r're\.\w+\(\s*r?[\'"]([^\'"]+)[\'"]',  # re.search(r'pattern')
            r're\.\w+\(\s*([a-zA-Z_]\w*)',        # re.search(pattern_var)
        ]
        
        for pattern in patterns:
            match = re.search(pattern, line)
            if match:
                return match.group(1)
        
        return ""
    
    def _get_context(self, lines: List[str], line_num: int) -> str:
        """Get context around the regex usage"""
        start = max(0, line_num - 3)
        end = min(len(lines), line_num + 2)
        return '\n'.join(lines[start:end])
    
    def _get_function_name(self, lines: List[str], line_num: int) -> str:
        """Get the function name containing the regex"""
        for i in range(line_num - 1, -1, -1):
            line = lines[i].strip()
            if line.startswith('def ') or line.startswith('async def '):
                match = re.search(r'def\s+(\w+)', line)
                if match:
                    return match.group(1)
        return "unknown"
    
    def _assess_complexity(self, pattern: str) -> RegexComplexity:
        """Assess the complexity of a regex pattern"""
        if not pattern:
            return RegexComplexity.SIMPLE
        
        # Simple patterns
        simple_indicators = [
            len(pattern) < 20,
            pattern.count('(') <= 1,
            '\\b' not in pattern,
            '(?:' not in pattern,
            '\\s*' not in pattern or pattern.count('\\s*') <= 2
        ]
        
        # Complex patterns
        complex_indicators = [
            len(pattern) > 50,
            pattern.count('(') > 3,
            '(?:' in pattern,
            '\\b' in pattern and pattern.count('\\b') > 2,
            re.search(r'\[\^[^\]]+\]', pattern),  # Character class negation
            '\\x' in pattern  # Hex escapes
        ]
        
        # Critical patterns (security)
        critical_indicators = [
            'select|insert|update|delete' in pattern.lower(),
            'ignore.*instructions' in pattern.lower(),
            '\\x00-\\x' in pattern,  # Control character filtering
            '\\d{3}[\\-\\s]?\\d{2}' in pattern  # SSN/sensitive data
        ]
        
        if any(critical_indicators):
            return RegexComplexity.CRITICAL
        elif sum(complex_indicators) >= 2:
            return RegexComplexity.COMPLEX
        elif sum(simple_indicators) >= 3:
            return RegexComplexity.SIMPLE
        else:
            return RegexComplexity.MODERATE
    
    def _assess_ai_priority(self, pattern: str, file_path: str) -> AIReplacementPriority:
        """Assess priority for AI replacement"""
        if not pattern:
            return AIReplacementPriority.LOW
        
        # Check if it's a security/validation pattern that should be kept
        for category, patterns in self.keep_as_regex.items():
            for keep_pattern in patterns:
                if keep_pattern in pattern or pattern in keep_pattern:
                    return AIReplacementPriority.KEEP
        
        # Check if it's a good candidate for AI replacement
        for category, patterns in self.ai_replacement_candidates.items():
            for ai_pattern in patterns:
                if ai_pattern in pattern or pattern in ai_pattern:
                    return AIReplacementPriority.HIGH
        
        # File-based priority
        if any(keyword in file_path for keyword in ['symbol', 'extract', 'parse', 'intent']):
            return AIReplacementPriority.HIGH
        elif any(keyword in file_path for keyword in ['security', 'validation', 'sanitiz']):
            return AIReplacementPriority.KEEP
        elif any(keyword in file_path for keyword in ['ai_', 'chat', 'response']):
            return AIReplacementPriority.MEDIUM
        
        return AIReplacementPriority.LOW
    
    def _categorize_pattern(self, pattern: str, file_path: str) -> str:
        """Categorize the regex pattern"""
        if not pattern:
            return 'unknown'
        
        # Symbol patterns
        if any(indicator in pattern for indicator in ['[A-Z]', '$', 'symbol', 'ticker']):
            return 'symbol_extraction'
        
        # Price patterns
        if any(indicator in pattern for indicator in ['\\d+', '%', 'price', '\\$']):
            return 'price_extraction'
        
        # Intent patterns
        if any(indicator in pattern for indicator in ['\\b(', 'buy|sell', 'market|sector']):
            return 'intent_detection'
        
        # Security patterns
        if any(indicator in pattern for indicator in ['select|insert', 'instructions', '\\x']):
            return 'security'
        
        # Validation patterns
        if any(indicator in pattern for indicator in ['^[', ']$', 'postgresql|mysql']):
            return 'validation'
        
        # Parsing patterns
        if any(indicator in pattern for indicator in ['STAGES', 'import', 'class']):
            return 'parsing'
        
        # File-based categorization
        if 'security' in file_path or 'validation' in file_path:
            return 'security'
        elif 'symbol' in file_path or 'extract' in file_path:
            return 'symbol_extraction'
        elif 'format' in file_path or 'clean' in file_path:
            return 'formatting'
        
        return 'parsing'
    
    def _describe_pattern(self, pattern: str) -> str:
        """Generate a human-readable description of the pattern"""
        if not pattern:
            return "Empty pattern"
        
        descriptions = {
            r'\$([A-Z]{1,5})\b': "Extract stock symbols with $ prefix",
            r'\b([A-Z]{2,5})\b': "Extract capitalized stock symbols",
            r'^[A-Z]{1,10}$': "Validate stock symbol format",
            r'(\d+(?:\.\d+)?)\s*%': "Extract percentage values",
            r'\$(\d+(?:\.\d{2})?)': "Extract dollar amounts",
            r'\b(price|quote|value)\b': "Detect price-related keywords",
            r'select|insert|update|delete': "Detect SQL injection patterns",
            r'ignore.*instructions': "Detect prompt injection attempts"
        }
        
        for desc_pattern, description in descriptions.items():
            if desc_pattern in pattern or pattern in desc_pattern:
                return description
        
        # Generic descriptions based on pattern characteristics
        if pattern.startswith('^') and pattern.endswith('$'):
            return f"Validate format: {pattern}"
        elif '\\b(' in pattern and ')\\b' in pattern:
            return f"Detect keywords: {pattern}"
        elif '\\d+' in pattern:
            return f"Extract numbers: {pattern}"
        elif '[A-Z]' in pattern:
            return f"Extract uppercase text: {pattern}"
        else:
            return f"Pattern: {pattern}"
    
    def generate_report(self) -> Dict:
        """Generate a comprehensive analysis report"""
        print("\n📊 Generating regex analysis report...")
        
        # Summary statistics
        total_patterns = len(self.patterns)
        by_complexity = {}
        by_priority = {}
        by_category = {}
        
        for pattern in self.patterns:
            # Count by complexity
            complexity = pattern.complexity.value
            by_complexity[complexity] = by_complexity.get(complexity, 0) + 1
            
            # Count by AI priority
            priority = pattern.ai_priority.value
            by_priority[priority] = by_priority.get(priority, 0) + 1
            
            # Count by category
            category = pattern.category
            by_category[category] = by_category.get(category, 0) + 1
        
        # AI replacement candidates
        ai_candidates = [p for p in self.patterns if p.ai_priority in [AIReplacementPriority.HIGH, AIReplacementPriority.MEDIUM]]
        
        # Files with most regex usage
        file_counts = {}
        for pattern in self.patterns:
            file_counts[pattern.file_path] = file_counts.get(pattern.file_path, 0) + 1
        
        top_files = sorted(file_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        
        report = {
            'summary': {
                'total_patterns': total_patterns,
                'ai_replacement_candidates': len(ai_candidates),
                'replacement_percentage': (len(ai_candidates) / total_patterns * 100) if total_patterns > 0 else 0
            },
            'by_complexity': by_complexity,
            'by_priority': by_priority,
            'by_category': by_category,
            'top_files': top_files,
            'ai_candidates': [
                {
                    'pattern': p.pattern,
                    'file': p.file_path,
                    'line': p.line_number,
                    'function': p.function_name,
                    'category': p.category,
                    'description': p.description,
                    'priority': p.ai_priority.value
                }
                for p in ai_candidates[:20]  # Top 20 candidates
            ],
            'detailed_patterns': [
                {
                    'pattern': p.pattern,
                    'file': p.file_path,
                    'line': p.line_number,
                    'function': p.function_name,
                    'complexity': p.complexity.value,
                    'ai_priority': p.ai_priority.value,
                    'category': p.category,
                    'description': p.description
                }
                for p in self.patterns
            ]
        }
        
        return report
    
    def print_summary(self, report: Dict):
        """Print a summary of the analysis"""
        print("\n" + "="*80)
        print("📋 REGEX USAGE ANALYSIS SUMMARY")
        print("="*80)
        
        summary = report['summary']
        print(f"\n📊 Overall Statistics:")
        print(f"   Total regex patterns found: {summary['total_patterns']}")
        print(f"   AI replacement candidates: {summary['ai_replacement_candidates']}")
        print(f"   Replacement potential: {summary['replacement_percentage']:.1f}%")
        
        print(f"\n🎯 By AI Replacement Priority:")
        for priority, count in report['by_priority'].items():
            emoji = {"high": "🔴", "medium": "🟡", "low": "🟢", "keep": "🔒"}.get(priority, "⚪")
            print(f"   {emoji} {priority.title()}: {count}")
        
        print(f"\n📂 By Category:")
        for category, count in report['by_category'].items():
            print(f"   📁 {category}: {count}")
        
        print(f"\n🏆 Top Files with Regex Usage:")
        for file_path, count in report['top_files'][:5]:
            short_path = file_path.replace('./', '').replace('src/', '')
            print(f"   📄 {short_path}: {count} patterns")
        
        print(f"\n🎯 Top AI Replacement Candidates:")
        for candidate in report['ai_candidates'][:5]:
            print(f"   🔄 {candidate['category']}: {candidate['pattern']}")
            print(f"      📄 {candidate['file']}:{candidate['line']}")
            print(f"      📝 {candidate['description']}")

def main():
    """Main function to run regex analysis"""
    analyzer = RegexAnalyzer()
    
    # Analyze the codebase
    patterns = analyzer.analyze_codebase()
    
    # Generate report
    report = analyzer.generate_report()
    
    # Print summary
    analyzer.print_summary(report)
    
    # Save detailed report
    output_file = Path("docs/audit/regex_analysis_report.json")
    output_file.parent.mkdir(parents=True, exist_ok=True)
    
    with open(output_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n📄 Detailed report saved to {output_file}")
    
    return report

if __name__ == "__main__":
    main()
