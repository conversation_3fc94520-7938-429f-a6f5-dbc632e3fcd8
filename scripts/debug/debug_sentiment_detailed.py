import asyncio
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.abspath('.'))

from src.bot.pipeline.commands.ask.stages.ml_sentiment_analyzer import MLSentimentAnalyzer

async def debug_sentiment_detailed():
    analyzer = MLSentimentAnalyzer()
    
    # Test query with mixed sentiment
    query = "AAPL has strong growth but faces significant risks with weak margins"
    print("Query:", query)
    
    # Preprocess text
    processed_text = analyzer._preprocess_text(query)
    print("Processed text:", processed_text)
    
    # Check which tokens are in word embeddings
    for token in processed_text:
        if token in analyzer.word_embeddings:
            embedding = analyzer.word_embeddings[token]
            weighted_embedding = [embedding[i] * analyzer.sentiment_weights.get("dimension_weights", [0.7, 0.3])[i] for i in range(len(embedding))]
            score = sum(weighted_embedding)
            print(f"'{token}': embedding={embedding}, weighted={weighted_embedding}, score={score}")
    
    result = await analyzer.analyze_sentiment(query)
    print("\nResult:", result)

if __name__ == "__main__":
    asyncio.run(debug_sentiment_detailed())