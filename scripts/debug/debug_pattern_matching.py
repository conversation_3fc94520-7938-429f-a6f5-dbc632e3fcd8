#!/usr/bin/env python3
"""
Debug the pattern matching logic
"""

def debug_pattern_matching():
    """Debug why pattern matching isn't working"""
    
    query = "find me a stock you are bullish on this week"
    cleaned_query = query.strip().lower()
    
    print(f"Original query: '{query}'")
    print(f"Cleaned query: '{cleaned_query}'")
    print()
    
    # Test each pattern
    patterns = {
        'price': ['price', 'cost', 'value', 'worth', 'trading at', 'current price'],
        'trading_advice': ['bullish', 'bearish', 'recommend', 'suggest', 'advice', 'trading', 'strategy', 'buy', 'sell', 'hold'],
        'greeting': ['hello', 'hi', 'hey', 'good morning', 'good afternoon', 'good evening'],
        'help': ['help', 'what can you do', 'commands', 'how to']
    }
    
    for pattern_name, words in patterns.items():
        matches = [word for word in words if word in cleaned_query]
        print(f"{pattern_name}: {matches}")
    
    print()
    print("Expected: Should match 'trading_advice' with ['bullish', 'trading']")
    print("Actual: Let's see what it matches...")

if __name__ == "__main__":
    debug_pattern_matching()
