#!/usr/bin/env python3
"""
Simple test to verify sensitive data detection is working
"""

import os
import sys

# Ensure src is importable
ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if ROOT not in sys.path:
    sys.path.append(ROOT)

from src.bot.utils.enhanced_input_validator import EnhancedInputValidator, ValidationLevel, ThreatLevel

def simple_test():
    """Simple test of sensitive data detection"""
    print("🔍 SIMPLE SENSITIVE DATA TEST")
    print("=" * 40)
    
    validator = EnhancedInputValidator(ValidationLevel.STRICT)
    
    # Test the problematic case
    test_input = "My password is: admin123"
    result = validator.validate_input(test_input, "ask", "test_user", "query")
    
    print(f"Input: {test_input}")
    print(f"Threat Level: {result.threat_level.name} ({result.threat_level.value})")
    print(f"Issues: {result.issues}")
    print(f"Is Valid: {result.is_valid}")
    
    # Check the test condition
    if result.threat_level.value < ThreatLevel.HIGH.value:
        print("❌ WOULD FAIL TEST: Threat level too low")
    else:
        print("✅ WOULD PASS TEST: Threat level HIGH or higher")

if __name__ == "__main__":
    simple_test()
