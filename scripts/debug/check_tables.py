#!/usr/bin/env python3
"""
Check what tables exist in your Supabase database
"""

import asyncio
from supabase import create_client

# Your credentials
SUPABASE_URL = "https://sgxjackuhalscowqrulv.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNneGphY2t1aGFsc2Nvd3FydWx2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5NjExNDMsImV4cCI6MjA2NTUzNzE0M30.-gBZv9TWmb4nkyqhpaZzRtg6BY1lPArnz7QBOehh8sE"

async def check_available_tables():
    """Check what tables are available"""
    print("🔍 Checking Available Tables")
    print("=" * 40)
    
    try:
        supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
        print("✅ Connected to Supabase")
        
        # Try to get table information
        try:
            # Try to list tables (this might not work with anon key)
            result = supabase.table('_supabase_migrations').select('*').limit(1).execute()
            print("✅ System tables accessible")
        except Exception as e:
            print(f"⚠️  System tables not accessible: {e}")
        
        # Try some common table names
        common_tables = [
            'trading_alerts',
            'trading_halts', 
            'alerts',
            'webhooks',
            'users',
            'health_check'
        ]
        
        print(f"\n🔍 Checking common table names:")
        available_tables = []
        
        for table_name in common_tables:
            try:
                result = supabase.table(table_name).select('*').limit(1).execute()
                print(f"   ✅ {table_name}: Found ({len(result.data)} rows)")
                available_tables.append(table_name)
            except Exception as e:
                error_msg = str(e)
                if "Could not find the table" in error_msg:
                    print(f"   ❌ {table_name}: Not found")
                elif "permission denied" in error_msg.lower():
                    print(f"   ⚠️  {table_name}: Exists but no permission")
                else:
                    print(f"   ❓ {table_name}: Error - {error_msg[:50]}...")
        
        return available_tables
        
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return []

async def create_test_table():
    """Try to create a test table"""
    print(f"\n🔧 Creating Test Table")
    print("-" * 40)
    
    try:
        supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
        
        # Try to create a simple table (this will likely fail with anon key)
        try:
            # This is a SQL command that might work
            result = supabase.rpc('create_test_table').execute()
            print("✅ Test table created successfully")
            return True
        except Exception as e:
            print(f"⚠️  Cannot create table with anon key: {e}")
            print("   This is normal - you need service role key for table creation")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def show_solution():
    """Show the solution"""
    print(f"\n🎯 SOLUTION:")
    print("=" * 40)
    
    print("\n1️⃣  Current Status:")
    print("   ✅ Anon key works (public access)")
    print("   ❌ Need service role key for table operations")
    print("   ❌ Tables don't exist yet")
    
    print("\n2️⃣  What You Need to Do:")
    print("   a) Get Service Role Key from Supabase Dashboard")
    print("   b) Create tables in Supabase Dashboard")
    print("   c) Update your .env file")
    
    print("\n3️⃣  Quick Fix - Use Existing Tables:")
    print("   - Check what tables already exist")
    print("   - Use those tables for testing")
    print("   - Create new tables when you have service role key")

async def main():
    """Main function"""
    print("🚀 Supabase Table Checker")
    print("=" * 50)
    
    available_tables = await check_available_tables()
    await create_test_table()
    
    print(f"\n📊 Summary:")
    if available_tables:
        print(f"   Found {len(available_tables)} accessible tables:")
        for table in available_tables:
            print(f"   - {table}")
    else:
        print("   No accessible tables found")
    
    show_solution()

if __name__ == "__main__":
    asyncio.run(main())
