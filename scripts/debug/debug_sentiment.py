import asyncio
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.abspath('.'))

from src.bot.pipeline.commands.ask.stages.ml_sentiment_analyzer import MLSentimentAnalyzer

async def debug_sentiment():
    analyzer = MLSentimentAnalyzer()
    
    # Test query with mixed sentiment
    query = "AAPL has strong growth but faces significant risks with weak margins"
    result = await analyzer.analyze_sentiment(query)
    
    print("Query:", query)
    print("Result:", result)
    print("Positive words:", result.get("positive_words", []))
    print("Negative words:", result.get("negative_words", []))
    print("Neutral words:", result.get("neutral_words", []))

if __name__ == "__main__":
    asyncio.run(debug_sentiment())