#!/usr/bin/env python3
"""
Check the actual structure of existing tables
"""

import asyncio
from supabase import create_client

# Your credentials
SUPABASE_URL = "https://sgxjackuhalscowqrulv.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNneGphY2t1aGFsc2Nvd3FydWx2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5NjExNDMsImV4cCI6MjA2NTUzNzE0M30.-gBZv9TWmb4nkyqhpaZzRtg6BY1lPArnz7QBOehh8sE"

def get_supabase_client():
    return create_client(SUPABASE_URL, SUPABASE_KEY)

async def check_table_structure():
    """Check the structure of existing tables"""
    print("🔍 Checking Table Structure")
    print("=" * 40)
    
    supabase = get_supabase_client()
    
    tables = ['trading_halts', 'alerts', 'webhooks', 'users']
    
    for table_name in tables:
        print(f"\n📊 Table: {table_name}")
        print("-" * 30)
        
        try:
            # Get a sample record to see the structure
            result = supabase.table(table_name).select('*').limit(1).execute()
            
            if result.data:
                print(f"   ✅ Found {len(result.data)} sample record(s)")
                sample = result.data[0]
                print(f"   📋 Columns: {list(sample.keys())}")
                
                # Show sample data
                for key, value in sample.items():
                    print(f"      {key}: {value}")
            else:
                print(f"   📋 Table is empty - checking with minimal insert")
                
                # Try to insert minimal data to see what columns are required
                try:
                    minimal_data = {'id': 1}  # Most tables have an id
                    insert_result = supabase.table(table_name).insert(minimal_data).execute()
                    print(f"   ✅ Minimal insert successful")
                    print(f"   📋 Columns: {list(insert_result.data[0].keys())}")
                except Exception as e:
                    print(f"   ⚠️  Minimal insert failed: {str(e)[:100]}...")
                    
        except Exception as e:
            print(f"   ❌ Error: {str(e)[:100]}...")

async def test_webhooks_table():
    """Test the webhooks table specifically (it has data)"""
    print(f"\n🔹 Testing Webhooks Table")
    print("-" * 40)
    
    supabase = get_supabase_client()
    
    try:
        # Get existing webhooks
        result = supabase.table('webhooks').select('*').limit(3).execute()
        print(f"📊 Found {len(result.data)} webhooks")
        
        if result.data:
            sample = result.data[0]
            print(f"📋 Sample webhook structure:")
            for key, value in sample.items():
                print(f"   {key}: {value}")
            
            # Try to insert a new webhook using the same structure
            try:
                new_webhook = {
                    'id': len(result.data) + 1,  # Simple ID
                    'created_at': '2024-01-01T00:00:00Z'
                }
                
                # Copy structure from existing record
                for key in sample.keys():
                    if key not in new_webhook and key != 'id':
                        new_webhook[key] = f'test_{key}'
                
                insert_result = supabase.table('webhooks').insert(new_webhook).execute()
                print(f"✅ Successfully inserted test webhook")
                print(f"   New record: {insert_result.data[0]}")
                
            except Exception as e:
                print(f"❌ Insert failed: {e}")
        
    except Exception as e:
        print(f"❌ Error: {e}")

async def main():
    """Main function"""
    print("🚀 Table Structure Checker")
    print("=" * 50)
    
    await check_table_structure()
    await test_webhooks_table()
    
    print(f"\n🎯 SUMMARY:")
    print("=" * 40)
    print("✅ Tables exist and are accessible")
    print("✅ Can read from all tables")
    print("⚠️  Need to match exact column structure for inserts")
    print("💡 Use the webhooks table for testing - it has data and works!")

if __name__ == "__main__":
    asyncio.run(main())
