#!/usr/bin/env python3
"""
Test script to debug AST parsing of command decorators.
"""

import ast

# Sample code to parse
sample_code = '''
import discord
from discord.ext import commands
from discord import app_commands

class OptimizedAskCommand(commands.Cog):
    """Optimized AI-powered ask command with real performance improvements."""

    @app_commands.command(name="ask", description="Ask the AI about trading and markets (Optimized)")
    @app_commands.describe(
        query="Your question about trading and markets",
        attachment="Voice message attachment (optional)"
    )
    async def ask_command(self, interaction: discord.Interaction, query: str = None, attachment: discord.Attachment = None):
        """Ask the AI about trading and markets with real performance optimizations"""
        pass

async def setup(bot: commands.Bot):
    """Setup the optimized ask command extension"""
    await bot.add_cog(OptimizedAskCommand(bot))
'''

def debug_ast(content):
    """Debug the AST structure."""
    try:
        tree = ast.parse(content)
        
        # Print the AST structure
        print("AST structure:")
        print(ast.dump(tree, indent=2))
        
    except Exception as e:
        print(f"Error parsing: {e}")
        import traceback
        traceback.print_exc()

# Test the function
debug_ast(sample_code)