#!/usr/bin/env python3
"""
Debug the data format to see what Yahoo Finance is returning
"""

import asyncio
import sys
sys.path.insert(0, '/home/<USER>/Desktop/tradingview-automatio')

async def debug_data_format():
    """Debug what data format we're getting"""
    
    try:
        from src.api.data.market_data_service import MarketDataService
        
        print("🔍 Debugging Market Data Format")
        print("=" * 50)
        
        market_service = MarketDataService()
        data = await market_service.get_comprehensive_stock_data("NVDA")
        
        print("Raw data structure:")
        print(f"Type: {type(data)}")
        print(f"Keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
        print()
        
        if isinstance(data, dict):
            for key, value in data.items():
                print(f"{key}: {type(value)} = {value}")
                print()
        
        print("Looking for price_data...")
        if 'price_data' in data:
            price_data = data['price_data']
            print(f"price_data type: {type(price_data)}")
            print(f"price_data keys: {list(price_data.keys()) if isinstance(price_data, dict) else 'Not a dict'}")
            
            if isinstance(price_data, dict) and 'close' in price_data:
                close_prices = price_data['close']
                print(f"close prices: {close_prices[:5]}... (length: {len(close_prices)})")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_data_format())
