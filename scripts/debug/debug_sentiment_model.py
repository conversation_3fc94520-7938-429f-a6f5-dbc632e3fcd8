import asyncio
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.abspath('.'))

from src.bot.pipeline.commands.ask.stages.ml_sentiment_analyzer import MLSentimentAnalyzer

async def debug_sentiment_model():
    analyzer = MLSentimentAnalyzer()
    
    print("Model loaded:", analyzer.model_loaded)
    print("Word embeddings keys:", list(analyzer.word_embeddings.keys()))
    print("'risks' in word_embeddings:", "risks" in analyzer.word_embeddings)
    
    if "risks" in analyzer.word_embeddings:
        print("risks embedding:", analyzer.word_embeddings["risks"])
    else:
        print("Available keys with 'risk' in them:", [k for k in analyzer.word_embeddings.keys() if "risk" in k])

if __name__ == "__main__":
    asyncio.run(debug_sentiment_model())