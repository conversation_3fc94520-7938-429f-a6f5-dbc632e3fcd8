#!/usr/bin/env python3
"""
Debug sensitive data detection in test vs actual implementation
"""

import os
import sys
import asyncio

# Ensure src is importable
ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if ROOT not in sys.path:
    sys.path.append(ROOT)

from src.bot.utils.enhanced_input_validator import EnhancedInputValidator, ValidationLevel, ThreatLevel

async def debug_sensitive_data_test():
    """Debug why sensitive data tests are failing"""
    print("🔍 DEBUGGING SENSITIVE DATA TEST FAILURES")
    print("=" * 60)
    
    validator = EnhancedInputValidator(ValidationLevel.STRICT)
    
    # Test the exact sensitive data from the test suite
    sensitive_data_tests = [
        "My password is: admin123",
        "API key: sk-1234567890abcdef1234567890abcdef",
        "Credit card: 4532-1234-5678-9012",
        "SSN: ***********",
        "Email: <EMAIL> with password: secret123",
        "AWS_ACCESS_KEY_ID=AKIAIOSFODNN7EXAMPLE",
        "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9",
        "Phone: ************",
        "IP: ************* with admin:password",
        "https://user:<EMAIL>/secret"
    ]
    
    test_user_id = "security_test_user"
    
    for i, attack in enumerate(sensitive_data_tests, 1):
        print(f"\n🧪 Test {i}: {attack[:50]}...")
        
        # Test with enhanced validator (same as test suite)
        validation_result = await validator.validate_input(
            attack, "ask", test_user_id, "query"
        )
        
        print(f"   Threat Level: {validation_result.threat_level.name} ({validation_result.threat_level.value})")
        print(f"   Is Valid: {validation_result.is_valid}")
        print(f"   Issues: {validation_result.issues}")
        
        # Apply the exact test logic from the test suite
        should_detect_sensitive = True  # This is 'sensitive_data' category
        
        # This is the exact condition from the test suite
        if should_detect_sensitive and validation_result.threat_level.value < ThreatLevel.HIGH.value:
            print(f"   ❌ TEST WOULD FAIL: Threat level {validation_result.threat_level.value} < {ThreatLevel.HIGH.value}")
        else:
            print(f"   ✅ TEST WOULD PASS: Threat level {validation_result.threat_level.value} >= {ThreatLevel.HIGH.value}")
        
        print("-" * 60)

if __name__ == "__main__":
    asyncio.run(debug_sensitive_data_test())
