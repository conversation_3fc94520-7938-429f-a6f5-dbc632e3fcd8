#!/usr/bin/env python3
"""
Debug script to understand threat level assignment in EnhancedInputValidator
"""

import os
import sys

# Ensure src is importable
ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if ROOT not in sys.path:
    sys.path.append(ROOT)

from src.bot.utils.enhanced_input_validator import EnhancedInputValidator, ValidationLevel, ThreatLevel

def debug_threat_level_assignment():
    """Debug the threat level assignment for sensitive data"""
    print("🔍 DEBUGGING THREAT LEVEL ASSIGNMENT")
    print("=" * 60)
    
    validator = EnhancedInputValidator(ValidationLevel.STRICT)
    
    # Test cases that should trigger HIGH threat level
    test_cases = [
        "My password is: admin123",
        "API key: sk-1234567890abcdef1234567890abcdef",
        "Credit card: 4532-1234-5678-9012",
        "SSN: ***********",
        "Email: <EMAIL> with password: secret123",
        "AWS_ACCESS_KEY_ID=AKIAIOSFODNN7EXAMPLE",
        "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9",
        "Phone: ************",
        "IP: ************* with admin:password",
        "https://user:<EMAIL>/secret"
    ]
    
    for i, test_input in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}: {test_input}")
        
        # Test sensitive information detection directly
        sensitive_issues = validator._detect_sensitive_information(test_input)
        print(f"   Sensitive Issues Detected: {sensitive_issues}")
        
        # Test full validation
        result = validator.validate_input(test_input, "ask", "debug_user", "query")
        print(f"   Final Threat Level: {result.threat_level.name} ({result.threat_level.value})")
        print(f"   All Issues: {result.issues}")
        print(f"   Is Valid: {result.is_valid}")
        
        # Check if HIGH threat level was assigned correctly
        if sensitive_issues and result.threat_level.value < ThreatLevel.HIGH.value:
            print(f"   ❌ BUG: Sensitive data detected but threat level is {result.threat_level.name}")
        elif sensitive_issues and result.threat_level.value >= ThreatLevel.HIGH.value:
            print(f"   ✅ CORRECT: Sensitive data properly flagged as {result.threat_level.name}")
        elif not sensitive_issues:
            print(f"   ⚠️  WARNING: No sensitive data detected")
        
        print("-" * 60)

def debug_pattern_matching():
    """Debug pattern matching for sensitive information"""
    print("\n🔍 DEBUGGING PATTERN MATCHING")
    print("=" * 60)
    
    validator = EnhancedInputValidator()
    
    # Test individual patterns
    test_cases = [
        ("My password is: admin123", "passwords"),
        ("API key: sk-1234567890abcdef1234567890abcdef", "api_keys"),
        ("Phone: ************", "phone_numbers"),
        ("IP: *************", "ip_addresses"),
    ]
    
    for test_input, expected_pattern in test_cases:
        print(f"\n📝 Testing: {test_input}")
        print(f"   Expected Pattern: {expected_pattern}")
        
        # Test each pattern individually
        for pattern_name, pattern in validator.sensitive_patterns.items():
            import re
            if re.search(pattern, test_input, re.IGNORECASE):
                print(f"   ✅ Matched Pattern: {pattern_name}")
            else:
                print(f"   ❌ No Match: {pattern_name}")
        
        # Test decode/normalize
        normalized = validator._decode_and_normalize_input(test_input)
        print(f"   Normalized Input: {normalized}")
        
        # Test detection method
        issues = validator._detect_sensitive_information(test_input)
        print(f"   Detection Result: {issues}")

if __name__ == "__main__":
    debug_threat_level_assignment()
    debug_pattern_matching()
