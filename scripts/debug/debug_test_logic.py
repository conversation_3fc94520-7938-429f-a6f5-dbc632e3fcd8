#!/usr/bin/env python3
"""
Debug the test logic to understand why tests are still failing
"""

import os
import sys

# Ensure src is importable
ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if ROOT not in sys.path:
    sys.path.append(ROOT)

from src.bot.utils.enhanced_input_validator import EnhancedInputValidator, ValidationLevel, Thr<PERSON>Level

def debug_test_logic():
    """Debug the exact test logic used in the security test suite"""
    print("🔍 DEBUGGING TEST LOGIC")
    print("=" * 60)
    
    enhanced_validator = EnhancedInputValidator(ValidationLevel.STRICT)
    
    # Test the exact sensitive data attacks from the test suite
    sensitive_attacks = [
        "My password is: admin123",
        "API key: sk-1234567890abcdef1234567890abcdef",
        "Credit card: 4532-1234-5678-9012",
        "SSN: ***********",
        "Email: <EMAIL> with password: secret123",
        "AWS_ACCESS_KEY_ID=AKIAIOSFODNN7EXAMPLE",
        "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9",
        "Phone: ************",
        "IP: ************* with admin:password",
        "https://user:<EMAIL>/secret"
    ]
    
    test_user_id = "security_test_user"
    
    for i, attack in enumerate(sensitive_attacks, 1):
        print(f"\n🧪 Test {i}: {attack[:50]}...")
        
        # Test with enhanced validator (same as test suite)
        validation_result = enhanced_validator.validate_input(
            attack, "ask", test_user_id, "query"
        )
        
        print(f"   Threat Level: {validation_result.threat_level.name} ({validation_result.threat_level.value})")
        print(f"   Is Valid: {validation_result.is_valid}")
        print(f"   Issues: {validation_result.issues}")
        
        # Apply the exact test logic
        should_detect_sensitive = True  # This is 'sensitive_data' category
        
        if should_detect_sensitive and validation_result.threat_level.value < ThreatLevel.HIGH.value:
            print(f"   ❌ TEST LOGIC: Sensitive data not properly flagged")
        else:
            print(f"   ✅ TEST LOGIC: Passed")
        
        print("-" * 60)

if __name__ == "__main__":
    debug_test_logic()
