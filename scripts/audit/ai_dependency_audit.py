#!/usr/bin/env python3
"""
AI Service Dependency Audit Script

Scans the entire codebase to identify which AI service files are actively used
and maps their dependencies to understand the current architecture.
"""

import os
import re
import ast
import json
from pathlib import Path
from typing import Dict, List, Set, Tuple, Any
from collections import defaultdict, Counter
from dataclasses import dataclass

@dataclass
class ImportInfo:
    """Information about an import statement"""
    file_path: str
    line_number: int
    import_statement: str
    imported_module: str
    imported_names: List[str]
    import_type: str  # 'from', 'import', 'relative'

@dataclass
class AIFileInfo:
    """Information about an AI service file"""
    file_path: str
    file_size: int
    line_count: int
    class_count: int
    function_count: int
    import_count: int
    is_imported: bool
    import_frequency: int
    classes: List[str]
    functions: List[str]
    dependencies: List[str]

class AIDependencyAuditor:
    """Audits AI service dependencies across the codebase"""
    
    def __init__(self, root_dir: str = "."):
        self.root_dir = Path(root_dir)
        self.ai_service_dir = self.root_dir / "src" / "shared" / "ai_services"
        self.imports_map: Dict[str, List[ImportInfo]] = defaultdict(list)
        self.ai_files: Dict[str, AIFileInfo] = {}
        self.usage_stats: Dict[str, int] = Counter()
        
    def run_audit(self) -> Dict[str, Any]:
        """Run complete AI dependency audit"""
        print("🔍 Starting AI Service Dependency Audit...")
        
        # Step 1: Discover all AI service files
        ai_files = self._discover_ai_files()
        print(f"📁 Found {len(ai_files)} AI service files")
        
        # Step 2: Analyze each AI file
        for ai_file in ai_files:
            self._analyze_ai_file(ai_file)
        
        # Step 3: Scan entire codebase for AI imports
        self._scan_codebase_for_imports()
        
        # Step 4: Calculate usage statistics
        self._calculate_usage_stats()
        
        # Step 5: Generate audit report
        return self._generate_audit_report()
    
    def _discover_ai_files(self) -> List[Path]:
        """Discover all AI service files"""
        ai_files = []
        
        if self.ai_service_dir.exists():
            for file_path in self.ai_service_dir.rglob("*.py"):
                if file_path.name != "__init__.py":
                    ai_files.append(file_path)
        
        return ai_files
    
    def _analyze_ai_file(self, file_path: Path):
        """Analyze an individual AI service file"""
        try:
            content = file_path.read_text(encoding='utf-8')
            
            # Parse AST to extract metadata
            tree = ast.parse(content)
            
            classes = []
            functions = []
            imports = []
            
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    classes.append(node.name)
                elif isinstance(node, ast.FunctionDef):
                    functions.append(node.name)
                elif isinstance(node, (ast.Import, ast.ImportFrom)):
                    if isinstance(node, ast.ImportFrom) and node.module:
                        imports.append(node.module)
                    elif isinstance(node, ast.Import):
                        for alias in node.names:
                            imports.append(alias.name)
            
            # Create file info
            relative_path = str(file_path.relative_to(self.root_dir))
            self.ai_files[relative_path] = AIFileInfo(
                file_path=relative_path,
                file_size=file_path.stat().st_size,
                line_count=len(content.splitlines()),
                class_count=len(classes),
                function_count=len(functions),
                import_count=len(imports),
                is_imported=False,  # Will be updated during import scan
                import_frequency=0,  # Will be updated during import scan
                classes=classes,
                functions=functions,
                dependencies=imports
            )
            
        except Exception as e:
            print(f"❌ Error analyzing {file_path}: {e}")
    
    def _scan_codebase_for_imports(self):
        """Scan entire codebase for AI service imports"""
        print("🔍 Scanning codebase for AI imports...")
        
        # Scan all Python files
        for file_path in self.root_dir.rglob("*.py"):
            if "venv" in str(file_path) or "__pycache__" in str(file_path):
                continue
                
            try:
                self._scan_file_for_imports(file_path)
            except Exception as e:
                print(f"❌ Error scanning {file_path}: {e}")
    
    def _scan_file_for_imports(self, file_path: Path):
        """Scan a single file for AI service imports"""
        try:
            content = file_path.read_text(encoding='utf-8')
            lines = content.splitlines()
            
            # Parse AST for imports
            tree = ast.parse(content)
            
            for node in ast.walk(tree):
                if isinstance(node, (ast.Import, ast.ImportFrom)):
                    self._process_import_node(node, file_path, lines)
                    
        except Exception as e:
            # Skip files with syntax errors
            pass
    
    def _process_import_node(self, node: ast.AST, file_path: Path, lines: List[str]):
        """Process an import node to check if it's an AI service import"""
        try:
            if isinstance(node, ast.ImportFrom):
                module = node.module or ""
                if "ai_services" in module or "ai_chat" in module:
                    imported_names = [alias.name for alias in node.names]
                    import_statement = lines[node.lineno - 1].strip() if node.lineno <= len(lines) else ""
                    
                    import_info = ImportInfo(
                        file_path=str(file_path.relative_to(self.root_dir)),
                        line_number=node.lineno,
                        import_statement=import_statement,
                        imported_module=module,
                        imported_names=imported_names,
                        import_type="from"
                    )
                    
                    self.imports_map[module].append(import_info)
                    self.usage_stats[module] += 1
                    
            elif isinstance(node, ast.Import):
                for alias in node.names:
                    module = alias.name
                    if "ai_services" in module or "ai_chat" in module:
                        import_statement = lines[node.lineno - 1].strip() if node.lineno <= len(lines) else ""
                        
                        import_info = ImportInfo(
                            file_path=str(file_path.relative_to(self.root_dir)),
                            line_number=node.lineno,
                            import_statement=import_statement,
                            imported_module=module,
                            imported_names=[alias.asname or alias.name],
                            import_type="import"
                        )
                        
                        self.imports_map[module].append(import_info)
                        self.usage_stats[module] += 1
                        
        except Exception as e:
            # Skip problematic imports
            pass
    
    def _calculate_usage_stats(self):
        """Calculate usage statistics for AI files"""
        # Update AI file info with usage data
        for file_path, file_info in self.ai_files.items():
            # Convert file path to module path
            module_path = file_path.replace("/", ".").replace(".py", "")
            
            # Check if this file is imported
            total_imports = 0
            for module, imports in self.imports_map.items():
                if module_path in module or file_path in module:
                    total_imports += len(imports)
                    file_info.is_imported = True
            
            file_info.import_frequency = total_imports
    
    def _generate_audit_report(self) -> Dict[str, Any]:
        """Generate comprehensive audit report"""
        # Sort files by usage frequency
        used_files = [(path, info) for path, info in self.ai_files.items() if info.is_imported]
        unused_files = [(path, info) for path, info in self.ai_files.items() if not info.is_imported]
        
        used_files.sort(key=lambda x: x[1].import_frequency, reverse=True)
        
        # Calculate statistics
        total_files = len(self.ai_files)
        used_count = len(used_files)
        unused_count = len(unused_files)
        
        # Most imported modules
        top_imports = self.usage_stats.most_common(10)
        
        return {
            "summary": {
                "total_ai_files": total_files,
                "used_files": used_count,
                "unused_files": unused_count,
                "usage_percentage": round((used_count / total_files) * 100, 1) if total_files > 0 else 0,
                "total_import_statements": sum(self.usage_stats.values())
            },
            "used_files": [
                {
                    "file": path,
                    "import_frequency": info.import_frequency,
                    "classes": info.classes,
                    "functions": info.functions,
                    "line_count": info.line_count,
                    "file_size": info.file_size
                }
                for path, info in used_files
            ],
            "unused_files": [
                {
                    "file": path,
                    "classes": info.classes,
                    "functions": info.functions,
                    "line_count": info.line_count,
                    "file_size": info.file_size
                }
                for path, info in unused_files
            ],
            "top_imported_modules": [
                {"module": module, "import_count": count}
                for module, count in top_imports
            ],
            "import_details": {
                module: [
                    {
                        "file": imp.file_path,
                        "line": imp.line_number,
                        "statement": imp.import_statement,
                        "names": imp.imported_names
                    }
                    for imp in imports
                ]
                for module, imports in self.imports_map.items()
            }
        }

def main():
    """Run the AI dependency audit"""
    auditor = AIDependencyAuditor()
    report = auditor.run_audit()
    
    # Save report to file
    output_file = "docs/audit/ai_dependency_audit_report.json"
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    with open(output_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    # Print summary
    print("\n" + "="*60)
    print("🤖 AI SERVICE DEPENDENCY AUDIT RESULTS")
    print("="*60)
    
    summary = report["summary"]
    print(f"📊 Total AI Files: {summary['total_ai_files']}")
    print(f"✅ Used Files: {summary['used_files']} ({summary['usage_percentage']}%)")
    print(f"❌ Unused Files: {summary['unused_files']}")
    print(f"📈 Total Import Statements: {summary['total_import_statements']}")
    
    print(f"\n📋 Report saved to: {output_file}")

if __name__ == "__main__":
    main()
