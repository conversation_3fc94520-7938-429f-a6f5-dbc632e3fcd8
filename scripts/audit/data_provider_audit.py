#!/usr/bin/env python3
"""
Data Provider Status Audit Script

Comprehensive audit of all data providers to identify:
- Which providers are working/failing
- Which providers are commented out
- API key configuration status
- Provider health and performance
"""

import asyncio
import sys
import os
import logging
from pathlib import Path

# Add root to path
ROOT = Path(__file__).parent.parent.parent
sys.path.insert(0, str(ROOT))

# Configure logging
logging.basicConfig(level=logging.WARNING)  # Reduce noise

async def audit_environment_config():
    """Audit environment configuration for data providers"""
    print("📋 Environment Configuration Audit")
    print("=" * 50)
    
    env_vars = {
        'POLYGON_API_KEY': 'Polygon.io API key',
        'FINNHUB_API_KEY': 'Finnhub API key', 
        'ALPHA_VANTAGE_API_KEY': 'Alpha Vantage API key',
        'YAHOO_FINANCE_API_KEY': 'Yahoo Finance API key (optional)',
        'ALPACA_API_KEY': 'Alpaca API key',
        'ALPACA_SECRET_KEY': 'Alpaca secret key'
    }
    
    config_status = {}
    for var, description in env_vars.items():
        value = os.getenv(var)
        status = 'SET' if value else 'MISSING'
        emoji = '✅' if value else '❌'
        print(f"  {var}: {emoji} {status}")
        config_status[var] = bool(value)
    
    return config_status

async def audit_data_source_manager():
    """Audit the main data source manager"""
    print("\n🔧 Data Source Manager Audit")
    print("=" * 50)
    
    try:
        from src.api.data.providers.data_source_manager import DataSourceManager
        manager = DataSourceManager()
        print("✅ DataSourceManager imported successfully")
        
        # Check provider health
        health = manager.get_health_status()
        print("\n🏥 Provider Health Status:")
        
        provider_status = {}
        for provider, status in health['providers'].items():
            emoji = '✅' if status else '❌'
            print(f"  {provider}: {emoji} {'HEALTHY' if status else 'UNHEALTHY'}")
            provider_status[provider] = status
        
        # Test data fetch
        print("\n📊 Testing Data Fetch (AAPL):")
        try:
            result = await manager.get_market_data('AAPL')
            if result['status'] == 'success':
                provider = result.get('provider', 'unknown')
                price = result.get('data', {}).get('price', 'N/A')
                print(f"✅ Data fetch successful via {provider}")
                print(f"   Price: ${price}")
                return {'manager_working': True, 'provider_status': provider_status, 'test_result': result}
            else:
                error = result.get('error', 'Unknown error')
                print(f"❌ Data fetch failed: {error}")
                return {'manager_working': False, 'provider_status': provider_status, 'error': error}
        except Exception as e:
            print(f"❌ Data fetch error: {e}")
            return {'manager_working': False, 'provider_status': provider_status, 'error': str(e)}
            
    except Exception as e:
        print(f"❌ DataSourceManager import failed: {e}")
        return {'manager_working': False, 'error': str(e)}

async def audit_individual_providers():
    """Audit individual provider implementations"""
    print("\n🔌 Individual Provider Audit")
    print("=" * 50)
    
    provider_results = {}
    
    # Test YFinance (should work without API key)
    print("\n📈 YFinance Provider:")
    try:
        from src.shared.data_providers.yfinance_provider import YFinanceProvider
        yf = YFinanceProvider()
        print("✅ YFinanceProvider imported")
        
        try:
            result = await yf.get_current_price('AAPL')
            if result and 'price' in result:
                price = result['price']
                print(f"✅ YFinance working: AAPL = ${price}")
                provider_results['yfinance'] = {'status': 'working', 'price': price}
            else:
                print(f"⚠️  YFinance returned unexpected result: {result}")
                provider_results['yfinance'] = {'status': 'partial', 'result': result}
        except Exception as e:
            print(f"❌ YFinance test failed: {e}")
            provider_results['yfinance'] = {'status': 'failed', 'error': str(e)}
            
    except Exception as e:
        print(f"❌ YFinanceProvider import failed: {e}")
        provider_results['yfinance'] = {'status': 'import_failed', 'error': str(e)}
    
    # Test Polygon
    print("\n🔺 Polygon Provider:")
    try:
        from src.shared.data_providers.polygon_provider import PolygonProvider
        polygon = PolygonProvider()
        print("✅ PolygonProvider imported")
        
        if os.getenv('POLYGON_API_KEY'):
            try:
                result = await polygon.get_current_price('AAPL')
                if result and 'price' in result:
                    price = result['price']
                    print(f"✅ Polygon working: AAPL = ${price}")
                    provider_results['polygon'] = {'status': 'working', 'price': price}
                else:
                    print(f"⚠️  Polygon returned: {result}")
                    provider_results['polygon'] = {'status': 'partial', 'result': result}
            except Exception as e:
                print(f"❌ Polygon test failed: {e}")
                provider_results['polygon'] = {'status': 'failed', 'error': str(e)}
        else:
            print("⚠️  Polygon API key not configured")
            provider_results['polygon'] = {'status': 'not_configured', 'reason': 'missing_api_key'}
            
    except Exception as e:
        print(f"❌ PolygonProvider import failed: {e}")
        provider_results['polygon'] = {'status': 'import_failed', 'error': str(e)}
    
    # Test Finnhub
    print("\n🐟 Finnhub Provider:")
    try:
        from src.shared.data_providers.finnhub_provider import FinnhubProvider
        finnhub = FinnhubProvider()
        print("✅ FinnhubProvider imported")
        
        if os.getenv('FINNHUB_API_KEY'):
            try:
                result = await finnhub.get_current_price('AAPL')
                if result and 'price' in result:
                    price = result['price']
                    print(f"✅ Finnhub working: AAPL = ${price}")
                    provider_results['finnhub'] = {'status': 'working', 'price': price}
                else:
                    print(f"⚠️  Finnhub returned: {result}")
                    provider_results['finnhub'] = {'status': 'partial', 'result': result}
            except Exception as e:
                print(f"❌ Finnhub test failed: {e}")
                provider_results['finnhub'] = {'status': 'failed', 'error': str(e)}
        else:
            print("⚠️  Finnhub API key not configured")
            provider_results['finnhub'] = {'status': 'not_configured', 'reason': 'missing_api_key'}
            
    except Exception as e:
        print(f"❌ FinnhubProvider import failed: {e}")
        provider_results['finnhub'] = {'status': 'import_failed', 'error': str(e)}
    
    # Test Alpaca
    print("\n🦙 Alpaca Provider:")
    try:
        from src.shared.data_providers.alpaca_provider import AlpacaProvider
        alpaca = AlpacaProvider()
        print("✅ AlpacaProvider imported")
        
        if os.getenv('ALPACA_API_KEY') and os.getenv('ALPACA_SECRET_KEY'):
            try:
                result = await alpaca.get_current_price('AAPL')
                if result and 'price' in result:
                    price = result['price']
                    print(f"✅ Alpaca working: AAPL = ${price}")
                    provider_results['alpaca'] = {'status': 'working', 'price': price}
                else:
                    print(f"⚠️  Alpaca returned: {result}")
                    provider_results['alpaca'] = {'status': 'partial', 'result': result}
            except Exception as e:
                print(f"❌ Alpaca test failed: {e}")
                provider_results['alpaca'] = {'status': 'failed', 'error': str(e)}
        else:
            print("⚠️  Alpaca API credentials not configured")
            provider_results['alpaca'] = {'status': 'not_configured', 'reason': 'missing_api_credentials'}
            
    except Exception as e:
        print(f"❌ AlpacaProvider import failed: {e}")
        provider_results['alpaca'] = {'status': 'import_failed', 'error': str(e)}
    
    return provider_results

async def audit_commented_code():
    """Audit for commented out provider code"""
    print("\n💬 Commented Code Audit")
    print("=" * 50)
    
    # Check the main __init__.py file for commented imports
    init_file = ROOT / "src" / "shared" / "data_providers" / "__init__.py"
    
    if init_file.exists():
        content = init_file.read_text()
        
        # Look for commented imports
        lines = content.split('\n')
        commented_imports = []
        
        for i, line in enumerate(lines, 1):
            if line.strip().startswith('#') and ('import' in line or 'from' in line):
                commented_imports.append((i, line.strip()))
        
        if commented_imports:
            print("🔍 Found commented imports in __init__.py:")
            for line_num, line in commented_imports:
                print(f"  Line {line_num}: {line}")
        else:
            print("✅ No commented imports found in __init__.py")
    
    return commented_imports

def generate_audit_summary(config_status, manager_result, provider_results, commented_imports):
    """Generate comprehensive audit summary"""
    print("\n" + "=" * 60)
    print("📊 DATA PROVIDER AUDIT SUMMARY")
    print("=" * 60)
    
    # Configuration summary
    configured_count = sum(1 for status in config_status.values() if status)
    total_config = len(config_status)
    print(f"\n🔧 Configuration: {configured_count}/{total_config} API keys configured")
    
    # Provider summary
    working_providers = []
    failed_providers = []
    not_configured_providers = []
    
    for provider, result in provider_results.items():
        status = result.get('status')
        if status == 'working':
            working_providers.append(provider)
        elif status in ['not_configured']:
            not_configured_providers.append(provider)
        else:
            failed_providers.append(provider)
    
    print(f"\n📊 Provider Status:")
    print(f"  ✅ Working: {len(working_providers)} ({', '.join(working_providers) if working_providers else 'None'})")
    print(f"  ⚠️  Not Configured: {len(not_configured_providers)} ({', '.join(not_configured_providers) if not_configured_providers else 'None'})")
    print(f"  ❌ Failed: {len(failed_providers)} ({', '.join(failed_providers) if failed_providers else 'None'})")
    
    # Manager status
    manager_working = manager_result.get('manager_working', False)
    print(f"\n🔧 Data Source Manager: {'✅ Working' if manager_working else '❌ Failed'}")
    
    # Commented code
    print(f"\n💬 Commented Code: {len(commented_imports)} commented imports found")
    
    # Recommendations
    print(f"\n🎯 RECOMMENDATIONS:")
    
    if not_configured_providers:
        print(f"  1. Configure API keys for: {', '.join(not_configured_providers)}")
    
    if failed_providers:
        print(f"  2. Fix issues with: {', '.join(failed_providers)}")
    
    if commented_imports:
        print(f"  3. Review and uncomment {len(commented_imports)} commented imports")
    
    if not working_providers:
        print(f"  4. ⚠️  CRITICAL: No working data providers found!")
    
    if working_providers:
        print(f"  5. ✅ Good: {', '.join(working_providers)} provider(s) working")

async def main():
    """Run comprehensive data provider audit"""
    print("🔍 COMPREHENSIVE DATA PROVIDER AUDIT")
    print("=" * 60)
    
    # Run all audit components
    config_status = await audit_environment_config()
    manager_result = await audit_data_source_manager()
    provider_results = await audit_individual_providers()
    commented_imports = await audit_commented_code()
    
    # Generate summary
    generate_audit_summary(config_status, manager_result, provider_results, commented_imports)
    
    return {
        'config_status': config_status,
        'manager_result': manager_result,
        'provider_results': provider_results,
        'commented_imports': commented_imports
    }

if __name__ == "__main__":
    result = asyncio.run(main())
