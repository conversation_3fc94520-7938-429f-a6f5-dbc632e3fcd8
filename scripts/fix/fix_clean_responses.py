#!/usr/bin/env python3
"""
Remove internal error messages from user responses
"""

def fix_clean_responses():
    """Remove print statements that show errors to users"""
    
    clean_processor = '''"""
AI Chat Processor for intent detection and symbol extraction
"""
import json
import asyncio
from typing import Dict, Any, Optional
from src.shared.ai_chat.ai_client import AIClientWrapper

class AIChatProcessor:
    def __init__(self):
        self.ai_client = AIClientWrapper()
    
    async def process_query_for_intent(self, query: str) -> Dict[str, Any]:
        """Use AI to detect intent and extract symbols from query"""
        # Try AI first, but don't let it block fallback logic
        ai_result = None
        try:
            # Create a prompt for AI to analyze the query
            prompt = f"""Analyze this trading query and determine:
1. Intent: price_query, trading_advice, help, greeting, or general_question
2. Symbol: Extract stock symbol if it's a price query (e.g., "Tesla" -> "TSLA", "Microsoft" -> "MSFT")

Query: "{query}"

Respond with JSON only:
{{
    "intent": "detected_intent",
    "symbol": "extracted_symbol_or_null",
    "confidence": 0.95
}}

Common company name mappings:
- Tesla -> TSLA
- Microsoft -> MSFT  
- Apple -> AAPL
- Google -> GOOGL
- Amazon -> AMZN
- NVIDIA -> NVDA
- Meta -> META
- Netflix -> NFLX
- AMD -> AMD
- Intel -> INTC
- Oracle -> ORCL
- Salesforce -> CRM
- Adobe -> ADBE
- PayPal -> PYPL
- Uber -> UBER
- Lyft -> LYFT
- Spotify -> SPOT
- Square -> SQ
- Roku -> ROKU
- Zoom -> ZM
- DocuSign -> DOCU
- Snowflake -> SNOW
- Palantir -> PLTR
- CrowdStrike -> CRWD
- Okta -> OKTA
- Datadog -> DDOG
- Cloudflare -> NET
- Elastic -> ESTC
- MongoDB -> MDB
- Twilio -> TWLO
- Pinterest -> PINS
- Snapchat -> SNAP
- Twitter -> TWTR
- Facebook -> META
- Alphabet -> GOOGL"""

            # Get AI response
            response = await self.ai_client.generate_response(prompt)
            
            if response:
                # Try to parse JSON response
                try:
                    # Clean the response to extract JSON
                    if isinstance(response, str):
                        # Look for JSON in the response
                        start_idx = response.find('{')
                        end_idx = response.rfind('}') + 1
                        if start_idx != -1 and end_idx > start_idx:
                            json_str = response[start_idx:end_idx]
                            result = json.loads(json_str)
                            
                            # Validate the result
                            if 'intent' in result:
                                ai_result = {
                                    'intent': result['intent'],
                                    'symbol': result.get('symbol'),
                                    'confidence': result.get('confidence', 0.8)
                                }
                except (json.JSONDecodeError, KeyError):
                    # Silently continue to fallback
                    pass
        except Exception:
            # Silently continue to fallback
            pass
        
        # If AI worked, return its result
        if ai_result:
            return ai_result
        
        # Fallback: simple pattern matching if AI fails
        query_lower = query.lower()
        
        # Check for price queries
        if any(word in query_lower for word in ['price', 'cost', 'value', 'worth', 'trading at', 'current price']):
            # Try to extract symbol from common company names
            company_mappings = {
                'tesla': 'TSLA',
                'microsoft': 'MSFT', 
                'apple': 'AAPL',
                'google': 'GOOGL',
                'amazon': 'AMZN',
                'nvidia': 'NVDA',
                'meta': 'META',
                'netflix': 'NFLX',
                'amd': 'AMD',
                'intel': 'INTC',
                'oracle': 'ORCL',
                'salesforce': 'CRM',
                'adobe': 'ADBE',
                'paypal': 'PYPL',
                'uber': 'UBER',
                'lyft': 'LYFT',
                'spotify': 'SPOT',
                'square': 'SQ',
                'roku': 'ROKU',
                'zoom': 'ZM',
                'docusign': 'DOCU',
                'snowflake': 'SNOW',
                'palantir': 'PLTR',
                'crowdstrike': 'CRWD',
                'okta': 'OKTA',
                'datadog': 'DDOG',
                'cloudflare': 'NET',
                'elastic': 'ESTC',
                'mongodb': 'MDB',
                'twilio': 'TWLO',
                'pinterest': 'PINS',
                'snapchat': 'SNAP',
                'twitter': 'TWTR',
                'facebook': 'META',
                'alphabet': 'GOOGL'
            }
            
            symbol = None
            for company, ticker in company_mappings.items():
                if company in query_lower:
                    symbol = ticker
                    break
            
            return {
                'intent': 'price_query',
                'symbol': symbol,
                'confidence': 0.7
            }
        
        # Check for trading advice
        elif any(word in query_lower for word in ['buy', 'sell', 'bullish', 'bearish', 'recommend', 'strategy', 'strategies', 'market', 'stocks']):
            return {
                'intent': 'trading_advice',
                'symbol': None,
                'confidence': 0.8
            }
        
        # Check for help
        elif any(word in query_lower for word in ['help', 'commands', 'how to use', 'what can you do']):
            return {
                'intent': 'help',
                'symbol': None,
                'confidence': 0.9
            }
        
        # Check for greetings
        elif any(word in query_lower for word in ['hello', 'hi', 'hey', 'good morning', 'good afternoon', 'good evening']):
            return {
                'intent': 'greeting',
                'symbol': None,
                'confidence': 0.9
            }
        
        # Default to general question
        else:
            return {
                'intent': 'general_question',
                'symbol': None,
                'confidence': 0.6
            }
'''
    
    # Write the clean processor
    with open('src/shared/ai_chat/processor.py', 'w') as f:
        f.write(clean_processor)
    
    print("✅ Removed internal error messages from user responses")

if __name__ == "__main__":
    fix_clean_responses()
