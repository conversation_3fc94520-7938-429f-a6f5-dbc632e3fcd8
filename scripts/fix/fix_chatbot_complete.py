#!/usr/bin/env python3
"""
Complete fix to make the /ask command work as a proper chatbot
"""

def fix_chatbot():
    """Fix the /ask command to work as a proper chatbot"""
    
    # 1. Create a proper chatbot pipeline
    pipeline_content = '''"""
Enhanced Ask Command Pipeline - CHATBOT VERSION
"""

import time
import asyncio
import re
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class AskPipeline:
    """Enhanced Ask Command Pipeline that works as a chatbot"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        logger.info("AskPipeline initialized as chatbot")
    
    async def process_query(self, 
                           query: str, 
                           user_id: str, 
                           username: str,
                           context: Optional[Dict[str, Any]] = None,
                           correlation_id: Optional[str] = None) -> Dict[str, Any]:
        """Process a user query as a chatbot"""
        try:
            start_time = time.time()
            logger.info(f"Processing query: {query}")
            
            # Check if this is a price query
            query_lower = query.lower()
            price_indicators = ['price', 'cost', 'value', 'worth', 'current price', 'how much']
            is_price_query = any(indicator in query_lower for indicator in price_indicators)
            
            if is_price_query:
                # Better symbol extraction - look for $SYMBOL or SYMBOL patterns
                symbol = None
                
                # First try to find $SYMBOL pattern
                dollar_match = re.search(r'\\$([A-Z]{1,5})\\b', query.upper())
                if dollar_match:
                    symbol = dollar_match.group(1)
                else:
                    # Then try to extract from the end of the query
                    # Remove common phrases and look for symbols
                    cleaned_query = query.upper()
                    for phrase in ['WHAT DO YOU EXPECT THE PRICE OF', 'WHAT IS THE PRICE OF', 'PRICE OF', 'WHAT IS', 'CURRENT PRICE OF', 'HOW MUCH IS', 'EXPECT THE PRICE OF']:
                        cleaned_query = cleaned_query.replace(phrase, '')
                    cleaned_query = cleaned_query.strip()
                    
                    # Look for 1-5 uppercase letters
                    symbol_match = re.search(r'\\b([A-Z]{1,5})\\b', cleaned_query)
                    if symbol_match:
                        symbol = symbol_match.group(1)
                
                if symbol and len(symbol) >= 1:
                    logger.info(f"Processing price query for symbol: {symbol}")
                    
                    # Try to get real price data
                    try:
                        from src.shared.data_providers.aggregator import data_provider_aggregator
                        
                        # Get current price data
                        price_data = await asyncio.wait_for(
                            data_provider_aggregator.get_ticker(symbol),
                            timeout=10.0
                        )
                        
                        if price_data and not price_data.get('error') and price_data.get('price'):
                            price = price_data.get('price')
                            # Calculate change if we have open price
                            open_price = price_data.get('open', price)
                            change = price - open_price if open_price else 0
                            change_percent = (change / open_price * 100) if open_price else 0
                            
                            # Build response
                            response_parts = [f"**💰 {symbol} Current Price**", f"📈 **Price**: ${price:.2f}"]
                            
                            if change != 0:
                                change_emoji = "📈" if change >= 0 else "📉"
                                response_parts.append(f"{change_emoji} **Change**: {change:+.2f} ({change_percent:+.2f}%)")
                            
                            response_parts.append("\\n⚠️ *This is real-time price data. For detailed analysis, use the /analyze command.*")
                            
                            result = {
                                "response": "\\n".join(response_parts),
                                "data_quality": 95,
                                "status": "success"
                            }
                            
                            logger.info(f"Successfully retrieved price data for {symbol}: ${price}")
                            
                        else:
                            # Fallback response
                            result = {
                                "response": f"❌ Unable to retrieve current price data for {symbol}. Please try again later or use the /analyze command for more detailed information.",
                                "data_quality": 60,
                                "status": "partial_success"
                            }
                            
                    except Exception as price_error:
                        logger.warning(f"Price data retrieval failed for {symbol}: {price_error}")
                        result = {
                            "response": f"❌ Error retrieving price data for {symbol}: {str(price_error)}. Please try again later.",
                            "data_quality": 40,
                            "status": "error"
                        }
                else:
                    # No symbol found
                    result = {
                        "response": "❌ I couldn't find a stock symbol in your query. Please specify a symbol like $AAPL, $TSLA, or $GME.",
                        "data_quality": 70,
                        "status": "error"
                    }
            else:
                # General chatbot query - provide helpful responses
                query_lower = query.lower()
                
                if any(word in query_lower for word in ['hello', 'hi', 'hey', 'greetings']):
                    result = {
                        "response": f"Hello! 👋 I'm your trading assistant. I can help you with:\n• Stock prices (e.g., 'What is the price of $AAPL?')\n• Market analysis\n• Trading questions\n• Portfolio advice\n\nHow can I help you today?",
                        "data_quality": 90,
                        "status": "success"
                    }
                elif any(word in query_lower for word in ['help', 'what can you do', 'commands']):
                    result = {
                        "response": "🤖 **I can help you with:**\\n\\n**Stock Data:**\\n• Current prices: 'What is the price of $AAPL?'\\n• Market analysis: 'Analyze $TSLA'\\n\\n**Trading:**\\n• Use `/analyze` for detailed technical analysis\\n• Use `/compare` to compare stocks\\n• Use `/alerts` to set price alerts\\n\\n**General:**\\n• Ask me about market trends\\n• Get trading advice\\n• Portfolio questions\\n\\nWhat would you like to know?",
                        "data_quality": 90,
                        "status": "success"
                    }
                elif any(word in query_lower for word in ['market', 'trend', 'analysis', 'forecast', 'prediction']):
                    result = {
                        "response": "📊 For detailed market analysis, I recommend using the `/analyze` command with a specific stock symbol. For general market trends and predictions, I can help with:\\n\\n• Technical analysis\\n• Market sentiment\\n• Trading strategies\\n• Risk assessment\\n\\nWhat specific stock or market aspect would you like me to analyze?",
                        "data_quality": 85,
                        "status": "success"
                    }
                elif any(word in query_lower for word in ['trading', 'strategy', 'advice', 'recommendation']):
                    result = {
                        "response": "💡 **Trading Advice:**\\n\\nI can help you with:\\n• Technical analysis using `/analyze`\\n• Stock comparisons with `/compare`\\n• Setting up alerts with `/alerts`\\n• Portfolio management\\n\\n**Remember:** This is for educational purposes only. Always do your own research and consider consulting with a financial advisor before making investment decisions.\\n\\nWhat specific trading question do you have?",
                        "data_quality": 85,
                        "status": "success"
                    }
                else:
                    # General response for other queries
                    result = {
                        "response": f"🤖 I understand you're asking: '{query}'\\n\\nI'm a trading assistant that can help with:\\n• **Stock prices**: 'What is the price of $AAPL?'\\n• **Market analysis**: Use `/analyze` command\\n• **Trading advice**: Ask me about strategies\\n• **General questions**: I'll do my best to help!\\n\\nCould you be more specific about what you'd like to know?",
                        "data_quality": 80,
                        "status": "success"
                    }
            
            execution_time = time.time() - start_time
            logger.info(f"Query processed in {execution_time:.2f} seconds")
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing query: {e}")
            return {
                "error": str(e),
                "response": f"❌ I encountered an error processing your query: {str(e)}\\n\\nPlease try rephrasing your question or use one of these commands:\\n• `/analyze` for detailed stock analysis\\n• `/compare` to compare stocks\\n• `/help` for more options",
                "status": "failed",
                "data_quality": 0
            }

# Create default pipeline instance
default_pipeline = AskPipeline()
'''
    
    # Write the enhanced pipeline
    with open('src/bot/pipeline/commands/ask/pipeline.py', 'w') as f:
        f.write(pipeline_content)
    
    print("✅ Created enhanced chatbot pipeline")
    
    # 2. Fix the Discord interaction handling
    # Read the current client file
    with open('src/bot/client.py', 'r') as f:
        content = f.read()
    
    # Better ask command with proper interaction handling
    better_ask_command = '''        async def ask_command(interaction: discord.Interaction, query: Optional[str] = None, attachment: Optional[discord.Attachment] = None):
            """Ask the AI about trading and markets - Chatbot version"""
            try:
                print(f"🚨 ASK COMMAND CALLED: query='{query}', attachment={attachment}")
                
                # Check if interaction is already responded to
                if interaction.response.is_done():
                    print("⚠️ Interaction already responded to, using followup")
                    # Use followup for already responded interactions
                    try:
                        await self.handle_ask_command_followup(interaction, query, attachment)
                    except Exception as followup_error:
                        print(f"⚠️ Followup failed: {followup_error}")
                        # Try to send a simple response
                        try:
                            await interaction.followup.send("❌ I encountered an error. Please try again later.")
                        except:
                            pass
                else:
                    # Try to defer first
                    try:
                        await interaction.response.defer(thinking=True)
                        print("✅ Successfully deferred interaction")
                    except Exception as defer_error:
                        print(f"⚠️ Failed to defer interaction: {defer_error}. Continuing...")
                    
                    # Process the query
                    await self.handle_ask_command(interaction, query, attachment)
                
                print(f"✅ ASK COMMAND COMPLETED SUCCESSFULLY")
            except Exception as e:
                print(f"🚨 ERROR IN ASK COMMAND: {e}")
                import traceback
                traceback.print_exc()
                # Try to send error response
                try:
                    if not interaction.response.is_done():
                        await interaction.response.send_message(
                            "❌ I encountered an error while processing your request. Please try again later.",
                            ephemeral=True
                        )
                    else:
                        await interaction.followup.send(
                            "❌ I encountered an error while processing your request. Please try again later.",
                            ephemeral=True
                        )
                except Exception as send_error:
                    print(f"🚨 Failed to send error response: {send_error}")
                raise'''
    
    # Replace the ask command
    import re
    content = re.sub(
        r'async def ask_command\(interaction: discord\.Interaction, query: Optional\[str\] = None, attachment: Optional\[discord\.Attachment\] = None\):.*?(?=\n        # Zones command)',
        better_ask_command,
        content,
        flags=re.DOTALL
    )
    
    # Write the fixed content back
    with open('src/bot/client.py', 'w') as f:
        f.write(content)
    
    print("✅ Fixed ask command with better interaction handling")
    
    print("🎉 Chatbot fix applied successfully!")

if __name__ == "__main__":
    fix_chatbot()
