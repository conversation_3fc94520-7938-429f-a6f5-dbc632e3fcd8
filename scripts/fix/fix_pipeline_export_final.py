#!/usr/bin/env python3
"""
Fix pipeline export properly
"""

def fix_pipeline_export_final():
    """Fix the pipeline export"""
    
    # Read the current pipeline file
    with open('src/bot/pipeline/commands/ask/pipeline.py', 'r') as f:
        content = f.read()
    
    # Remove the incomplete export line and add proper export
    content = content.replace("# Export default pipeline for executor", "")
    content = content.replace("default_pipeline = pipeline", "")
    
    # Add proper export at the end
    content += "\n\n# Export default pipeline for executor\ndefault_pipeline = pipeline"
    
    # Write the fixed content back
    with open('src/bot/pipeline/commands/ask/pipeline.py', 'w') as f:
        f.write(content)
    
    print("✅ Fixed pipeline export properly")

if __name__ == "__main__":
    fix_pipeline_export_final()
