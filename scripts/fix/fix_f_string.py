#!/usr/bin/env python3
"""
Fix the f-string syntax error
"""

def fix_f_string():
    """Fix the f-string syntax error"""
    
    # Read the current pipeline file
    with open('src/bot/pipeline/commands/ask/pipeline.py', 'r') as f:
        content = f.read()
    
    # Fix the problematic f-string
    content = content.replace(
        'f"Hello! 👋 I\'m your trading assistant. I can help you with:',
        '"Hello! 👋 I\'m your trading assistant. I can help you with:'
    )
    
    # Write the fixed content back
    with open('src/bot/pipeline/commands/ask/pipeline.py', 'w') as f:
        f.write(content)
    
    print("✅ Fixed f-string syntax error")

if __name__ == "__main__":
    fix_f_string()
