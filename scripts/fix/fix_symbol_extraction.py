#!/usr/bin/env python3
"""
Fix the symbol extraction regex to properly find stock symbols
"""

def fix_symbol_extraction():
    """Fix the symbol extraction logic"""
    
    # Read the current pipeline file
    with open('src/bot/pipeline/commands/ask/pipeline.py', 'r') as f:
        content = f.read()
    
    # Fix the symbol extraction regex
    old_regex = r'\\b([A-Z]{1,5})\\b'
    new_regex = r'\\b([A-Z]{1,5})\\b'
    
    # The issue is that we need to look for actual stock symbols, not just any uppercase words
    # Let's improve the symbol extraction logic
    old_symbol_logic = '''                symbol_match = re.search(r'\\b([A-Z]{1,5})\\b', query.upper())'''
    
    new_symbol_logic = '''                # Look for common stock symbols in the query
                # First try to find known stock symbols
                known_symbols = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'META', 'NFLX', 'AMD', 'INTC', 'ORCL', 'CRM', 'ADBE', 'PYPL', 'UBER', 'LYFT', 'SPOT', 'SQ', 'ROKU', 'ZM', 'DOCU', 'SNOW', 'PLTR', 'CRWD', 'OKTA', 'DDOG', 'NET', 'ESTC', 'MDB', 'TWLO', 'PINS', 'SNAP', 'TWTR', 'FB', 'GOOG', 'AMZN', 'NFLX', 'AMD', 'INTC', 'ORCL', 'CRM', 'ADBE', 'PYPL', 'UBER', 'LYFT', 'SPOT', 'SQ', 'ROKU', 'ZM', 'DOCU', 'SNOW', 'PLTR', 'CRWD', 'OKTA', 'DDOG', 'NET', 'ESTC', 'MDB', 'TWLO', 'PINS', 'SNAP', 'TWTR']
                
                symbol = None
                query_upper = query.upper()
                
                # First check for known symbols
                for known_symbol in known_symbols:
                    if known_symbol in query_upper:
                        symbol = known_symbol
                        break
                
                # If no known symbol found, try regex for 2-5 letter symbols
                if not symbol:
                    symbol_match = re.search(r'\\b([A-Z]{2,5})\\b', query_upper)
                    if symbol_match:
                        symbol = symbol_match.group(1)'''
    
    content = content.replace(old_symbol_logic, new_symbol_logic)
    
    # Also update the condition check
    old_condition = '''                if symbol_match:'''
    new_condition = '''                if symbol:'''
    
    content = content.replace(old_condition, new_condition)
    
    # Remove the old symbol assignment
    old_assignment = '''                    symbol = symbol_match.group(1)'''
    new_assignment = '''                    # symbol already assigned above'''
    
    content = content.replace(old_assignment, new_assignment)
    
    # Write the fixed content back
    with open('src/bot/pipeline/commands/ask/pipeline.py', 'w') as f:
        f.write(content)
    
    print("✅ Fixed symbol extraction to properly find stock symbols")

if __name__ == "__main__":
    fix_symbol_extraction()
