#!/usr/bin/env python3
"""
Fix the data provider to actually get real stock prices
"""

def fix_data_provider():
    """Create a working data provider that gets real prices"""
    
    working_provider = '''"""
Working data provider that gets real stock prices using yfinance
"""
import yfinance as yf
from typing import Optional, Dict, Any
from .unified_base import UnifiedDataProvider

class YFinanceProvider(UnifiedDataProvider):
    """Working yfinance provider that gets real data."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        cfg = config or {}
        super().__init__(provider_name='yfinance', provider_type='market_data', config=cfg)

    async def get_current_price(self, symbol: str) -> Dict[str, Any]:
        """Get current price using yfinance"""
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            hist = ticker.history(period="1d")
            
            if hist.empty:
                return {
                    'provider': 'yfinance',
                    'success': False,
                    'symbol': symbol,
                    'price': None,
                    'error': 'No data available for symbol'
                }
            
            current_price = hist['Close'].iloc[-1]
            
            return {
                'provider': 'yfinance',
                'success': True,
                'symbol': symbol,
                'price': float(current_price),
                'change': float(hist['Close'].iloc[-1] - hist['Open'].iloc[-1]),
                'change_percent': float((hist['Close'].iloc[-1] - hist['Open'].iloc[-1]) / hist['Open'].iloc[-1] * 100),
                'volume': int(hist['Volume'].iloc[-1]),
                'timestamp': hist.index[-1].isoformat(),
                'source': 'yfinance'
            }
        except Exception as e:
            return {
                'provider': 'yfinance',
                'success': False,
                'symbol': symbol,
                'price': None,
                'error': str(e)
            }
    
    async def get_ticker(self, symbol: str) -> Dict[str, Any]:
        """Get ticker data - same as get_current_price for compatibility"""
        return await self.get_current_price(symbol)
'''
    
    # Write the working provider
    with open('src/shared/data_providers/yfinance_provider.py', 'w') as f:
        f.write(working_provider)
    
    print("✅ Fixed yfinance provider to get real stock prices")

if __name__ == "__main__":
    fix_data_provider()
