#!/usr/bin/env python3
"""
Fix the pipeline grader timing calculation bug
"""

import re

def fix_pipeline_grader():
    """Fix the pipeline grader timing calculation issues"""
    
    # Read the current file
    with open('src/shared/monitoring/pipeline_grader.py', 'r') as f:
        content = f.read()
    
    # Fix 1: Change start_time initialization to use time.time()
    content = re.sub(
        r'self\.start_time = datetime\.now\(\)',
        'self.start_time = time.time()  # Use time.time() for consistent timing',
        content
    )
    
    # Fix 2: Fix the execution time calculation in complete_pipeline
    content = re.sub(
        r'# Calculate execution time\n        if self\.step_grades:\n            # Get the end time of the last step\n            end_time = datetime\.now\(\)\n            execution_time = \(end_time - self\.start_time\)\.total_seconds\(\)',
        '''# Calculate execution time
        if self.step_grades:
            # Get the end time of the last step
            end_time = time.time()
            execution_time = end_time - self.start_time  # Direct subtraction for time.time()''',
        content
    )
    
    # Fix 3: Fix the case when no steps are graded
    content = re.sub(
        r'end_time=datetime\.now\(\)',
        'end_time=time.time()',
        content
    )
    
    # Fix 4: Fix the PipelineGrade class execution_time calculation
    content = re.sub(
        r'execution_time=0\.0',
        'execution_time=0.0',
        content
    )
    
    # Write the fixed content back
    with open('src/shared/monitoring/pipeline_grader.py', 'w') as f:
        f.write(content)
    
    print("✅ Fixed pipeline grader timing calculation issues")

if __name__ == "__main__":
    fix_pipeline_grader()
