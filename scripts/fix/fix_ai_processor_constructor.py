#!/usr/bin/env python3
"""
Fix AIChatProcessor constructor mismatch
"""

def fix_constructor():
    """Fix the AIChatProcessor constructor to match the expected signature"""
    
    fixed_ai_services = '''"""
AI Chat Processor wrapper for backward compatibility.
"""
import logging
from typing import Dict, Any, Optional

from src.shared.error_handling.logging import get_logger
logger = get_logger(__name__)

class AIChatProcessorWrapper:
    """
    Fixed AI Chat Processor wrapper without circular imports.
    """
    
    def __init__(self, context: Optional[Any] = None):
        """
        Initialize the AI Chat Processor wrapper.
        
        Args:
            context: Optional context object with pipeline information
        """
        self.context = context
        self.pipeline_id = getattr(context, 'pipeline_id', 'unknown') if context else 'unknown'
        self.logger = logging.getLogger(__name__)
        
        # Initialize components directly to avoid circular imports
        self._initialize_components()
        
        self.logger.info(f"Initialized AIChatProcessorWrapper for pipeline: {self.pipeline_id}")
    
    def _initialize_components(self):
        """Initialize components without circular imports."""
        try:
            # Import here to avoid circular imports
            from src.shared.ai_chat.processor import AIChatProcessor
            # Don't pass context to AIChatProcessor - it doesn't accept it
            self.processor = AIChatProcessor()
        except ImportError as e:
            self.logger.warning(f"Could not import AIChatProcessor: {e}")
            self.processor = None
    
    async def process_query(self, query: str, user_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Process a user query - backward compatible method.
        
        Args:
            query: The user's query string
            user_id: Optional user ID for context
            
        Returns:
            Dictionary with processed response
        """
        if not self.processor:
            return {
                'response': 'AI service temporarily unavailable. Please try again later.',
                'status': 'error',
                'data_quality': 0
            }
        
        try:
            # Use the processor to get intent and symbol
            result = await self.processor.process_query_for_intent(query)
            
            # Return in the expected format
            return {
                'response': f"Intent: {result.get('intent', 'unknown')}, Symbol: {result.get('symbol', 'none')}",
                'status': 'success',
                'data_quality': 80,
                'intent': result.get('intent'),
                'symbol': result.get('symbol')
            }
        except Exception as e:
            self.logger.error(f"Error processing query: {e}")
            return {
                'response': 'Error processing your request. Please try again.',
                'status': 'error',
                'data_quality': 0
            }
    
    async def process(self, query: str, **kwargs) -> Dict[str, Any]:
        """Main processing method."""
        return await self.process_query(query, kwargs.get('user_id'))
    
    def _generate_final_response(self, ai_response: Dict[str, Any]) -> str:
        """Legacy method alias - now handled by ResponseFormatter."""
        logger.warning("_generate_final_response is deprecated. Use process() method instead.")
        return ai_response

# Create a global instance for backward compatibility
processor = AIChatProcessorWrapper()

# Export the main class and instance
__all__ = ['AIChatProcessorWrapper', 'processor', 'create_processor']

# Backward compatibility - export the processor instance as the main class
AIChatProcessor = AIChatProcessorWrapper

# Create a simple AIAskResult class for compatibility
class AIAskResult:
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)
    
    def dict(self):
        return {k: v for k, v in self.__dict__.items()}

def create_processor(context: Optional[Any] = None) -> AIChatProcessorWrapper:
    """
    Factory function to create a new processor instance.
    
    Args:
        context: Optional context object
        
    Returns:
        AIChatProcessorWrapper instance
    """
    return AIChatProcessorWrapper(context)

# Export the factory function
__all__.append('create_processor')
'''
    
    # Write the fixed AI services processor
    with open('src/shared/ai_services/ai_chat_processor.py', 'w') as f:
        f.write(fixed_ai_services)
    
    print("✅ Fixed AIChatProcessor constructor mismatch")

if __name__ == "__main__":
    fix_constructor()
