#!/usr/bin/env python3
"""
Fix AI Price Hallucination Issue

The AI is generating technical analysis based on outdated training data
instead of using the real market data provided to it.

This script identifies and fixes the issue where AI generates support/resistance
levels that don't match the actual current price.
"""

import re
from typing import Dict, Any, List

def analyze_price_hallucination(response: str, current_price: float) -> Dict[str, Any]:
    """
    Analyze if the AI response contains price levels that don't match current price.
    
    Args:
        response: The AI-generated response
        current_price: The actual current price
        
    Returns:
        Analysis of price hallucination issues
    """
    issues = []
    
    # Extract price levels mentioned in the response
    price_patterns = [
        r'\$(\d+(?:\.\d{1,2})?)',  # $123.45 format
        r'(\d+(?:\.\d{1,2})?)\s*\$',  # 123.45$ format
        r'support.*?(\d+(?:\.\d{1,2})?)',  # support levels
        r'resistance.*?(\d+(?:\.\d{1,2})?)',  # resistance levels
        r'entry.*?(\d+(?:\.\d{1,2})?)',  # entry levels
        r'target.*?(\d+(?:\.\d{1,2})?)',  # target levels
    ]
    
    mentioned_prices = []
    for pattern in price_patterns:
        matches = re.findall(pattern, response, re.IGNORECASE)
        for match in matches:
            try:
                price = float(match)
                mentioned_prices.append(price)
            except ValueError:
                continue
    
    # Check for price levels that are significantly different from current price
    for price in mentioned_prices:
        if price > 0:  # Avoid division by zero
            ratio = max(price / current_price, current_price / price)
            if ratio > 2.0:  # More than 2x different
                issues.append({
                    'mentioned_price': price,
                    'current_price': current_price,
                    'ratio': ratio,
                    'severity': 'high' if ratio > 3.0 else 'medium'
                })
    
    return {
        'has_hallucination': len(issues) > 0,
        'issues': issues,
        'mentioned_prices': mentioned_prices,
        'current_price': current_price
    }

def fix_ai_prompt_for_price_accuracy():
    """
    Generate an improved AI prompt that prevents price hallucination.
    """
    return """
CRITICAL: PRICE ACCURACY REQUIREMENT

You MUST use ONLY the real market data provided to you. NEVER generate price levels, 
support/resistance, or technical analysis based on your training data.

RULES:
1. If current price is $177.82, ALL price levels must be in the $170-180 range
2. Support/resistance levels should be within 10-20% of current price
3. Entry levels should be realistic relative to current price
4. NEVER mention price levels that are 3x+ different from current price

VALIDATION:
- Before mentioning any price level, check it against current price
- If ratio > 2.0, DO NOT mention that price level
- Focus on percentage-based analysis instead of absolute prices

EXAMPLE:
❌ WRONG: "Support at $875, resistance at $950" (when current price is $177)
✅ CORRECT: "Support at $170-175, resistance at $180-185" (when current price is $177)

Always validate price levels against the provided current price data.
"""

def create_price_validation_function():
    """
    Create a function to validate AI responses for price accuracy.
    """
    def validate_response(response: str, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate that AI response price levels match the provided market data.
        """
        validation_results = {
            'is_valid': True,
            'issues': [],
            'suggestions': []
        }
        
        for symbol, data in market_data.items():
            if 'current_price' in data:
                current_price = data['current_price']
                analysis = analyze_price_hallucination(response, current_price)
                
                if analysis['has_hallucination']:
                    validation_results['is_valid'] = False
                    validation_results['issues'].extend(analysis['issues'])
                    validation_results['suggestions'].append(
                        f"AI mentioned price levels that don't match {symbol} current price of ${current_price:.2f}"
                    )
        
        return validation_results
    
    return validate_response

def create_improved_ai_prompt_template():
    """
    Create an improved AI prompt template that prevents price hallucination.
    """
    return """
You are a financial analysis AI. CRITICAL RULE: Use ONLY the market data provided to you.

MARKET DATA PROVIDED:
{market_data}

CURRENT PRICE: ${current_price}

ANALYSIS REQUIREMENTS:
1. ALL price levels must be within 20% of current price (${current_price})
2. Support levels: ${current_price * 0.9:.2f} - ${current_price * 0.95:.2f}
3. Resistance levels: ${current_price * 1.05:.2f} - ${current_price * 1.10:.2f}
4. Entry levels: ${current_price * 0.95:.2f} - ${current_price * 1.05:.2f}

VALIDATION CHECK:
Before mentioning any price, ensure it's realistic relative to ${current_price}.
If a price level seems too high/low, don't mention it.

Generate analysis based ONLY on the provided data, not your training data.
"""

if __name__ == "__main__":
    # Test the analysis function
    test_response = """
    Based on current market data for NVIDIA (NVDA), here are key entry levels to watch this week:
    
    Current Technical Picture:
    - NVDA is trading near its 52-week high range
    - Key support levels: $875-885 (recent consolidation zone)
    - Resistance: $950-970 (all-time high area)
    
    Entry Strategy Options:
    1. Aggressive: $890-900 range if we see a pullback to the 20-day MA
    2. Conservative: $850-870 on any deeper pullback to the 50-day MA
    3. Momentum: Break above $970 with volume could signal continuation
    """
    
    current_price = 177.82
    
    analysis = analyze_price_hallucination(test_response, current_price)
    
    print("🔍 Price Hallucination Analysis:")
    print(f"Current Price: ${current_price}")
    print(f"Has Hallucination: {analysis['has_hallucination']}")
    print(f"Issues Found: {len(analysis['issues'])}")
    
    for i, issue in enumerate(analysis['issues'], 1):
        print(f"\nIssue {i}:")
        print(f"  Mentioned Price: ${issue['mentioned_price']}")
        print(f"  Current Price: ${issue['current_price']}")
        print(f"  Ratio: {issue['ratio']:.2f}x")
        print(f"  Severity: {issue['severity']}")
    
    print(f"\n📝 Improved AI Prompt:")
    print(fix_ai_prompt_for_price_accuracy())
