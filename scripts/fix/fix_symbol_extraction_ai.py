#!/usr/bin/env python3
"""
Replace regex symbol extraction with AI-powered extraction
"""

def fix_symbol_extraction():
    """Replace regex with AI for symbol extraction"""
    
    ai_pipeline = '''"""
AI-powered ask pipeline that uses AI for symbol extraction and response generation
"""
import asyncio
import re
from typing import Dict, Any, Optional
from src.shared.data_providers.aggregator import DataProviderAggregator
from src.shared.ai_chat.processor import AIChatProcessor

class AskPipeline:
    def __init__(self):
        self.data_provider_aggregator = DataProviderAggregator()
        self.ai_processor = AIChatProcessor()
    
    async def process_query(self, query: str, user_id: str, username: str, context: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """Process user query using AI for intent detection and symbol extraction"""
        try:
            # Use AI to extract intent and symbol
            ai_response = await self.ai_processor.process_query_for_intent(query)
            
            if ai_response and 'intent' in ai_response:
                intent = ai_response['intent']
                symbol = ai_response.get('symbol')
                
                if intent == 'price_query' and symbol:
                    # Get real price data
                    try:
                        price_data = await self.data_provider_aggregator.get_ticker(symbol)
                        if 'price' in price_data:
                            return {
                                "response": f"📊 **{symbol.upper()} Current Price:** ${price_data['price']:.2f}\\n\\n*Data provided for educational purposes only. Always do your own research before making investment decisions.*",
                                "data_quality": 95,
                                "status": "success"
                            }
                        else:
                            return {
                                "response": f"❌ Could not retrieve current price for {symbol.upper()}. Please check the symbol and try again.\\n\\n*You can use `/analyze` for detailed technical analysis.*",
                                "data_quality": 60,
                                "status": "partial_success"
                            }
                    except Exception as e:
                        return {
                            "response": f"❌ Error retrieving price data: {str(e)}\\n\\n*Please try again later or use `/analyze` for detailed analysis.*",
                            "data_quality": 40,
                            "status": "error"
                        }
                elif intent == 'trading_advice':
                    return {
                        "response": "💡 **Trading Advice:**\\n\\nI can help you with:\\n- Technical analysis using `/analyze`\\n- Stock comparisons with `/compare`\\n- Setting up alerts with `/alerts`\\n- Portfolio analysis with `/portfolio`\\n\\n*This information is for educational purposes only and does not constitute financial advice. Always do your own research and consult with a financial advisor before making investment decisions.*",
                        "data_quality": 85,
                        "status": "success"
                    }
                elif intent == 'help':
                    return {
                        "response": "🤖 **How I can help you:**\\n\\n**Stock Analysis:**\\n- `/analyze` - Detailed technical analysis\\n- `/compare` - Compare multiple stocks\\n- `/alerts` - Set up price alerts\\n\\n**Portfolio Management:**\\n- `/portfolio` - View your portfolio\\n- `/watchlist` - Manage watchlists\\n\\n**Market Data:**\\n- `/ask` - Ask questions about stocks\\n- `/status` - Check bot status\\n\\n*Type `/help` for more detailed command information.*",
                        "data_quality": 95,
                        "status": "success"
                    }
                elif intent == 'greeting':
                    return {
                        "response": "Hello! 👋 I'm your trading assistant. I can help you with stock prices, market analysis, and trading questions. How can I help you today?\\n\\n*This information is for educational purposes only and does not constitute financial advice. Always do your own research and consult with a financial advisor before making investment decisions.*",
                        "data_quality": 90,
                        "status": "success"
                    }
                else:
                    # General question - use AI to generate response
                    ai_generated = await self.ai_processor.process_query_for_intent(query)
                    if ai_generated and 'response' in ai_generated:
                        return {
                            "response": ai_generated['response'],
                            "data_quality": 80,
                            "status": "success"
                        }
                    else:
                        return {
                            "response": f"🤖 I understand you're asking: '{query}'\\n\\nI'm a trading assistant that can help with stock prices, market analysis, and trading advice. Could you be more specific about what you'd like to know?\\n\\n*Use `/help` to see all available commands.*",
                            "data_quality": 80,
                            "status": "success"
                        }
            else:
                # Fallback if AI fails
                return {
                    "response": f"🤖 I understand you're asking: '{query}'\\n\\nI'm a trading assistant that can help with stock prices, market analysis, and trading advice. Could you be more specific about what you'd like to know?\\n\\n*Use `/help` to see all available commands.*",
                    "data_quality": 80,
                    "status": "success"
                }
        except Exception as e:
            return {
                "response": f"❌ I encountered an error while processing your request: {str(e)}. Please try again later.\\n\\n*This information is for educational purposes only and does not constitute financial advice. Always do your own research and consult with a financial advisor before making investment decisions.*",
                "data_quality": 40,
                "status": "error"
            }

# Create pipeline instance
pipeline = AskPipeline()

# Export functions
def process_query(query: str, user_id: str, username: str, context: Optional[str] = None, **kwargs) -> Dict[str, Any]:
    """Process query using the pipeline"""
    return asyncio.run(pipeline.process_query(query, user_id, username, context, **kwargs))

default_pipeline = pipeline
__all__ = ['default_pipeline', 'process_query']
'''
    
    # Write the AI-powered pipeline
    with open('src/bot/pipeline/commands/ask/pipeline.py', 'w') as f:
        f.write(ai_pipeline)
    
    print("✅ Replaced regex with AI-powered symbol extraction and intent detection")

if __name__ == "__main__":
    fix_symbol_extraction()
