#!/usr/bin/env python3
"""
Fix the pipeline to handle the correct data structure from data providers
"""

def fix_pipeline_data_structure():
    """Fix the pipeline to handle correct data structure"""
    
    # Read the current pipeline file
    with open('src/bot/pipeline/commands/ask/pipeline.py', 'r') as f:
        content = f.read()
    
    # Fix the data structure handling
    content = content.replace(
        '''                        if price_data and price_data.get('success'):
                            price = price_data.get('price')
                            change = price_data.get('change')
                            change_percent = price_data.get('change_percent')''',
        '''                        if price_data and not price_data.get('error') and price_data.get('price'):
                            price = price_data.get('price')
                            # Calculate change if we have open price
                            open_price = price_data.get('open', price)
                            change = price - open_price if open_price else 0
                            change_percent = (change / open_price * 100) if open_price else 0'''
    )
    
    # Write the fixed content back
    with open('src/bot/pipeline/commands/ask/pipeline.py', 'w') as f:
        f.write(content)
    
    print("✅ Fixed pipeline data structure handling")

if __name__ == "__main__":
    fix_pipeline_data_structure()
