#!/usr/bin/env python3
"""
Quick fix to add the missing _generate_comprehensive_analysis method
"""

import re

def fix_missing_method():
    file_path = "src/shared/ai_services/ai_processor_robust.py"
    
    # Read the current file
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Find the right location to add the method (before the @dataclass line at end of CleanAIProcessor)
    # Look for the pattern that ends the CleanAIProcessor class
    pattern = r'(\s+\)\n\n\n\n@dataclass)'
    
    # The method to add
    method_code = '''
    async def _generate_comprehensive_analysis(self, query: str, market_data_results: List[Dict[str, Any]]) -> str:
        """Generate comprehensive analysis using real market data"""
        try:
            import os
            from openai import OpenAI
            
            # Prepare market data summary for AI analysis
            market_summary = []
            for result in market_data_results:
                symbol = result['symbol']
                data = result['data']
                current_price = data.get('current_price', 0)
                change_percent = data.get('change_percent', 0)
                volume = data.get('volume', 0)
                
                market_summary.append(f"""
{symbol}:
- Current Price: ${current_price:.2f}
- Change: {change_percent:+.2f}%
- Volume: {volume:,}
- Data Quality: {'High' if current_price > 0 else 'Low'}
""")
            
            # Create AI prompt with real market data
            analysis_prompt = f"""You are an expert financial analyst. Analyze the following REAL market data and provide specific, actionable trading recommendations.

USER QUERY: "{query}"

REAL MARKET DATA:
{chr(10).join(market_summary)}

CRITICAL REQUIREMENTS:
1. Use ONLY the real market data provided above
2. Do NOT generate or estimate any prices, indicators, or technical values
3. Provide specific, actionable recommendations based on the actual data
4. Include concrete entry points, stop losses, and price targets based on current prices
5. Be confident and specific in your analysis
6. Focus on the stock(s) with the best risk/reward based on real data

RESPONSE FORMAT:
- Start with your top recommendation
- Provide specific analysis using the real data
- Include concrete trading strategy with real price levels
- End with risk management and next steps

Remember: Use ONLY the real data provided - do not fabricate any numbers."""

            # Get AI analysis
            api_key = os.getenv('OPENROUTER_API_KEY', '')
            if not api_key:
                return self._generate_fallback_analysis_with_data(market_data_results)
            
            client = OpenAI(api_key=api_key, base_url=os.getenv('OPENROUTER_BASE_URL', 'https://openrouter.ai/api/v1'))
            model = router.get_model_for_market_analysis()
            
            response = client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": analysis_prompt}],
                temperature=0.7,
                max_tokens=2000
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            self.logger.error(f"Error generating comprehensive analysis: {e}")
            return self._generate_fallback_analysis_with_data(market_data_results)
    
    def _generate_fallback_analysis_with_data(self, market_data_results: List[Dict[str, Any]]) -> str:
        """Generate fallback analysis using real market data when AI is unavailable"""
        try:
            analysis_parts = ["# Real-Time Market Analysis\\n"]
            
            for result in market_data_results:
                symbol = result['symbol']
                data = result['data']
                current_price = data.get('current_price', 0)
                change_percent = data.get('change_percent', 0)
                volume = data.get('volume', 0)
                
                analysis_parts.append(f"""
## {symbol} Analysis
**Current Price:** ${current_price:.2f}
**Change:** {change_percent:+.2f}%
**Volume:** {volume:,}

**Trading Recommendation:** Based on current price of ${current_price:.2f}, consider:
- **Entry:** ${current_price * 0.98:.2f} - ${current_price * 1.02:.2f}
- **Stop Loss:** ${current_price * 0.95:.2f}
- **Target:** ${current_price * 1.05:.2f} - ${current_price * 1.10:.2f}

**Risk Management:** Position size based on 2% account risk. Monitor volume for confirmation.
""")
            
            analysis_parts.append("\\n*Analysis based on real-time market data. Past performance does not guarantee future results.*")
            
            return "\\n".join(analysis_parts)
            
        except Exception as e:
            self.logger.error(f"Error generating fallback analysis: {e}")
            return "Unable to generate analysis at this time. Please try again later."
'''
    
    # Replace the pattern with the method + original pattern
    replacement = method_code + '\n\n\n\n@dataclass'
    
    # Apply the fix
    new_content = re.sub(pattern, replacement, content)
    
    if new_content != content:
        # Write the fixed content back
        with open(file_path, 'w') as f:
            f.write(new_content)
        print("✅ Successfully added missing _generate_comprehensive_analysis method!")
        return True
    else:
        print("❌ Could not find the right pattern to insert the method")
        return False

if __name__ == "__main__":
    fix_missing_method()
