#!/usr/bin/env python3
"""
Fix the process_query method to accept context parameter and any other parameters
"""

def fix_context_parameter():
    """Add context parameter and make method flexible for any additional parameters"""
    
    # Read the current pipeline file
    with open('src/bot/pipeline/commands/ask/pipeline.py', 'r') as f:
        content = f.read()
    
    # Fix the process_query method signature to accept any additional parameters
    content = content.replace(
        "async def process_query(self, query: str, user_id: str = None, username: str = None) -> dict:",
        "async def process_query(self, query: str, user_id: str = None, username: str = None, context: dict = None, **kwargs) -> dict:"
    )
    
    # Also fix the exported process_query function
    content = content.replace(
        "async def process_query(query: str, user_id: str = None, username: str = None) -> dict:",
        "async def process_query(query: str, user_id: str = None, username: str = None, context: dict = None, **kwargs) -> dict:"
    )
    
    # Write the fixed content back
    with open('src/bot/pipeline/commands/ask/pipeline.py', 'w') as f:
        f.write(content)
    
    print("✅ Fixed context parameter and added **kwargs for flexibility")

if __name__ == "__main__":
    fix_context_parameter()
