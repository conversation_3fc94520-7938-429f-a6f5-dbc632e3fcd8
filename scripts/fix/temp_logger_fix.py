
import logging

# Quick fix for logger issue - create get_security_logger function
def get_security_logger():
    '''Temporary security logger function'''
    return logging.getLogger('security')

# Add to the logger module with error handling
try:
    import sys
    import importlib
    
    # Try to import the logger module
    logger_module = importlib.import_module('src.core.logger')
    # Add the function to the module
    logger_module.get_security_logger = get_security_logger
except ImportError as e:
    print(f"Warning: Could not import src.core.logger: {e}")
    print("get_security_logger will only be available in the current module.")

