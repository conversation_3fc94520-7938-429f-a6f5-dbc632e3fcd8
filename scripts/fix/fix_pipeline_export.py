#!/usr/bin/env python3
"""
Fix pipeline export to match executor expectations
"""

def fix_pipeline_export():
    """Add default_pipeline export"""
    
    # Read the current pipeline file
    with open('src/bot/pipeline/commands/ask/pipeline.py', 'r') as f:
        content = f.read()
    
    # Add default_pipeline export at the end
    content += "\n\n# Export default pipeline for executor\ndefault_pipeline = pipeline"
    
    # Write the fixed content back
    with open('src/bot/pipeline/commands/ask/pipeline.py', 'w') as f:
        f.write(content)
    
    print("✅ Added default_pipeline export")

if __name__ == "__main__":
    fix_pipeline_export()
