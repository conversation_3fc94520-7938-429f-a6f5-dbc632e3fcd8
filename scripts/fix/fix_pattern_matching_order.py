#!/usr/bin/env python3
"""
Fix the pattern matching order to prioritize trading advice over greetings
"""

def fix_pattern_matching_order():
    """Fix the pattern matching logic order"""
    
    # Read the current pipeline file
    with open('src/bot/pipeline/commands/ask/pipeline.py', 'r') as f:
        content = f.read()
    
    # Fix the pattern matching order - trading advice should come before greetings
    old_logic = '''            # Simple intent detection
            if any(word in cleaned_query for word in ['price', 'cost', 'value', 'worth', 'trading at', 'current price']):
                # Price query - extract symbol and get price
                symbol_match = re.search(r'\\b([A-Z]{1,5})\\b', query.upper())
                if symbol_match:
                    symbol = symbol_match.group(1)
                    try:
                        price_data = await self.data_provider_aggregator.get_ticker(symbol)
                        if 'price' in price_data:
                            result = {
                                "response": f"📊 **{symbol.upper()} Current Price:** ${price_data['price']:.2f}\\n\\n*Data provided for educational purposes only. Always do your own research before making investment decisions.*",
                                "data_quality": 95,
                                "status": "success"
                            }
                        else:
                            result = {
                                "response": f"❌ Could not retrieve current price for {symbol.upper()}. Please check the symbol and try again.\\n\\n*You can use `/analyze` for detailed technical analysis.*",
                                "data_quality": 60,
                                "status": "partial_success"
                            }
                    except Exception as e:
                        result = {
                            "response": f"❌ Error retrieving price data: {str(e)}\\n\\n*Please try again later or use `/analyze` for detailed analysis.*",
                            "data_quality": 40,
                            "status": "error"
                        }
                else:
                    result = {
                        "response": "❌ I couldn't identify a specific stock symbol in your query. Please mention a stock symbol (e.g., AAPL, MSFT, TSLA) for price information.\\n\\n*You can also use `/analyze` for detailed technical analysis.*",
                        "data_quality": 50,
                        "status": "partial_success"
                    }
            
            elif any(word in cleaned_query for word in ['bullish', 'bearish', 'recommend', 'suggest', 'advice', 'trading', 'strategy', 'buy', 'sell', 'hold']):
                # Trading advice query
                result = {
                    "response": "�� **Trading Advice:**\\n\\nI can help you with:\\n- Technical analysis using `/analyze`\\n- Stock comparisons with `/compare`\\n- Setting up alerts with `/alerts`\\n- Portfolio management\\n- Market sentiment analysis\\n\\n**For specific stock recommendations:**\\n- Use `/analyze` with a stock symbol for detailed analysis\\n- Use `/compare` to compare multiple stocks\\n- Use `/alerts` to track price movements\\n\\n**Remember:** This is for educational purposes only. Always do your own research and consider consulting with a financial advisor before making investment decisions.\\n\\nWhat specific trading question do you have?",
                    "data_quality": 85,
                    "status": "success"
                }
            
            elif any(word in cleaned_query for word in ['hello', 'hi', 'hey', 'good morning', 'good afternoon', 'good evening']):
                # Greeting
                result = {
                    "response": "Hello! 👋 I'm your trading assistant. I can help you with stock prices, market analysis, and trading questions. How can I help you today?\\n\\n*This information is for educational purposes only and does not constitute financial advice. Always do your own research and consult with a financial advisor before making investment decisions.*",
                    "data_quality": 90,
                    "status": "success"
                }'''
    
    new_logic = '''            # Simple intent detection - ORDER MATTERS!
            
            # 1. Trading advice (highest priority - check first)
            if any(word in cleaned_query for word in ['bullish', 'bearish', 'recommend', 'suggest', 'advice', 'trading', 'strategy', 'buy', 'sell', 'hold']):
                # Trading advice query
                result = {
                    "response": "💡 **Trading Advice:**\\n\\nI can help you with:\\n- Technical analysis using `/analyze`\\n- Stock comparisons with `/compare`\\n- Setting up alerts with `/alerts`\\n- Portfolio management\\n- Market sentiment analysis\\n\\n**For specific stock recommendations:**\\n- Use `/analyze` with a stock symbol for detailed analysis\\n- Use `/compare` to compare multiple stocks\\n- Use `/alerts` to track price movements\\n\\n**Remember:** This is for educational purposes only. Always do your own research and consider consulting with a financial advisor before making investment decisions.\\n\\nWhat specific trading question do you have?",
                    "data_quality": 85,
                    "status": "success"
                }
            
            # 2. Price queries (second priority)
            elif any(word in cleaned_query for word in ['price', 'cost', 'value', 'worth', 'trading at', 'current price']):
                # Price query - extract symbol and get price
                symbol_match = re.search(r'\\b([A-Z]{1,5})\\b', query.upper())
                if symbol_match:
                    symbol = symbol_match.group(1)
                    try:
                        price_data = await self.data_provider_aggregator.get_ticker(symbol)
                        if 'price' in price_data:
                            result = {
                                "response": f"📊 **{symbol.upper()} Current Price:** ${price_data['price']:.2f}\\n\\n*Data provided for educational purposes only. Always do your own research before making investment decisions.*",
                                "data_quality": 95,
                                "status": "success"
                            }
                        else:
                            result = {
                                "response": f"❌ Could not retrieve current price for {symbol.upper()}. Please check the symbol and try again.\\n\\n*You can use `/analyze` for detailed technical analysis.*",
                                "data_quality": 60,
                                "status": "partial_success"
                            }
                    except Exception as e:
                        result = {
                            "response": f"❌ Error retrieving price data: {str(e)}\\n\\n*Please try again later or use `/analyze` for detailed analysis.*",
                            "data_quality": 40,
                            "status": "error"
                        }
                else:
                    result = {
                        "response": "❌ I couldn't identify a specific stock symbol in your query. Please mention a stock symbol (e.g., AAPL, MSFT, TSLA) for price information.\\n\\n*You can also use `/analyze` for detailed technical analysis.*",
                        "data_quality": 50,
                        "status": "partial_success"
                    }
            
            # 3. Greetings (third priority - only if no trading terms)
            elif any(word in cleaned_query for word in ['hello', 'hi', 'hey', 'good morning', 'good afternoon', 'good evening']):
                # Greeting
                result = {
                    "response": "Hello! 👋 I'm your trading assistant. I can help you with stock prices, market analysis, and trading questions. How can I help you today?\\n\\n*This information is for educational purposes only and does not constitute financial advice. Always do your own research and consult with a financial advisor before making investment decisions.*",
                    "data_quality": 90,
                    "status": "success"
                }'''
    
    content = content.replace(old_logic, new_logic)
    
    # Write the fixed content back
    with open('src/bot/pipeline/commands/ask/pipeline.py', 'w') as f:
        f.write(content)
    
    print("✅ Fixed pattern matching order - trading advice now has priority")

if __name__ == "__main__":
    fix_pattern_matching_order()
