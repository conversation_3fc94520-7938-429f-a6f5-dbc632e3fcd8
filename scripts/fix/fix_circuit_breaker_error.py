#!/usr/bin/env python3
"""
Fix the circuit breaker error in AI client
"""

def fix_circuit_breaker_error():
    """Fix the missing circuit_breaker attribute"""
    
    # Read the AI client file
    with open('src/shared/ai_chat/ai_client.py', 'r') as f:
        content = f.read()
    
    # Find the classify_query method and fix the circuit breaker check
    old_circuit_check = '''        if not await self.circuit_breaker.can_execute():
            raise Exception("Circuit breaker is open")'''
    
    new_circuit_check = '''        # Check circuit breaker if available
        if hasattr(self, 'circuit_breaker') and self.circuit_breaker:
            if not await self.circuit_breaker.can_execute():
                raise Exception("Circuit breaker is open")'''
    
    content = content.replace(old_circuit_check, new_circuit_check)
    
    # Write the fixed content back
    with open('src/shared/ai_chat/ai_client.py', 'w') as f:
        f.write(content)
    
    print("✅ Fixed circuit breaker error")

if __name__ == "__main__":
    fix_circuit_breaker_error()
