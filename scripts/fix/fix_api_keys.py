#!/usr/bin/env python3
"""
Fix API key configuration issues
"""

import os
import re

def fix_api_keys():
    """Fix API key configuration issues"""
    
    # Check if .env file exists
    env_file = '.env'
    if not os.path.exists(env_file):
        print("❌ .env file not found. Please create one from .env.example")
        return
    
    # Read the .env file
    with open(env_file, 'r') as f:
        content = f.read()
    
    # Check for missing API keys
    missing_keys = []
    
    # Check for Polygon API key
    if 'POLYGON_API_KEY' not in content or 'your_polygon_api_key_here' in content:
        missing_keys.append('POLYGON_API_KEY')
    
    # Check for Finnhub API key
    if 'FINNHUB_API_KEY' not in content or 'your_finnhub_api_key_here' in content:
        missing_keys.append('FINNHUB_API_KEY')
    
    # Check for Alpha Vantage API key
    if 'ALPHA_VANTAGE_API_KEY' not in content or 'your_alpha_vantage_api_key_here' in content:
        missing_keys.append('ALPHA_VANTAGE_API_KEY')
    
    if missing_keys:
        print(f"⚠️ Missing or invalid API keys: {', '.join(missing_keys)}")
        print("Please update your .env file with valid API keys.")
        print("\nYou can get API keys from:")
        print("- Polygon: https://polygon.io/")
        print("- Finnhub: https://finnhub.io/")
        print("- Alpha Vantage: https://www.alphavantage.co/")
    else:
        print("✅ All API keys are configured")
    
    # Create a fallback configuration for when APIs fail
    fallback_config = '''
# Fallback configuration for when APIs fail
FALLBACK_DATA_SOURCES = ["yahoo", "finnhub_free"]
RATE_LIMIT_RETRY_ATTEMPTS = 3
RATE_LIMIT_BACKOFF_MULTIPLIER = 2
MAX_RATE_LIMIT_WAIT = 60
'''
    
    # Add fallback configuration if not present
    if 'FALLBACK_DATA_SOURCES' not in content:
        with open(env_file, 'a') as f:
            f.write(fallback_config)
        print("✅ Added fallback configuration")
    
    # Update the data provider manager to handle API failures gracefully
    with open('src/api/data/providers/data_source_manager.py', 'r') as f:
        content = f.read()
    
    # Add better error handling
    error_handling = '''
    async def _handle_api_error(self, provider_name: str, symbol: str, error: Exception):
        """Handle API errors with appropriate fallbacks"""
        error_str = str(error).lower()
        
        if "403" in error_str or "forbidden" in error_str:
            logger.error(f"API key invalid for {provider_name}: {error}")
            return "invalid_key"
        elif "429" in error_str or "rate limit" in error_str:
            logger.warning(f"Rate limit hit for {provider_name}: {error}")
            return "rate_limit"
        elif "404" in error_str or "not found" in error_str:
            logger.warning(f"Symbol not found for {provider_name}: {error}")
            return "not_found"
        else:
            logger.error(f"API error for {provider_name}: {error}")
            return "unknown_error"
    '''
    
    # Add error handling if not present
    if "def _handle_api_error" not in content:
        content = content.replace(
            "class DataSourceManager:",
            "class DataSourceManager:\n" + error_handling
        )
    
    with open('src/api/data/providers/data_source_manager.py', 'w') as f:
        f.write(content)
    
    print("✅ Enhanced API error handling")

if __name__ == "__main__":
    fix_api_keys()
