#!/usr/bin/env python3
"""
Add missing logger import to the pipeline
"""

def fix_logger_import():
    """Add logger import"""
    
    # Read the current pipeline file
    with open('src/bot/pipeline/commands/ask/pipeline.py', 'r') as f:
        content = f.read()
    
    # Add logger import at the top
    content = content.replace(
        "import asyncio\nimport time\nimport re",
        "import asyncio\nimport time\nimport re\nimport logging\n\nlogger = logging.getLogger(__name__)"
    )
    
    # Write the fixed content back
    with open('src/bot/pipeline/commands/ask/pipeline.py', 'w') as f:
        f.write(content)
    
    print("✅ Added logger import")

if __name__ == "__main__":
    fix_logger_import()
