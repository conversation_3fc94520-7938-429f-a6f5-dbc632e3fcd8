#!/usr/bin/env python3
"""
Fix Discord interaction acknowledgment issues
"""

import re

def fix_discord_interaction():
    """Fix Discord interaction acknowledgment issues"""
    
    # Read the current client.py file
    with open('src/bot/client.py', 'r') as f:
        content = f.read()
    
    # Fix 1: Improve the ask command to handle interaction properly
    ask_command_fix = '''        async def ask_command(interaction: discord.Interaction, query: Optional[str] = None, attachment: Optional[discord.Attachment] = None):
            """Ask the AI about trading and markets"""
            try:
                print(f"🚨 ASK COMMAND CALLED: query='{query}', attachment={attachment}")
                
                # Initialize optimization service
                optimization_service = await get_optimization_service()
                
                # Try to get cached response for simple queries
                if query:
                    cached_result, was_cached = await optimization_service.optimize_simple_query(
                        query, str(interaction.user.id)
                    )
                    
                    if was_cached and cached_result:
                        # Send cached response immediately
                        await interaction.response.send_message(cached_result)
                        print(f"✅ ASK COMPLETED SUCCESSFULLY (CACHED)")
                        return
                
                # Defer the interaction to prevent timeout
                try:
                    await interaction.response.defer(thinking=True)
                    print("✅ Successfully deferred interaction")
                except Exception as e:
                    print(f"⚠️ Failed to defer interaction: {e}. Continuing without defer.")
                
                # Process normally if not cached
                await self.handle_ask_command(interaction, query, attachment)
                print(f"✅ ASK COMMAND COMPLETED SUCCESSFULLY")
            except Exception as e:
                print(f"🚨 ERROR IN ASK COMMAND: {e}")
                import traceback
                traceback.print_exc()
                # Try to send error response if interaction not already responded
                try:
                    if not interaction.response.is_done():
                        await interaction.response.send_message(
                            "❌ I encountered an error while processing your request. Please try again later.",
                            ephemeral=True
                        )
                    else:
                        await interaction.followup.send(
                            "❌ I encountered an error while processing your request. Please try again later.",
                            ephemeral=True
                        )
                except Exception as send_error:
                    print(f"🚨 Failed to send error response: {send_error}")
                # Re-raise to trigger the error handler
                raise'''
    
    # Replace the ask command
    content = re.sub(
        r'async def ask_command\(interaction: discord\.Interaction, query: Optional\[str\] = None, attachment: Optional\[discord\.Attachment\] = None\):.*?(?=\n        # Zones command)',
        ask_command_fix,
        content,
        flags=re.DOTALL
    )
    
    # Fix 2: Improve the handle_ask_command method to not defer if already deferred
    handle_ask_fix = '''    async def handle_ask_command(self, interaction: discord.Interaction, query: Optional[str] = None, attachment: Optional[discord.Attachment] = None):
        """Handle the /ask command with AI-powered responses, batch query support, and voice input"""
        # Check if interaction is already deferred
        already_deferred = interaction.response.is_done()
        
        # Only defer if not already deferred
        if not already_deferred:
            try:
                if attachment is None:
                    # Always try to defer for non-attachment requests
                    await interaction.response.defer(thinking=True)
                    logger.info("✅ Successfully deferred interaction")
            except Exception as e:
                # Log the error but continue processing
                logger.warning(f"Failed to defer interaction: {e}. Continuing without defer.")
        else:
            logger.info("✅ Interaction already deferred, continuing with processing")'''
    
    # Replace the handle_ask_command method start
    content = re.sub(
        r'async def handle_ask_command\(self, interaction: discord\.Interaction, query: Optional\[str\] = None, attachment: Optional\[discord\.Attachment\] = None\):.*?(?=        # Prevent Discord interaction timeout)',
        handle_ask_fix,
        content,
        flags=re.DOTALL
    )
    
    # Write the fixed content back
    with open('src/bot/client.py', 'w') as f:
        f.write(content)
    
    print("✅ Fixed Discord interaction acknowledgment issues")

if __name__ == "__main__":
    fix_discord_interaction()
