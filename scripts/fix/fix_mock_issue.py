#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix the mock object issue with execute_ask_pipeline function.

This script identifies and fixes issues where the execute_ask_pipeline function
is being replaced with a mock object that raises exceptions instead of executing normally.
"""

import sys
import os
import importlib
import unittest.mock

def fix_mock_issue():
    """
    Fix the mock object issue by ensuring the execute_ask_pipeline function
    is properly restored to its original implementation.
    """
    try:
        # Import the module containing the function
        from src.bot.pipeline.commands.ask.pipeline import execute_ask_pipeline
        
        # Check if the function is a mock object
        if isinstance(execute_ask_pipeline, unittest.mock.Mock) or isinstance(execute_ask_pipeline, unittest.mock.MagicMock):
            print("WARNING: execute_ask_pipeline is currently a mock object!")
            print("Attempting to restore the original function...")
            
            # Try to reload the module to restore the original function
            import src.bot.pipeline.commands.ask.pipeline as pipeline_module
            importlib.reload(pipeline_module)
            
            # Check if the function is now the original
            if not (isinstance(pipeline_module.execute_ask_pipeline, unittest.mock.Mock) or 
                   isinstance(pipeline_module.execute_ask_pipeline, unittest.mock.MagicMock)):
                print("SUCCESS: execute_ask_pipeline has been restored to its original implementation.")
                return True
            else:
                print("ERROR: Failed to restore execute_ask_pipeline to its original implementation.")
                return False
        else:
            print("INFO: execute_ask_pipeline is already the original function, not a mock.")
            return True
            
    except Exception as e:
        print(f"ERROR: Failed to fix mock issue: {e}")
        return False

def check_imports():
    """
    Check if there are any incorrect imports that might be causing the issue.
    """
    try:
        # Check client.py imports
        from src.bot.client import execute_ask_pipeline as client_import
        from src.bot.pipeline.commands.ask.pipeline import execute_ask_pipeline as pipeline_import
        
        # Check if they're the same object
        if client_import is pipeline_import:
            print("INFO: Both imports refer to the same object.")
        else:
            print("WARNING: Client and pipeline imports refer to different objects!")
            
        return True
    except Exception as e:
        print(f"ERROR: Failed to check imports: {e}")
        return False

def main():
    """
    Main function to run the mock fix.
    """
    print("Checking and fixing execute_ask_pipeline mock issue...")
    
    # Check imports first
    if not check_imports():
        print("Failed to check imports.")
        return 1
    
    # Try to fix the mock issue
    if not fix_mock_issue():
        print("Failed to fix mock issue.")
        return 1
    
    print("Mock issue check and fix completed successfully.")
    return 0

if __name__ == "__main__":
    sys.exit(main())