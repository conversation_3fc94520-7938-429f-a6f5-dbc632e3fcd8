#!/usr/bin/env python3
"""
Create a simple ask command without optimization service dependency
"""

def fix_ask_command():
    """Fix the ask command to remove optimization service dependency"""
    
    # Read the current client file
    with open('src/bot/client.py', 'r') as f:
        content = f.read()
    
    # Simple ask command without optimization service
    simple_ask_command = '''        async def ask_command(interaction: discord.Interaction, query: Optional[str] = None, attachment: Optional[discord.Attachment] = None):
            """Ask the AI about trading and markets"""
            try:
                print(f"🚨 ASK COMMAND CALLED: query='{query}', attachment={attachment}")
                
                # Check if interaction is already responded to
                if interaction.response.is_done():
                    print("⚠️ Interaction already responded to, using followup")
                    # Use followup for already responded interactions
                    await self.handle_ask_command_followup(interaction, query, attachment)
                else:
                    # Defer the interaction to prevent timeout
                    try:
                        await interaction.response.defer(thinking=True)
                        print("✅ Successfully deferred interaction")
                    except Exception as e:
                        print(f"⚠️ Failed to defer interaction: {e}. Continuing without defer.")
                    
                    # Process normally
                    await self.handle_ask_command(interaction, query, attachment)
                
                print(f"✅ ASK COMMAND COMPLETED SUCCESSFULLY")
            except Exception as e:
                print(f"🚨 ERROR IN ASK COMMAND: {e}")
                import traceback
                traceback.print_exc()
                # Try to send error response if interaction not already responded
                try:
                    if not interaction.response.is_done():
                        await interaction.response.send_message(
                            "❌ I encountered an error while processing your request. Please try again later.",
                            ephemeral=True
                        )
                    else:
                        await interaction.followup.send(
                            "❌ I encountered an error while processing your request. Please try again later.",
                            ephemeral=True
                        )
                except Exception as send_error:
                    print(f"🚨 Failed to send error response: {send_error}")
                # Re-raise to trigger the error handler
                raise'''
    
    # Replace the ask command
    import re
    content = re.sub(
        r'async def ask_command\(interaction: discord\.Interaction, query: Optional\[str\] = None, attachment: Optional\[discord\.Attachment\] = None\):.*?(?=\n        # Zones command)',
        simple_ask_command,
        content,
        flags=re.DOTALL
    )
    
    # Write the fixed content back
    with open('src/bot/client.py', 'w') as f:
        f.write(content)
    
    print("✅ Fixed ask command to remove optimization service dependency")

if __name__ == "__main__":
    fix_ask_command()
