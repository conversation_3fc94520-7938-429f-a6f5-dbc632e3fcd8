#!/usr/bin/env python3
"""
Fix trading keywords to include bullish, bearish, and other trading terms
"""

def fix_trading_keywords():
    """Fix trading keywords detection"""
    
    # Read the current pipeline file
    with open('src/bot/pipeline/commands/ask/pipeline.py', 'r') as f:
        content = f.read()
    
    # Add more trading-related keywords
    content = content.replace(
        "['trading', 'strategy', 'advice', 'recommendation']",
        "['trading', 'strategy', 'advice', 'recommendation', 'bullish', 'bearish', 'buy', 'sell', 'hold', 'recommend', 'suggest', 'think', 'expect', 'forecast', 'prediction', 'outlook', 'trend', 'momentum', 'volatile', 'volatility']"
    )
    
    # Also improve the trading advice response
    content = content.replace(
        '"💡 **Trading Advice:**\\n\\nI can help you with technical analysis using `/analyze`, stock comparisons with `/compare`, setting up alerts with `/alerts`, and portfolio management.\\n\\n**Remember:** This is for educational purposes only. Always do your own research and consider consulting with a financial advisor before making investment decisions.\\n\\nWhat specific trading question do you have?"',
        '"💡 **Trading Advice:**\\n\\nI can help you with:\\n- Technical analysis using `/analyze`\\n- Stock comparisons with `/compare`\\n- Setting up alerts with `/alerts`\\n- Portfolio management\\n- Market sentiment analysis\\n- Trading strategies\\n\\n**For specific stock recommendations:**\\n- Use `/analyze` with a stock symbol for detailed analysis\\n- Use `/compare` to compare multiple stocks\\n- Use `/alerts` to track price movements\\n\\n**Remember:** This is for educational purposes only. Always do your own research and consider consulting with a financial advisor before making investment decisions.\\n\\nWhat specific trading question do you have?"'
    )
    
    # Write the fixed content back
    with open('src/bot/pipeline/commands/ask/pipeline.py', 'w') as f:
        f.write(content)
    
    print("✅ Fixed trading keywords detection")

if __name__ == "__main__":
    fix_trading_keywords()
