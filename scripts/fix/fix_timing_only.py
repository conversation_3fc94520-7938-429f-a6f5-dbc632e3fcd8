#!/usr/bin/env python3
"""
Fix only the pipeline timing issue without breaking other code
"""

def fix_pipeline_timing():
    """Fix the pipeline grader timing calculation"""
    
    # Read the current file
    with open('src/shared/monitoring/pipeline_grader.py', 'r') as f:
        content = f.read()
    
    # Fix 1: Change start_time initialization to use time.time()
    content = content.replace(
        'self.start_time = datetime.now()',
        'self.start_time = time.time()  # Use time.time() for consistent timing'
    )
    
    # Fix 2: Fix the execution time calculation
    content = content.replace(
        'end_time = datetime.now()\n            execution_time = (end_time - self.start_time).total_seconds()',
        'end_time = time.time()\n            execution_time = end_time - self.start_time  # Direct subtraction for time.time()'
    )
    
    # Fix 3: Fix the case when no steps are graded
    content = content.replace(
        'end_time=datetime.now()',
        'end_time=time.time()'
    )
    
    # Write the fixed content back
    with open('src/shared/monitoring/pipeline_grader.py', 'w') as f:
        f.write(content)
    
    print("✅ Fixed pipeline timing calculation")

if __name__ == "__main__":
    fix_pipeline_timing()
