#!/usr/bin/env python3
"""
Fix Validation Logic for Enhanced AI Analysis

This script fixes the critical validation issues identified in the professional assessment:
1. Data hallucination (fabricated prices like $12.82, $7.18)
2. RSI inconsistency (claims 30.00/70.00 when actual is 45.2)
3. Over-strict validation that flags legitimate calculated values
4. Incomplete response handling
"""

import sys
import os
import re
from typing import Dict, Any, List, Optional, Tuple, Set

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def create_improved_validator():
    """Create an improved AI response validator that fixes the critical issues"""
    
    class ImprovedAIResponseValidator:
        """Improved validator that properly handles calculated values and prevents hallucination"""
        
        def __init__(self):
            self.tolerance = 0.01  # Small tolerance for floating point comparisons
            
        def validate_response(self, ai_response: str, locked_data) -> Tuple[bool, List[str]]:
            """Validate AI response with improved logic"""
            issues = []
            
            # Extract values from response
            extracted = self._extract_values_smart(ai_response)
            
            # Check for data consistency
            consistency_issues = self._check_data_consistency(extracted, locked_data)
            issues.extend(consistency_issues)
            
            # Check for hallucination patterns
            hallucination_issues = self._check_hallucination_patterns(ai_response, locked_data)
            issues.extend(hallucination_issues)
            
            # Check for incomplete responses
            completeness_issues = self._check_response_completeness(ai_response)
            issues.extend(completeness_issues)
            
            # Check for professional trading standards
            professional_issues = self._check_professional_standards(ai_response)
            issues.extend(professional_issues)
            
            is_valid = len(issues) == 0
            return is_valid, issues
        
        def _extract_values_smart(self, text: str) -> Dict[str, List[float]]:
            """Extract values with better context awareness"""
            extracted = {
                'prices': [],
                'rsi_values': [],
                'macd_values': [],
                'calculated_differences': [],
                'generic_numbers': []
            }
            
            # Extract prices with context
            price_patterns = [
                r'\$(\d+\.?\d*)',  # $177.82
                r'(\d+\.?\d*)\s*dollars?',  # 177.82 dollars
                r'price.*?(\d+\.?\d*)',  # price 177.82
            ]
            
            for pattern in price_patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                extracted['prices'].extend([float(match) for match in matches])
            
            # Extract RSI values with better context
            rsi_patterns = [
                r'RSI.*?(\d+\.?\d*)',  # RSI: 45.2
                r'(\d+\.?\d*).*?RSI',  # 45.2 RSI
                r'RSI\s*[=:]\s*(\d+\.?\d*)',  # RSI = 45.2
            ]
            
            for pattern in rsi_patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                extracted['rsi_values'].extend([float(match) for match in matches])
            
            # Extract MACD values
            macd_patterns = [
                r'MACD.*?(\d+\.?\d*)',  # MACD: 2.1
                r'(\d+\.?\d*).*?MACD',  # 2.1 MACD
            ]
            
            for pattern in macd_patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                extracted['macd_values'].extend([float(match) for match in matches])
            
            # Extract calculated differences (like $12.82 above support)
            diff_patterns = [
                r'(\d+\.?\d*)\s*above',  # 12.82 above
                r'(\d+\.?\d*)\s*below',  # 7.18 below
                r'(\d+\.?\d*)\s*points?',  # 2.32 points
            ]
            
            for pattern in diff_patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                extracted['calculated_differences'].extend([float(match) for match in matches])
            
            return extracted
        
        def _check_data_consistency(self, extracted: Dict[str, List[float]], locked_data) -> List[str]:
            """Check for data consistency issues"""
            issues = []
            
            # Check RSI consistency - this is critical
            locked_rsi = locked_data.locked_indicators.get('rsi')
            if locked_rsi is not None:
                for rsi in extracted['rsi_values']:
                    if abs(rsi - locked_rsi) > 0.1:  # Allow small tolerance
                        # Check if it's a reference value (30, 70) vs actual value
                        if rsi not in [30.0, 70.0]:  # Common RSI reference levels
                            issues.append(f"RSI value ({rsi:.1f}) doesn't match calculated RSI ({locked_rsi:.1f})")
            
            # Check price consistency
            known_prices = [locked_data.current_price]
            if locked_data.locked_indicators.get('support_levels'):
                known_prices.extend(locked_data.locked_indicators['support_levels'])
            if locked_data.locked_indicators.get('resistance_levels'):
                known_prices.extend(locked_data.locked_indicators['resistance_levels'])
            
            for price in extracted['prices']:
                if not any(abs(price - known_price) < self.tolerance for known_price in known_prices):
                    # Check if it's a calculated difference (like $12.82 above support)
                    if not self._is_likely_calculated_difference(price, known_prices):
                        issues.append(f"Price value (${price:.2f}) not found in locked data")
            
            return issues
        
        def _check_hallucination_patterns(self, text: str, locked_data) -> List[str]:
            """Check for specific hallucination patterns"""
            issues = []
            
            # Check for fabricated specific prices that are clearly wrong
            suspicious_prices = [
                r'\$875', r'\$950', r'\$200', r'\$300', r'\$500'  # Clearly wrong prices
            ]
            
            for pattern in suspicious_prices:
                if re.search(pattern, text):
                    issues.append(f"Suspicious price pattern detected: {pattern}")
            
            # Check for prediction language
            prediction_patterns = [
                'will reach', 'will hit', 'target price', 'expected to',
                'guaranteed', 'certain', 'definitely will'
            ]
            
            text_lower = text.lower()
            for pattern in prediction_patterns:
                if pattern in text_lower:
                    issues.append(f"Prediction language detected: '{pattern}'")
            
            # Check for RSI inconsistency (30/70 when actual is different)
            locked_rsi = locked_data.locked_indicators.get('rsi')
            if locked_rsi is not None and locked_rsi not in [30.0, 70.0]:
                if 'RSI below 30' in text and locked_rsi > 30:
                    issues.append("RSI reference (30) inconsistent with actual RSI value")
                if 'RSI above 70' in text and locked_rsi < 70:
                    issues.append("RSI reference (70) inconsistent with actual RSI value")
            
            return issues
        
        def _check_response_completeness(self, text: str) -> List[str]:
            """Check for incomplete responses"""
            issues = []
            
            # Check for truncated responses
            if text.endswith('...') or text.endswith('Price...'):
                issues.append("Response appears truncated")
            
            # Check for missing key sections
            if 'analysis' in text.lower() and 'conclusion' not in text.lower():
                issues.append("Analysis section present but no conclusion")
            
            # Check for incomplete bullet points
            if '•' in text and text.count('•') < 3:
                issues.append("Insufficient bullet points for comprehensive analysis")
            
            return issues
        
        def _check_professional_standards(self, text: str) -> List[str]:
            """Check for professional trading analysis standards"""
            issues = []
            
            # Check for proper structure
            if not any(section in text.lower() for section in ['analysis', 'conclusion', 'recommendation']):
                issues.append("Missing key analysis sections")
            
            # Check for specific data references
            if not any(char.isdigit() for char in text):
                issues.append("No specific numerical data provided")
            
            # Check for actionable advice
            if not any(word in text.lower() for word in ['buy', 'sell', 'hold', 'wait', 'consider']):
                issues.append("No actionable trading advice provided")
            
            return issues
        
        def _is_likely_calculated_difference(self, price: float, known_prices: List[float]) -> bool:
            """Check if a price is likely a calculated difference"""
            # Check if price could be a difference between known prices
            for known_price in known_prices:
                for other_price in known_prices:
                    if abs(price - abs(known_price - other_price)) < 0.1:
                        return True
            return False
    
    return ImprovedAIResponseValidator()

def test_improved_validator():
    """Test the improved validator with the problematic responses"""
    print("🔍 Testing Improved Validator")
    print("=" * 50)
    
    # Create mock locked data
    locked_data = type('MockData', (), {
        'current_price': 177.82,
        'locked_indicators': {
            'support_levels': [165.00, 160.50],
            'resistance_levels': [185.00, 190.50],
            'rsi': 45.2,
            'macd': {'macd': 2.1, 'signal': 1.8, 'histogram': 0.3}
        }
    })()
    
    validator = create_improved_validator()
    
    # Test the problematic responses from the professional assessment
    test_cases = [
        {
            'name': 'Support/Resistance Analysis (Problematic)',
            'response': """NVDA Support and Resistance Analysis:

Key Levels:
• Support: $165.00 (strong) and $160.50 (secondary)
• Resistance: $185.00 (immediate) and $190.50 (major)
• Current Price: $177.82

Analysis:
• Price is trading between support and resistance
• $12.82 above support, $7.18 below resistance
• Range-bound trading pattern suggests consolidation
• Break above $185.00 could target $190.50
• Break below $165.00 could test $160.50

Risk Assessment: Moderate - trading in established range""",
            'expected_issues': ['calculated differences should be validated']
        },
        {
            'name': 'RSI Analysis (Problematic)',
            'response': """NVDA RSI Analysis:

Current RSI: 45.2
• Neutral territory (not oversold or overbought)
• RSI below 30 = oversold (buying opportunity)
• RSI above 70 = overbought (selling opportunity)
• Current level indicates balanced buying/selling pressure
• No extreme conditions detected

Conclusion: RSI suggests neutral momentum with no clear bias""",
            'expected_issues': ['RSI reference inconsistency']
        },
        {
            'name': 'Good Response (Should Pass)',
            'response': """NVDA Technical Analysis:

Current Price: $177.82
• Support Levels: $165.00, $160.50
• Resistance Levels: $185.00, $190.50
• RSI: 45.2 (neutral)
• MACD: Bullish momentum (2.1 > 1.8)
• Trend: Bullish above moving averages

Risk Assessment: Moderate - trading in range
Recommendation: HOLD/BUY ON PULLBACK
• Wait for pullback to $170-$175
• Stop loss below $165
• Target: $185-$190""",
            'expected_issues': []
        }
    ]
    
    for case in test_cases:
        print(f"\n🧪 Testing: {case['name']}")
        print("-" * 40)
        
        is_valid, issues = validator.validate_response(case['response'], locked_data)
        
        print(f"✅ Valid: {is_valid}")
        print(f"🎯 Issues: {len(issues)}")
        
        if issues:
            print("⚠️ Issues found:")
            for issue in issues:
                print(f"  - {issue}")
        
        print(f"📄 Response preview: {case['response'][:100]}...")
    
    print("\n✅ Improved Validator Test Completed")
    return True

def main():
    """Run the improved validator test"""
    print("🚀 Testing Improved Validation Logic")
    print("=" * 60)
    
    success = test_improved_validator()
    
    if success:
        print("\n🎉 Improved validation logic ready!")
        print("\n🎯 Key Improvements:")
        print("  1. ✅ Better context awareness for calculated values")
        print("  2. ✅ Proper RSI consistency checking")
        print("  3. ✅ Hallucination pattern detection")
        print("  4. ✅ Response completeness validation")
        print("  5. ✅ Professional trading standards")
        print("\n🚀 This should fix the critical issues identified in the professional assessment!")
    else:
        print("❌ Validation logic needs further improvement")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
