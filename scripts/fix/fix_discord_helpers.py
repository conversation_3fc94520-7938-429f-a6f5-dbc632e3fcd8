#!/usr/bin/env python3
"""
Fix Discord helpers to handle interactions more robustly
"""

def fix_discord_helpers():
    """Fix Discord helpers"""
    
    # Read the current helpers file
    with open('src/shared/utils/discord_helpers.py', 'r') as f:
        content = f.read()
    
    # Better safe_send_message method
    better_safe_send = '''    async def safe_send_message(
        interaction: discord.Interaction,
        content: str,
        ephemeral: bool = False
    ) -> bool:
        """
        Safely send a message with automatic length enforcement.
        
        Args:
            interaction: Discord interaction object
            content: Message content to send
            ephemeral: Whether message should be ephemeral
            
        Returns:
            True if message was sent successfully, False otherwise
        """
        try:
            # Enforce message length limit
            safe_content = DiscordMessageHelper.enforce_message_limit(content)

            logger.debug(f"Sending Discord message (len={len(safe_content)})")

            # Try to send via initial response first
            try:
                if not interaction.response.is_done():
                    await interaction.response.send_message(safe_content, ephemeral=ephemeral)
                    logger.info("Sent message via initial response")
                    return True
            except Exception as e:
                logger.warning(f"Initial response failed: {e}")

            # If initial response failed, try followup
            try:
                await interaction.followup.send(safe_content, ephemeral=ephemeral)
                logger.info("Sent message via followup")
                return True
            except Exception as e:
                logger.warning(f"Followup failed: {e}")

            # If both failed, try to defer and then send
            try:
                if not interaction.response.is_done():
                    await interaction.response.defer(thinking=True)
                await interaction.followup.send(safe_content, ephemeral=ephemeral)
                logger.info("Sent message via defer + followup")
                return True
            except Exception as e:
                logger.error(f"All send methods failed: {e}")
                return False

        except Exception as e:
            logger.error(f"Discord send error: {e}")
            return False'''
    
    # Replace the safe_send_message method
    import re
    content = re.sub(
        r'async def safe_send_message\(.*?return True',
        better_safe_send,
        content,
        flags=re.DOTALL
    )
    
    # Write the fixed content back
    with open('src/shared/utils/discord_helpers.py', 'w') as f:
        f.write(content)
    
    print("✅ Fixed Discord helpers with better interaction handling")

if __name__ == "__main__":
    fix_discord_helpers()
