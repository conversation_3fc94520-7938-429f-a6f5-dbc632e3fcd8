#!/usr/bin/env python3
"""
Fix Discord message sending to handle interaction acknowledgment properly
"""

def fix_discord_send():
    """Fix Discord message sending"""
    
    # Read the current client file
    with open('src/bot/client.py', 'r') as f:
        content = f.read()
    
    # Better ask command with improved interaction handling
    better_ask_command = '''        async def ask_command(interaction: discord.Interaction, query: Optional[str] = None, attachment: Optional[discord.Attachment] = None):
            """Ask the AI about trading and markets - Chatbot version"""
            try:
                print(f"🚨 ASK COMMAND CALLED: query='{query}', attachment={attachment}")
                
                # Process the query first
                await self.handle_ask_command(interaction, query, attachment)
                
                print(f"✅ ASK COMMAND COMPLETED SUCCESSFULLY")
            except Exception as e:
                print(f"🚨 ERROR IN ASK COMMAND: {e}")
                import traceback
                traceback.print_exc()
                # Try to send error response
                try:
                    if not interaction.response.is_done():
                        await interaction.response.send_message(
                            "❌ I encountered an error while processing your request. Please try again later.",
                            ephemeral=True
                        )
                    else:
                        await interaction.followup.send(
                            "❌ I encountered an error while processing your request. Please try again later.",
                            ephemeral=True
                        )
                except Exception as send_error:
                    print(f"🚨 Failed to send error response: {send_error}")
                raise'''
    
    # Replace the ask command
    import re
    content = re.sub(
        r'async def ask_command\(interaction: discord\.Interaction, query: Optional\[str\] = None, attachment: Optional\[discord\.Attachment\] = None\):.*?(?=\n        # Zones command)',
        better_ask_command,
        content,
        flags=re.DOTALL
    )
    
    # Write the fixed content back
    with open('src/bot/client.py', 'w') as f:
        f.write(content)
    
    print("✅ Fixed ask command with simplified interaction handling")

if __name__ == "__main__":
    fix_discord_send()
