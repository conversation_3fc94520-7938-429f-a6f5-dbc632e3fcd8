#!/usr/bin/env python3
"""
Replace regex pattern matching with AI-powered query interpretation
"""

def fix_ai_query_interpretation():
    """Replace regex with AI interpretation"""
    
    # Read the current pipeline file
    with open('src/bot/pipeline/commands/ask/pipeline.py', 'r') as f:
        content = f.read()
    
    # Replace the hardcoded pattern matching with AI interpretation
    new_pipeline = '''import asyncio
import time
import re
from src.shared.data_providers.aggregator import DataProviderAggregator
from src.shared.ai_services.intelligent_chatbot import IntelligentChatbot

class AskPipeline:
    def __init__(self):
        self.data_provider_aggregator = DataProviderAggregator()
        self.ai_chatbot = IntelligentChatbot()
    
    async def process_query(self, query: str) -> dict:
        """Process query using AI interpretation instead of regex patterns"""
        start_time = time.time()
        
        try:
            # Clean the query
            cleaned_query = query.strip().lower()
            
            # Use AI to interpret the query intent
            intent_analysis = await self._analyze_query_intent(query)
            
            # Process based on AI-determined intent
            if intent_analysis['intent'] == 'price_query':
                # Handle price queries with real data
                symbol = intent_analysis.get('symbol')
                if symbol:
                    try:
                        price_data = await self.data_provider_aggregator.get_ticker(symbol)
                        if 'price' in price_data:
                            result = {
                                "response": f"📊 **{symbol.upper()} Current Price:** ${price_data['price']:.2f}\\n\\n*Data provided for educational purposes only. Always do your own research before making investment decisions.*",
                                "data_quality": 95,
                                "status": "success"
                            }
                        else:
                            result = {
                                "response": f"❌ Could not retrieve current price for {symbol.upper()}. Please check the symbol and try again.\\n\\n*You can use `/analyze` for detailed technical analysis.*",
                                "data_quality": 60,
                                "status": "partial_success"
                            }
                    except Exception as e:
                        result = {
                            "response": f"❌ Error retrieving price data: {str(e)}\\n\\n*Please try again later or use `/analyze` for detailed analysis.*",
                            "data_quality": 40,
                            "status": "error"
                        }
                else:
                    result = {
                        "response": "❌ I couldn't identify a specific stock symbol in your query. Please mention a stock symbol (e.g., AAPL, MSFT, TSLA) for price information.\\n\\n*You can also use `/analyze` for detailed technical analysis.*",
                        "data_quality": 50,
                        "status": "partial_success"
                    }
            
            elif intent_analysis['intent'] == 'trading_advice':
                # Handle trading advice queries
                result = {
                    "response": f"💡 **Trading Advice:**\\n\\nI can help you with:\\n- Technical analysis using `/analyze`\\n- Stock comparisons with `/compare`\\n- Setting up alerts with `/alerts`\\n- Portfolio management\\n- Market sentiment analysis\\n- Trading strategies\\n\\n**For specific stock recommendations:**\\n- Use `/analyze` with a stock symbol for detailed analysis\\n- Use `/compare` to compare multiple stocks\\n- Use `/alerts` to track price movements\\n\\n**Remember:** This is for educational purposes only. Always do your own research and consider consulting with a financial advisor before making investment decisions.\\n\\nWhat specific trading question do you have?",
                    "data_quality": 85,
                    "status": "success"
                }
            
            elif intent_analysis['intent'] == 'greeting':
                # Handle greetings
                result = {
                    "response": "Hello! 👋 I'm your trading assistant. I can help you with stock prices, market analysis, and trading questions. How can I help you today?\\n\\n*This information is for educational purposes only and does not constitute financial advice. Always do your own research and consult with a financial advisor before making investment decisions.*",
                    "data_quality": 90,
                    "status": "success"
                }
            
            elif intent_analysis['intent'] == 'help':
                # Handle help requests
                result = {
                    "response": "🤖 **How I can help you:**\\n\\n**Stock Analysis:**\\n- `/analyze` - Detailed technical analysis\\n- `/compare` - Compare multiple stocks\\n- `/alerts` - Set up price alerts\\n\\n**Portfolio Management:**\\n- `/portfolio` - View your portfolio\\n- `/watchlist` - Manage watchlists\\n- `/recommendations` - Get stock recommendations\\n\\n**Market Data:**\\n- Ask me about stock prices (e.g., 'What is the price of AAPL?')\\n- Get market insights and trends\\n- Trading advice and strategies\\n\\n**Just ask me anything about trading or stocks!**",
                    "data_quality": 95,
                    "status": "success"
                }
            
            elif intent_analysis['intent'] == 'market_question':
                # Handle market-related questions
                result = {
                    "response": "📈 **Market Analysis:**\\n\\nI can help you understand market trends and analysis. For detailed insights:\\n\\n- Use `/analyze` for specific stock technical analysis\\n- Use `/compare` to compare market sectors\\n- Use `/alerts` to track market movements\\n- Ask me about specific stocks or market conditions\\n\\n**What specific market question do you have?**",
                    "data_quality": 80,
                    "status": "success"
                }
            
            else:
                # General response for other queries using AI
                ai_response = await self._get_ai_response(query)
                result = {
                    "response": ai_response,
                    "data_quality": 75,
                    "status": "success"
                }
            
            execution_time = time.time() - start_time
            logger.info(f"Query processed in {execution_time:.2f} seconds")
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing query: {e}")
            return {
                "response": "❌ I encountered an error while processing your request. Please try again later.",
                "data_quality": 0,
                "status": "error"
            }
    
    async def _analyze_query_intent(self, query: str) -> dict:
        """Use AI to analyze query intent instead of regex patterns"""
        try:
            # Use AI to determine intent
            intent_prompt = f"""Analyze this trading/finance query and determine the intent. Return a JSON response with:
- intent: one of ['price_query', 'trading_advice', 'greeting', 'help', 'market_question', 'general']
- symbol: stock symbol if it's a price query (extract from query)
- confidence: 0-100 confidence score

Query: "{query}"

Examples:
- "What is the price of AAPL?" → {{"intent": "price_query", "symbol": "AAPL", "confidence": 95}}
- "find me a stock you are bullish on" → {{"intent": "trading_advice", "symbol": null, "confidence": 90}}
- "Hello" → {{"intent": "greeting", "symbol": null, "confidence": 95}}
- "How can you help me?" → {{"intent": "help", "symbol": null, "confidence": 90}}
- "What's happening in the market?" → {{"intent": "market_question", "symbol": null, "confidence": 85}}

Return only the JSON response:"""
            
            # Get AI response
            ai_response = await self.ai_chatbot.process_query(intent_prompt)
            
            # Parse AI response (fallback to simple pattern matching if AI fails)
            try:
                import json
                # Extract JSON from AI response
                json_start = ai_response.find('{')
                json_end = ai_response.rfind('}') + 1
                if json_start != -1 and json_end > json_start:
                    json_str = ai_response[json_start:json_end]
                    intent_data = json.loads(json_str)
                    return intent_data
            except:
                pass
            
            # Fallback to simple pattern matching if AI fails
            query_lower = query.lower()
            
            # Check for price queries
            price_patterns = ['price', 'cost', 'value', 'worth', 'trading at', 'current price']
            if any(pattern in query_lower for pattern in price_patterns):
                # Extract symbol using regex as fallback
                symbol_match = re.search(r'\\b([A-Z]{1,5})\\b', query.upper())
                symbol = symbol_match.group(1) if symbol_match else None
                return {"intent": "price_query", "symbol": symbol, "confidence": 80}
            
            # Check for trading advice
            trading_patterns = ['bullish', 'bearish', 'buy', 'sell', 'recommend', 'suggest', 'advice', 'trading', 'strategy']
            if any(pattern in query_lower for pattern in trading_patterns):
                return {"intent": "trading_advice", "symbol": None, "confidence": 85}
            
            # Check for greetings
            greeting_patterns = ['hello', 'hi', 'hey', 'good morning', 'good afternoon', 'good evening']
            if any(pattern in query_lower for pattern in greeting_patterns):
                return {"intent": "greeting", "symbol": None, "confidence": 90}
            
            # Check for help
            help_patterns = ['help', 'what can you do', 'commands', 'how to']
            if any(pattern in query_lower for pattern in help_patterns):
                return {"intent": "help", "symbol": None, "confidence": 85}
            
            # Check for market questions
            market_patterns = ['market', 'trend', 'analysis', 'forecast', 'prediction']
            if any(pattern in query_lower for pattern in market_patterns):
                return {"intent": "market_question", "symbol": None, "confidence": 80}
            
            # Default to general
            return {"intent": "general", "symbol": None, "confidence": 70}
            
        except Exception as e:
            logger.error(f"Error analyzing query intent: {e}")
            # Fallback to general intent
            return {"intent": "general", "symbol": None, "confidence": 50}
    
    async def _get_ai_response(self, query: str) -> str:
        """Get AI response for general queries"""
        try:
            # Use AI chatbot for general responses
            ai_response = await self.ai_chatbot.process_query(query)
            return ai_response
        except Exception as e:
            logger.error(f"Error getting AI response: {e}")
            return f"🤖 I understand you're asking: '{query}'\\n\\nI'm a trading assistant that can help with stock prices, market analysis, and trading advice. Could you be more specific about what you'd like to know?"

# Create pipeline instance
pipeline = AskPipeline()

# Export the process_query function
async def process_query(query: str) -> dict:
    """Process a query using AI-powered interpretation"""
    return await pipeline.process_query(query)
'''
    
    # Write the new pipeline
    with open('src/bot/pipeline/commands/ask/pipeline.py', 'w') as f:
        f.write(new_pipeline)
    
    print("✅ Replaced regex patterns with AI-powered query interpretation")

if __name__ == "__main__":
    fix_ai_query_interpretation()
