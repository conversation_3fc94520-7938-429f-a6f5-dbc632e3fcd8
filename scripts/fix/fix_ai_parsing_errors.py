#!/usr/bin/env python3
"""
Fix AI response parsing errors in the query interpreter
"""

def fix_ai_parsing_errors():
    """Fix JSON parsing and string method errors"""
    
    # Read the current pipeline file
    with open('src/bot/pipeline/commands/ask/pipeline.py', 'r') as f:
        content = f.read()
    
    # Fix the AI response parsing logic
    old_parsing = '''            # Extract JSON from AI response
            json_start = ai_response.find('{')
            json_end = ai_response.rfind('}') + 1
            
            if json_start != -1 and json_end > json_start:
                json_str = ai_response[json_start:json_end]
                interpretation = json.loads(json_str)
                logger.info(f"AI interpreted query: {interpretation}")
                return interpretation
            else:
                # Fallback if AI response is malformed
                logger.warning(f"AI response malformed, using fallback: {ai_response}")
                return self._fallback_interpretation(query)'''
    
    new_parsing = '''            # Extract JSON from AI response - handle both string and dict responses
            if isinstance(ai_response, dict):
                # AI already returned a dict
                interpretation = ai_response
                logger.info(f"AI interpreted query: {interpretation}")
                return interpretation
            elif isinstance(ai_response, str):
                # Extract JSON from string response
                json_start = ai_response.find('{')
                json_end = ai_response.rfind('}') + 1
                
                if json_start != -1 and json_end > json_start:
                    try:
                        json_str = ai_response[json_start:json_end]
                        interpretation = json.loads(json_str)
                        logger.info(f"AI interpreted query: {interpretation}")
                        return interpretation
                    except json.JSONDecodeError as e:
                        logger.warning(f"JSON parsing failed: {e}, using fallback")
                        return self._fallback_interpretation(query)
                else:
                    # Fallback if AI response is malformed
                    logger.warning(f"AI response malformed, using fallback: {ai_response}")
                    return self._fallback_interpretation(query)
            else:
                # Unknown response type
                logger.warning(f"Unknown AI response type: {type(ai_response)}, using fallback")
                return self._fallback_interpretation(query)'''
    
    content = content.replace(old_parsing, new_parsing)
    
    # Also improve the fallback interpretation to be more robust
    old_fallback = '''    def _fallback_interpretation(self, query: str) -> dict:
        """Simple fallback when AI interpretation fails"""
        query_lower = query.lower()
        
        # Check for price queries
        if any(word in query_lower for word in ['price', 'cost', 'value', 'worth', 'trading at']):
            symbol_match = re.search(r'\\b([A-Z]{1,5})\\b', query.upper())
            return {
                "intent": "price_query",
                "symbol": symbol_match.group(1) if symbol_match else None,
                "confidence": 70,
                "user_wants": "stock price information",
                "suggested_response_type": "provide_price"
            }
        
        # Check for trading advice
        if any(word in query_lower for word in ['bullish', 'bearish', 'recommend', 'suggest', 'advice', 'trading']):
            return {
                "intent": "trading_advice",
                "symbol": None,
                "confidence": 70,
                "user_wants": "trading guidance",
                "suggested_response_type": "give_advice"
            }
        
        # Default to general chat
        return {
            "intent": "general_chat",
            "symbol": None,
            "confidence": 60,
            "user_wants": "general conversation",
            "suggested_response_type": "chat_response"
        }'''
    
    new_fallback = '''    def _fallback_interpretation(self, query: str) -> dict:
        """Simple fallback when AI interpretation fails"""
        try:
            query_lower = query.lower()
            
            # Check for price queries
            if any(word in query_lower for word in ['price', 'cost', 'value', 'worth', 'trading at']):
                symbol_match = re.search(r'\\b([A-Z]{1,5})\\b', query.upper())
                return {
                    "intent": "price_query",
                    "symbol": symbol_match.group(1) if symbol_match else None,
                    "confidence": 70,
                    "user_wants": "stock price information",
                    "suggested_response_type": "provide_price"
                }
            
            # Check for trading advice
            if any(word in query_lower for word in ['bullish', 'bearish', 'recommend', 'suggest', 'advice', 'trading']):
                return {
                    "intent": "trading_advice",
                    "symbol": None,
                    "confidence": 70,
                    "user_wants": "trading guidance",
                    "suggested_response_type": "give_advice"
                }
            
            # Default to general chat
            return {
                "intent": "general_chat",
                "symbol": None,
                "confidence": 60,
                "user_wants": "general conversation",
                "suggested_response_type": "chat_response"
            }
        except Exception as e:
            logger.error(f"Error in fallback interpretation: {e}")
            # Ultimate fallback
            return {
                "intent": "general_chat",
                "symbol": None,
                "confidence": 50,
                "user_wants": "general conversation",
                "suggested_response_type": "chat_response"
            }'''
    
    content = content.replace(old_fallback, new_fallback)
    
    # Write the fixed content back
    with open('src/bot/pipeline/commands/ask/pipeline.py', 'w') as f:
        f.write(content)
    
    print("✅ Fixed AI response parsing errors")

if __name__ == "__main__":
    fix_ai_parsing_errors()
