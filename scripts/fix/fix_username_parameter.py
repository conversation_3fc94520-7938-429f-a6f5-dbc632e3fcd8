#!/usr/bin/env python3
"""
Fix the process_query method to accept username parameter
"""

def fix_username_parameter():
    """Add username parameter to process_query method"""
    
    # Read the current pipeline file
    with open('src/bot/pipeline/commands/ask/pipeline.py', 'r') as f:
        content = f.read()
    
    # Fix the process_query method signature to include username
    content = content.replace(
        "async def process_query(self, query: str, user_id: str = None) -> dict:",
        "async def process_query(self, query: str, user_id: str = None, username: str = None) -> dict:"
    )
    
    # Also fix the exported process_query function
    content = content.replace(
        "async def process_query(query: str, user_id: str = None) -> dict:",
        "async def process_query(query: str, user_id: str = None, username: str = None) -> dict:"
    )
    
    # Write the fixed content back
    with open('src/bot/pipeline/commands/ask/pipeline.py', 'w') as f:
        f.write(content)
    
    print("✅ Fixed username parameter in process_query method")

if __name__ == "__main__":
    fix_username_parameter()
