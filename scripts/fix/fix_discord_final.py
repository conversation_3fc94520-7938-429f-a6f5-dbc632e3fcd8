#!/usr/bin/env python3
"""
Fix Discord interaction handling completely
"""

def fix_discord_interaction():
    """Fix Discord interaction handling to prevent all acknowledgment errors"""
    
    # Read the current client file
    with open('src/bot/client.py', 'r') as f:
        content = f.read()
    
    # Create a completely new ask command that handles interactions properly
    new_ask_command = '''        async def ask_command(interaction: discord.Interaction, query: Optional[str] = None, attachment: Optional[discord.Attachment] = None):
            """Ask the AI about trading and markets"""
            try:
                print(f"🚨 ASK COMMAND CALLED: query='{query}', attachment={attachment}")
                
                # Always try to defer first, but handle errors gracefully
                try:
                    if not interaction.response.is_done():
                        await interaction.response.defer(thinking=True)
                        print("✅ Successfully deferred interaction")
                    else:
                        print("⚠️ Interaction already responded to")
                except Exception as defer_error:
                    print(f"⚠️ Failed to defer interaction: {defer_error}. Continuing...")
                
                # Process the query
                await self.handle_ask_command(interaction, query, attachment)
                
                print(f"✅ ASK COMMAND COMPLETED SUCCESSFULLY")
            except Exception as e:
                print(f"🚨 ERROR IN ASK COMMAND: {e}")
                import traceback
                traceback.print_exc()
                # Try to send error response
                try:
                    if not interaction.response.is_done():
                        await interaction.response.send_message(
                            "❌ I encountered an error while processing your request. Please try again later.",
                            ephemeral=True
                        )
                    else:
                        await interaction.followup.send(
                            "❌ I encountered an error while processing your request. Please try again later.",
                            ephemeral=True
                        )
                except Exception as send_error:
                    print(f"🚨 Failed to send error response: {send_error}")
                raise'''
    
    # Replace the ask command
    import re
    content = re.sub(
        r'async def ask_command\(interaction: discord\.Interaction, query: Optional\[str\] = None, attachment: Optional\[discord\.Attachment\] = None\):.*?(?=\n        # Zones command)',
        new_ask_command,
        content,
        flags=re.DOTALL
    )
    
    # Write the fixed content back
    with open('src/bot/client.py', 'w') as f:
        f.write(content)
    
    print("✅ Fixed Discord interaction handling")

if __name__ == "__main__":
    fix_discord_interaction()
