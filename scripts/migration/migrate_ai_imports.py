#!/usr/bin/env python3
"""
AI Import Migration Script

Migrates all AI service imports to use the new UnifiedAIProcessor.
This script safely updates the 79 import statements identified in the audit.
"""

import os
import re
import json
from pathlib import Path
from typing import Dict, List, Tuple
from dataclasses import dataclass
from datetime import datetime

@dataclass
class ImportReplacement:
    """Represents an import replacement rule"""
    old_import: str
    new_import: str
    old_class: str
    new_class: str
    description: str

class AIImportMigrator:
    """Migrates AI imports to use the unified processor"""
    
    def __init__(self, root_dir: str = "."):
        self.root_dir = Path(root_dir)
        self.replacements = self._define_replacement_rules()
        self.migration_log = []
        
    def _define_replacement_rules(self) -> List[ImportReplacement]:
        """Define all import replacement rules"""
        return [
            # Primary AI Chat Processor replacements
            ImportReplacement(
                old_import="from src.shared.ai_services.ai_chat_processor import AIChatProcessorWrapper",
                new_import="from src.shared.ai_services.unified_ai_processor import UnifiedAIProcessor as AIChatProcessorWrapper",
                old_class="AIChatProcessorWrapper",
                new_class="UnifiedAIProcessor",
                description="Main AI chat processor wrapper"
            ),
            ImportReplacement(
                old_import="from src.shared.ai_services.ai_chat_processor import AIChatProcessor",
                new_import="from src.shared.ai_services.unified_ai_processor import UnifiedAIProcessor as AIChatProcessor",
                old_class="AIChatProcessor",
                new_class="UnifiedAIProcessor",
                description="AI chat processor class"
            ),
            ImportReplacement(
                old_import="from src.shared.ai_services.ai_chat_processor import create_processor",
                new_import="from src.shared.ai_services.unified_ai_processor import create_unified_processor as create_processor",
                old_class="create_processor",
                new_class="create_unified_processor",
                description="Processor factory function"
            ),
            ImportReplacement(
                old_import="from src.shared.ai_services.ai_chat_processor import processor",
                new_import="from src.shared.ai_services.unified_ai_processor import unified_ai_processor as processor",
                old_class="processor",
                new_class="unified_ai_processor",
                description="Global processor instance"
            ),
            
            # Robust AI Processor replacements
            ImportReplacement(
                old_import="from src.shared.ai_services.ai_processor_robust import CleanAIProcessor",
                new_import="from src.shared.ai_services.unified_ai_processor import UnifiedAIProcessor as CleanAIProcessor",
                old_class="CleanAIProcessor",
                new_class="UnifiedAIProcessor",
                description="Clean AI processor"
            ),
            ImportReplacement(
                old_import="from src.shared.ai_services.ai_processor_robust import RobustFinancialAnalyzer",
                new_import="from src.shared.ai_services.unified_ai_processor import UnifiedAIProcessor as RobustFinancialAnalyzer",
                old_class="RobustFinancialAnalyzer",
                new_class="UnifiedAIProcessor",
                description="Robust financial analyzer"
            ),
            ImportReplacement(
                old_import="from src.shared.ai_services.ai_processor_robust import QueryResult",
                new_import="from src.shared.ai_services.unified_ai_processor import ProcessingResult as QueryResult",
                old_class="QueryResult",
                new_class="ProcessingResult",
                description="Query result class"
            ),
            
            # AI Service Wrapper replacements
            ImportReplacement(
                old_import="from src.shared.ai_services.ai_service_wrapper import AIServiceWrapper",
                new_import="from src.shared.ai_services.unified_ai_processor import UnifiedAIProcessor as AIServiceWrapper",
                old_class="AIServiceWrapper",
                new_class="UnifiedAIProcessor",
                description="AI service wrapper"
            ),
            ImportReplacement(
                old_import="from src.shared.ai_services.ai_service_wrapper import ai_service",
                new_import="from src.shared.ai_services.unified_ai_processor import unified_ai_processor as ai_service",
                old_class="ai_service",
                new_class="unified_ai_processor",
                description="Global AI service instance"
            ),
        ]
    
    def run_migration(self, dry_run: bool = True) -> Dict[str, any]:
        """
        Run the complete migration process.
        
        Args:
            dry_run: If True, only analyze changes without modifying files
            
        Returns:
            Migration report
        """
        print(f"🔄 Starting AI import migration (dry_run={dry_run})...")
        
        # Load the audit report to get exact import locations
        audit_file = self.root_dir / "docs/audit/ai_dependency_audit_report.json"
        if not audit_file.exists():
            print("❌ Audit report not found. Run ai_dependency_audit.py first.")
            return {"status": "error", "message": "Audit report required"}
        
        with open(audit_file) as f:
            audit_data = json.load(f)
        
        # Process each import from the audit
        total_changes = 0
        files_modified = set()
        
        for module, imports in audit_data["import_details"].items():
            for import_info in imports:
                file_path = self.root_dir / import_info["file"]
                if file_path.exists():
                    changes = self._process_file(file_path, import_info, dry_run)
                    if changes > 0:
                        total_changes += changes
                        files_modified.add(str(file_path))
        
        # Generate migration report
        report = {
            "status": "success" if total_changes > 0 else "no_changes",
            "total_changes": total_changes,
            "files_modified": len(files_modified),
            "modified_files": list(files_modified),
            "migration_log": self.migration_log,
            "dry_run": dry_run
        }
        
        print(f"✅ Migration complete: {total_changes} changes in {len(files_modified)} files")
        return report
    
    def _process_file(self, file_path: Path, import_info: Dict, dry_run: bool) -> int:
        """Process a single file for import replacements"""
        try:
            content = file_path.read_text(encoding='utf-8')
            original_content = content
            changes_made = 0
            
            # Check each replacement rule
            for replacement in self.replacements:
                if replacement.old_import in content:
                    if not dry_run:
                        content = content.replace(replacement.old_import, replacement.new_import)
                    
                    changes_made += 1
                    self.migration_log.append({
                        "file": str(file_path),
                        "line": import_info.get("line", "unknown"),
                        "old_import": replacement.old_import,
                        "new_import": replacement.new_import,
                        "description": replacement.description
                    })
                    
                    print(f"  📝 {file_path}: {replacement.description}")
            
            # Write changes if not dry run
            if changes_made > 0 and not dry_run:
                file_path.write_text(content, encoding='utf-8')
            
            return changes_made
            
        except Exception as e:
            print(f"❌ Error processing {file_path}: {e}")
            return 0
    
    def generate_migration_report(self, report: Dict) -> str:
        """Generate a detailed migration report"""
        report_lines = [
            "# 🤖 AI Import Migration Report",
            f"**Date**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"**Status**: {report['status']}",
            f"**Dry Run**: {report['dry_run']}",
            "",
            "## 📊 Summary",
            f"- **Total Changes**: {report['total_changes']}",
            f"- **Files Modified**: {report['files_modified']}",
            "",
            "## 📋 Modified Files"
        ]
        
        for file_path in report['modified_files']:
            report_lines.append(f"- `{file_path}`")
        
        report_lines.extend([
            "",
            "## 🔄 Detailed Changes"
        ])
        
        for log_entry in report['migration_log']:
            report_lines.extend([
                f"### {log_entry['file']} (Line {log_entry['line']})",
                f"**Description**: {log_entry['description']}",
                f"```python",
                f"# OLD:",
                f"{log_entry['old_import']}",
                f"",
                f"# NEW:",
                f"{log_entry['new_import']}",
                f"```",
                ""
            ])
        
        return "\n".join(report_lines)


def main():
    """Run the migration"""
    import argparse
    from datetime import datetime
    
    parser = argparse.ArgumentParser(description="Migrate AI imports to unified processor")
    parser.add_argument("--dry-run", action="store_true", help="Analyze changes without modifying files")
    parser.add_argument("--execute", action="store_true", help="Execute the migration")
    
    args = parser.parse_args()
    
    if not args.dry_run and not args.execute:
        print("❌ Please specify either --dry-run or --execute")
        return
    
    migrator = AIImportMigrator()
    report = migrator.run_migration(dry_run=args.dry_run)
    
    # Save migration report
    report_file = f"docs/migration/ai_import_migration_{'dry_run' if args.dry_run else 'executed'}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
    os.makedirs(os.path.dirname(report_file), exist_ok=True)
    
    with open(report_file, 'w') as f:
        f.write(migrator.generate_migration_report(report))
    
    print(f"📋 Migration report saved to: {report_file}")
    
    if args.dry_run:
        print("\n🔍 This was a dry run. Use --execute to apply changes.")
    else:
        print("\n✅ Migration executed successfully!")


if __name__ == "__main__":
    main()
