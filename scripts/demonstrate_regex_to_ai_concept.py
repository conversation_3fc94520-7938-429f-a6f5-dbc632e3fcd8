#!/usr/bin/env python3
"""
Demonstrate Regex to AI Migration Concept

This script shows the conceptual difference between rigid regex patterns
and AI-powered text processing for financial text analysis.
"""

import re
import time
from typing import List, Dict, Any, Tuple
from dataclasses import dataclass

@dataclass
class ParseResult:
    """Result of text parsing"""
    symbols: List[str]
    prices: List[float]
    intent: str
    confidence: float
    method: str
    processing_time: float

class RegexParser:
    """Traditional regex-based parsing"""
    
    def __init__(self):
        # Rigid regex patterns
        self.symbol_patterns = [
            r'\$([A-Z]{1,5})\b',                    # $AAPL
            r'\b([A-Z]{2,5})\b(?:\s+(?:stock|shares))',  # AAPL stock
            r'(?:ticker|symbol):\s*([A-Z]{1,5})',   # ticker: AAPL
        ]
        
        self.price_patterns = [
            r'\$(\d+(?:\.\d{2})?)',                 # $150.25
            r'(\d+(?:\.\d+)?)\s*(?:dollars?|USD)',  # 150 dollars
            r'trading\s+at\s+\$?(\d+(?:\.\d{2})?)', # trading at $150
        ]
        
        self.intent_patterns = {
            'price_inquiry': [
                r'\b(?:what|current|latest)\s+(?:is\s+)?(?:the\s+)?price',
                r'\bhow\s+much\s+(?:is|does)',
                r'\bquote\s+for',
            ],
            'analysis_request': [
                r'\banalyze\s+',
                r'\banalysis\s+(?:of|for)',
                r'\btechnical\s+analysis',
            ],
            'recommendation': [
                r'\bshould\s+I\s+(?:buy|sell)',
                r'\brecommend(?:ation)?',
                r'\bgood\s+(?:buy|investment)',
            ]
        }
    
    def parse(self, text: str) -> ParseResult:
        """Parse text using rigid regex patterns"""
        start_time = time.time()
        
        # Extract symbols
        symbols = set()
        for pattern in self.symbol_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                symbol = match.group(1) if match.groups() else match.group(0)
                if symbol and 1 <= len(symbol) <= 5 and symbol.isupper():
                    symbols.add(symbol)
        
        # Extract prices
        prices = []
        for pattern in self.price_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                try:
                    price_str = match.group(1) if match.groups() else match.group(0)
                    price = float(price_str.replace('$', '').replace(',', ''))
                    if 0 < price < 100000:
                        prices.append(price)
                except ValueError:
                    continue
        
        # Detect intent
        intent = 'unknown'
        text_lower = text.lower()
        for intent_type, patterns in self.intent_patterns.items():
            for pattern in patterns:
                if re.search(pattern, text_lower):
                    intent = intent_type
                    break
            if intent != 'unknown':
                break
        
        processing_time = time.time() - start_time
        
        return ParseResult(
            symbols=list(symbols),
            prices=prices,
            intent=intent,
            confidence=0.7,  # Fixed confidence for regex
            method="regex",
            processing_time=processing_time
        )

class MockAIParser:
    """Mock AI-powered parsing to demonstrate the concept"""
    
    def __init__(self):
        # AI-like knowledge base for demonstration
        self.company_symbols = {
            'apple': 'AAPL',
            'microsoft': 'MSFT',
            'tesla': 'TSLA',
            'nvidia': 'NVDA',
            'amazon': 'AMZN',
            'google': 'GOOGL',
            'alphabet': 'GOOGL',
            'meta': 'META',
            'facebook': 'META',
            'amd': 'AMD',
            'intel': 'INTC',
            'bitcoin': 'BTC',
            'ethereum': 'ETH'
        }
        
        self.number_words = {
            'one': 1, 'two': 2, 'three': 3, 'four': 4, 'five': 5,
            'ten': 10, 'twenty': 20, 'thirty': 30, 'forty': 40, 'fifty': 50,
            'hundred': 100, 'thousand': 1000, 'million': 1000000, 'billion': 1000000000
        }
    
    def parse(self, text: str) -> ParseResult:
        """Parse text using AI-like intelligent processing"""
        start_time = time.time()
        
        text_lower = text.lower()
        words = text_lower.split()
        
        # Intelligent symbol extraction
        symbols = set()
        
        # Direct symbol matches (like regex)
        regex_symbols = re.findall(r'\$([A-Z]{1,5})\b', text)
        symbols.update(regex_symbols)
        
        # Company name recognition (AI advantage)
        for word in words:
            clean_word = re.sub(r'[^\w]', '', word)
            if clean_word in self.company_symbols:
                symbols.add(self.company_symbols[clean_word])
        
        # Context-aware symbol detection
        for i, word in enumerate(words):
            if word in ['stock', 'shares', 'ticker', 'company']:
                # Look for symbols near these keywords
                for j in range(max(0, i-3), min(len(words), i+4)):
                    potential_symbol = words[j].upper()
                    if re.match(r'^[A-Z]{2,5}$', potential_symbol):
                        symbols.add(potential_symbol)
        
        # Intelligent price extraction
        prices = []
        
        # Standard price patterns
        regex_prices = re.findall(r'\$(\d+(?:\.\d{2})?)', text)
        prices.extend([float(p) for p in regex_prices])
        
        # Written numbers (AI advantage)
        for i, word in enumerate(words):
            if word in self.number_words:
                value = self.number_words[word]
                # Check for multipliers
                if i + 1 < len(words) and words[i + 1] in self.number_words:
                    value *= self.number_words[words[i + 1]]
                # Check for currency context
                if any(currency in text_lower for currency in ['dollar', 'usd', '$']):
                    prices.append(float(value))
        
        # Intelligent intent detection
        intent = self._detect_intent_ai(text_lower)
        
        # Calculate confidence based on context
        confidence = self._calculate_confidence(text, symbols, prices, intent)
        
        processing_time = time.time() - start_time
        
        return ParseResult(
            symbols=list(symbols),
            prices=prices,
            intent=intent,
            confidence=confidence,
            method="ai_mock",
            processing_time=processing_time
        )
    
    def _detect_intent_ai(self, text: str) -> str:
        """AI-like intent detection with context understanding"""
        
        # Question words indicate inquiry
        if any(word in text for word in ['what', 'how', 'when', 'where', 'which']):
            if any(word in text for word in ['price', 'cost', 'worth', 'value', 'trading']):
                return 'price_inquiry'
            elif any(word in text for word in ['analyze', 'analysis', 'think', 'opinion']):
                return 'analysis_request'
        
        # Action words indicate recommendations
        if any(word in text for word in ['should', 'recommend', 'advice', 'suggest']):
            return 'recommendation'
        
        # Analysis keywords
        if any(word in text for word in ['analyze', 'analysis', 'technical', 'fundamental']):
            return 'analysis_request'
        
        # Comparison indicates analysis
        if any(word in text for word in ['vs', 'versus', 'compare', 'better', 'best']):
            return 'analysis_request'
        
        return 'general_question'
    
    def _calculate_confidence(self, text: str, symbols: List[str], prices: List[float], intent: str) -> float:
        """Calculate confidence based on parsing quality"""
        confidence = 0.5  # Base confidence
        
        # Boost confidence for clear symbols
        if symbols:
            confidence += 0.2
        
        # Boost confidence for clear prices
        if prices:
            confidence += 0.1
        
        # Boost confidence for clear intent
        if intent != 'general_question':
            confidence += 0.1
        
        # Boost confidence for financial keywords
        financial_keywords = ['stock', 'price', 'trading', 'market', 'invest', 'buy', 'sell']
        keyword_count = sum(1 for keyword in financial_keywords if keyword in text.lower())
        confidence += min(0.1, keyword_count * 0.02)
        
        return min(1.0, confidence)

class ComparisonDemo:
    """Demonstrate the differences between regex and AI parsing"""
    
    def __init__(self):
        self.regex_parser = RegexParser()
        self.ai_parser = MockAIParser()
        
        # Test cases that highlight AI advantages
        self.test_cases = [
            {
                'text': "What's the current price of Apple stock?",
                'description': "Company name recognition",
                'ai_advantage': "AI recognizes 'Apple' → 'AAPL'"
            },
            {
                'text': "Should I invest in Microsoft Corporation?",
                'description': "Company name + intent detection",
                'ai_advantage': "AI understands 'Microsoft Corporation' and investment intent"
            },
            {
                'text': "Tesla is trading around two hundred fifty dollars",
                'description': "Written numbers",
                'ai_advantage': "AI parses 'two hundred fifty dollars' → $250"
            },
            {
                'text': "Compare AMZN vs Google for long-term investment",
                'description': "Mixed symbol/company + comparison intent",
                'ai_advantage': "AI recognizes 'Google' → 'GOOGL' and comparison intent"
            },
            {
                'text': "Looking for tech stocks under fifty dollars",
                'description': "Written price + sector context",
                'ai_advantage': "AI understands 'fifty dollars' and sector context"
            },
            {
                'text': "NVDA earnings beat expectations, up 15% to $890",
                'description': "News context with percentage",
                'ai_advantage': "AI better understands news context and percentages"
            },
            {
                'text': "What do you think about cryptocurrency? BTC hit forty-five thousand",
                'description': "Large written numbers",
                'ai_advantage': "AI parses 'forty-five thousand' → 45000"
            },
            {
                'text': "Is Meta a good buy at current levels?",
                'description': "Company rebrand recognition",
                'ai_advantage': "AI knows Meta = Facebook = META ticker"
            }
        ]
    
    def run_demo(self):
        """Run the comparison demonstration"""
        print("🤖 REGEX vs AI TEXT PARSING DEMONSTRATION")
        print("=" * 80)
        print("\nThis demo shows how AI-powered parsing can be more flexible")
        print("and intelligent than rigid regex patterns for financial text.\n")
        
        total_regex_time = 0
        total_ai_time = 0
        regex_wins = 0
        ai_wins = 0
        
        for i, test_case in enumerate(self.test_cases, 1):
            print(f"\n📝 Test {i}: {test_case['description']}")
            print(f"   Text: \"{test_case['text']}\"")
            print(f"   AI Advantage: {test_case['ai_advantage']}")
            
            # Parse with regex
            regex_result = self.regex_parser.parse(test_case['text'])
            total_regex_time += regex_result.processing_time
            
            # Parse with AI
            ai_result = self.ai_parser.parse(test_case['text'])
            total_ai_time += ai_result.processing_time
            
            # Compare results
            print(f"\n   📝 REGEX Results:")
            print(f"      Symbols: {regex_result.symbols}")
            print(f"      Prices: {regex_result.prices}")
            print(f"      Intent: {regex_result.intent}")
            print(f"      Confidence: {regex_result.confidence:.2f}")
            
            print(f"\n   🤖 AI Results:")
            print(f"      Symbols: {ai_result.symbols}")
            print(f"      Prices: {ai_result.prices}")
            print(f"      Intent: {ai_result.intent}")
            print(f"      Confidence: {ai_result.confidence:.2f}")
            
            # Determine winner
            ai_score = self._score_result(ai_result, test_case)
            regex_score = self._score_result(regex_result, test_case)
            
            if ai_score > regex_score:
                print(f"   🏆 Winner: AI (score: {ai_score:.1f} vs {regex_score:.1f})")
                ai_wins += 1
            elif regex_score > ai_score:
                print(f"   🏆 Winner: Regex (score: {regex_score:.1f} vs {ai_score:.1f})")
                regex_wins += 1
            else:
                print(f"   🤝 Tie (both scored {ai_score:.1f})")
            
            print(f"   ⏱️  Time: AI {ai_result.processing_time:.4f}s, Regex {regex_result.processing_time:.4f}s")
        
        # Summary
        print("\n" + "=" * 80)
        print("📊 SUMMARY")
        print("=" * 80)
        
        print(f"\n🏆 Results:")
        print(f"   AI Wins: {ai_wins}")
        print(f"   Regex Wins: {regex_wins}")
        print(f"   Ties: {len(self.test_cases) - ai_wins - regex_wins}")
        
        print(f"\n⏱️  Performance:")
        print(f"   Total AI Time: {total_ai_time:.4f}s")
        print(f"   Total Regex Time: {total_regex_time:.4f}s")
        print(f"   Speed Winner: {'Regex' if total_regex_time < total_ai_time else 'AI'}")
        
        print(f"\n💡 Key AI Advantages Demonstrated:")
        print(f"   ✅ Company name recognition (Apple → AAPL)")
        print(f"   ✅ Written number parsing (fifty dollars → $50)")
        print(f"   ✅ Context-aware intent detection")
        print(f"   ✅ Flexible symbol extraction")
        print(f"   ✅ Better confidence scoring")
        
        print(f"\n🔧 Regex Advantages:")
        print(f"   ✅ Faster processing")
        print(f"   ✅ Deterministic results")
        print(f"   ✅ No API dependencies")
        print(f"   ✅ Perfect for simple patterns")
        
        print(f"\n🎯 Recommendation:")
        if ai_wins > regex_wins:
            print(f"   AI parsing shows clear advantages for financial text processing.")
            print(f"   Consider implementing AI with regex fallback for best results.")
        else:
            print(f"   Regex remains competitive for these test cases.")
            print(f"   Consider hybrid approach: regex for speed, AI for complex cases.")
    
    def _score_result(self, result: ParseResult, test_case: Dict) -> float:
        """Score a parsing result based on expected quality"""
        score = 0.0
        
        # Base score from confidence
        score += result.confidence * 2
        
        # Bonus for finding symbols
        if result.symbols:
            score += 1.0
        
        # Bonus for finding prices
        if result.prices:
            score += 0.5
        
        # Bonus for detecting intent (not unknown)
        if result.intent != 'unknown':
            score += 1.0
        
        # Penalty for processing time (prefer faster)
        if result.processing_time > 0.001:
            score -= min(0.5, result.processing_time * 100)
        
        return max(0.0, score)

def main():
    """Run the demonstration"""
    demo = ComparisonDemo()
    demo.run_demo()

if __name__ == "__main__":
    main()
