#!/usr/bin/env python3
"""
Test the Discord bot fixes for intent detection and data providers
"""

import asyncio
import sys
sys.path.insert(0, 'src')

async def test_intent_detection_fixes():
    """Test the enhanced intent detection with new intent types"""
    from shared.ai_services.enhanced_intent_detector import EnhancedIntentDetector, IntentType
    
    print("🧪 Testing Enhanced Intent Detection Fixes...")
    print("=" * 60)
    
    detector = EnhancedIntentDetector()
    
    # Test queries that should trigger the new intent types
    test_queries = [
        "What are the support and resistance levels for AAPL?",
        "Show me the support resistance analysis for TSLA",
        "I need support resistance levels for NVDA",
        "What's the current technical analysis for AAPL?",
        "Analyze the stock price for MSFT"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n{i}. Query: {query}")
        try:
            analysis = await detector.analyze_intent(query)
            print(f"   ✅ Intent: {analysis.primary_intent.value}")
            print(f"   ✅ Confidence: {analysis.confidence:.2f}")
            print(f"   ✅ Method: {analysis.method}")
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print("\n" + "=" * 60)
    print("✅ Intent detection testing completed!")

async def test_finnhub_provider_fix():
    """Test the Finnhub provider get_ticker method"""
    from shared.data_providers.finnhub_provider import FinnhubProvider
    
    print("\n🧪 Testing Finnhub Provider Fix...")
    print("=" * 60)
    
    provider = FinnhubProvider()
    
    # Test the get_ticker method exists
    if hasattr(provider, 'get_ticker'):
        print("✅ get_ticker method exists")
        
        # Test calling it (will fail due to no API key, but should not crash)
        try:
            result = await provider.get_ticker("AAPL")
            print(f"✅ get_ticker call successful: {type(result)}")
        except Exception as e:
            print(f"⚠️  get_ticker call failed (expected without API key): {e}")
    else:
        print("❌ get_ticker method missing")
    
    print("✅ Finnhub provider testing completed!")

async def test_json_parsing_improvements():
    """Test the improved JSON parsing"""
    from shared.ai_services.enhanced_intent_detector import EnhancedIntentDetector
    
    print("\n🧪 Testing JSON Parsing Improvements...")
    print("=" * 60)
    
    detector = EnhancedIntentDetector()
    
    # Test various malformed JSON responses
    test_responses = [
        '{"primary_intent": "support_resistance", "confidence": 0.9}',  # Valid JSON
        'Some text {"primary_intent": "technical_analysis"} more text',  # JSON in text
        'Invalid JSON {primary_intent: support_resistance}',  # Invalid JSON
        'No JSON here, just text about support and resistance',  # No JSON
        '',  # Empty response
        '{"primary_intent": "invalid_intent", "confidence": 0.9}'  # Invalid intent
    ]
    
    for i, response in enumerate(test_responses, 1):
        print(f"\n{i}. Response: {response[:50]}...")
        try:
            result = detector._parse_ai_response(response)
            if result:
                print(f"   ✅ Parsed successfully: {result.get('primary_intent', 'unknown')}")
            else:
                print(f"   ⚠️  No result (fallback used)")
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print("\n✅ JSON parsing testing completed!")

async def main():
    """Run all tests"""
    print("🚀 Testing Discord Bot Fixes")
    print("=" * 80)
    
    await test_intent_detection_fixes()
    await test_finnhub_provider_fix()
    await test_json_parsing_improvements()
    
    print("\n" + "=" * 80)
    print("🎉 All tests completed!")

if __name__ == "__main__":
    asyncio.run(main())
