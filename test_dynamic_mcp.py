#!/usr/bin/env python3
"""
Test Dynamic MCP Tool Usage
Demonstrates how AI can dynamically choose which MCP tools to use.
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_dynamic_mcp_pipeline():
    """Test the dynamic MCP pipeline."""
    print("🧪 Testing Dynamic MCP Pipeline...")
    
    try:
        from src.bot.pipeline.commands.ask.dynamic_mcp_pipeline import DynamicMCPAskCommand
        
        # Initialize command
        command = DynamicMCPAskCommand()
        print("✅ Dynamic MCP command initialized")
        
        # Test queries that should trigger different tool usage
        test_queries = [
            {
                "query": "What's the current price of AAPL?",
                "expected_tools": ["get_global_quote"],
                "description": "Simple price query"
            },
            {
                "query": "Give me technical analysis for AAPL including RSI and MACD",
                "expected_tools": ["get_rsi", "get_macd", "get_global_quote"],
                "description": "Technical analysis query"
            },
            {
                "query": "What's the news sentiment for NVDA?",
                "expected_tools": ["get_news_sentiment"],
                "description": "News sentiment query"
            },
            {
                "query": "Analyze TSLA comprehensively - price, technical indicators, news, and fundamentals",
                "expected_tools": ["get_comprehensive_analysis"],
                "description": "Comprehensive analysis query"
            },
            {
                "query": "Compare AAPL and MSFT performance",
                "expected_tools": ["get_global_quote", "get_time_series_daily"],
                "description": "Comparison query"
            }
        ]
        
        for i, test_case in enumerate(test_queries, 1):
            print(f"\n🔍 Test Case {i}: {test_case['description']}")
            print(f"Query: {test_case['query']}")
            print(f"Expected tools: {test_case['expected_tools']}")
            
            # Process query
            result = await command.ask(test_case['query'])
            
            # Display results
            print(f"✅ Success: {result['success']}")
            print(f"🔧 Tools used: {result['total_tool_calls']}")
            print(f"🤖 MCP tools used: {result['mcp_tools_used']}")
            print(f"⭐ Quality score: {result['quality_score']:.2f}")
            print(f"⏱️ Processing time: {result['processing_time']:.2f}s")
            
            # Show tool calls made
            if result['tool_calls']:
                print("🔧 Tool calls made:")
                for call in result['tool_calls']:
                    tool_name = call.get('tool', 'unknown')
                    success = call.get('result', {}).get('success', False)
                    status = "✅" if success else "❌"
                    print(f"  {status} {tool_name}")
            
            # Show response preview
            response = result.get('response', '')
            print(f"💬 Response preview: {response[:200]}...")
            
            if result.get('error'):
                print(f"❌ Error: {result['error']}")
        
        # Get pipeline stats
        print(f"\n📊 Pipeline Statistics:")
        stats = await command.get_stats()
        print(f"🤖 MCP available: {stats['mcp_available']}")
        print(f"🔧 Total tools: {stats['tools_available']}")
        print(f"🤖 MCP tools: {stats['mcp_tools_available']}")
        print(f"🧠 Model used: {stats['model_used']}")
        
        # Show tool execution stats
        tool_stats = stats.get('tool_execution_stats', {})
        if tool_stats:
            print(f"📈 Tool execution stats:")
            print(f"  Total executions: {tool_stats.get('total_executions', 0)}")
            print(f"  Successful: {tool_stats.get('successful_executions', 0)}")
            print(f"  Failed: {tool_stats.get('failed_executions', 0)}")
            print(f"  Success rate: {tool_stats.get('success_rate', 0):.1f}%")
        
        await command.close()
        return True
        
    except Exception as e:
        print(f"❌ Dynamic MCP pipeline test failed: {e}")
        return False

async def test_tool_registry():
    """Test the AI tool registry."""
    print("\n🧪 Testing AI Tool Registry...")
    
    try:
        from src.shared.ai_services.ai_tool_registry import AIToolRegistry, MCPToolManager
        from src.shared.data_providers.alpha_vantage_mcp import AlphaVantageMCPClient
        
        # Initialize MCP client and tool manager
        mcp_client = AlphaVantageMCPClient()
        tool_manager = MCPToolManager(mcp_client)
        registry = tool_manager.get_tool_registry()
        
        print(f"✅ Tool registry initialized")
        print(f"🔧 Total tools: {len(registry.tools)}")
        print(f"🤖 MCP tools: {len(registry.mcp_tools)}")
        
        # List available tools
        print(f"\n📋 Available tools:")
        for tool_name, tool in registry.tools.items():
            print(f"  {tool_name}: {tool.description}")
        
        # Test tool execution
        print(f"\n🔧 Testing tool execution...")
        
        # Test global quote tool
        if "get_global_quote" in registry.tools:
            print("Testing get_global_quote...")
            result = await registry.execute_tool("get_global_quote", {"symbol": "AAPL"})
            print(f"Result: {result.get('success', False)}")
            if result.get('success'):
                print(f"Quote data keys: {list(result.get('result', {}).keys())}")
        
        # Test RSI tool
        if "get_rsi" in registry.tools:
            print("Testing get_rsi...")
            result = await registry.execute_tool("get_rsi", {"symbol": "AAPL"})
            print(f"Result: {result.get('success', False)}")
            if result.get('success'):
                print(f"RSI data keys: {list(result.get('result', {}).keys())}")
        
        # Get execution stats
        stats = registry.get_execution_stats()
        print(f"\n📊 Tool execution stats:")
        print(f"  Total executions: {stats['total_executions']}")
        print(f"  Successful: {stats['successful_executions']}")
        print(f"  Failed: {stats['failed_executions']}")
        print(f"  Success rate: {stats['success_rate']:.1f}%")
        
        await tool_manager.close()
        return True
        
    except Exception as e:
        print(f"❌ Tool registry test failed: {e}")
        return False

async def test_enhanced_ai_client():
    """Test the enhanced AI client with tool access."""
    print("\n🧪 Testing Enhanced AI Client...")
    
    try:
        from src.shared.ai_services.enhanced_ai_client import EnhancedAIClient
        from src.shared.ai_services.ai_tool_registry import MCPToolManager
        from src.shared.data_providers.alpha_vantage_mcp import AlphaVantageMCPClient
        
        # Initialize MCP client and tool manager
        mcp_client = AlphaVantageMCPClient()
        tool_manager = MCPToolManager(mcp_client)
        registry = tool_manager.get_tool_registry()
        
        # Initialize AI client
        api_key = os.getenv('OPENAI_API_KEY', '')
        if not api_key:
            print("⚠️ OPENAI_API_KEY not found, skipping AI client test")
            return False
        
        ai_client = EnhancedAIClient(
            api_key=api_key,
            model="gpt-4o-mini",
            tool_registry=registry,
            mcp_tool_manager=tool_manager
        )
        
        print("✅ Enhanced AI client initialized")
        
        # Test queries
        test_queries = [
            "What's the current price of AAPL?",
            "Give me RSI analysis for MSFT",
            "What's the news sentiment for NVDA?"
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n🔍 Test Query {i}: {query}")
            
            result = await ai_client.generate_response_with_tools(query)
            
            print(f"✅ Success: {result['success']}")
            print(f"🔧 Tool calls: {result['total_tool_calls']}")
            print(f"⏱️ Processing time: {result['processing_time']:.2f}s")
            
            if result['tool_calls']:
                print("🔧 Tools used:")
                for call in result['tool_calls']:
                    tool_name = call.get('tool', 'unknown')
                    success = call.get('result', {}).get('success', False)
                    status = "✅" if success else "❌"
                    print(f"  {status} {tool_name}")
            
            print(f"💬 Response: {result['response'][:200]}...")
        
        # Get tool stats
        tool_stats = await ai_client.get_tool_stats()
        print(f"\n📊 Tool stats:")
        print(f"  Total executions: {tool_stats['total_executions']}")
        print(f"  Success rate: {tool_stats['success_rate']:.1f}%")
        
        await ai_client.close()
        return True
        
    except Exception as e:
        print(f"❌ Enhanced AI client test failed: {e}")
        return False

async def main():
    """Run all dynamic MCP tests."""
    print("🚀 Starting Dynamic MCP Tool Usage Tests")
    print("=" * 60)
    
    # Check API keys
    openai_key = os.getenv('OPENAI_API_KEY')
    alpha_vantage_key = os.getenv('ALPHA_VANTAGE_API_KEY')
    
    if not openai_key:
        print("⚠️ OPENAI_API_KEY not found")
        print("   AI client tests will be skipped")
    
    if not alpha_vantage_key:
        print("⚠️ ALPHA_VANTAGE_API_KEY not found")
        print("   MCP tests will use fallback providers")
    
    print("\n" + "=" * 60)
    
    # Run tests
    tests = [
        ("Tool Registry", test_tool_registry),
        ("Enhanced AI Client", test_enhanced_ai_client),
        ("Dynamic MCP Pipeline", test_dynamic_mcp_pipeline)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name} Test...")
        try:
            success = await test_func()
            results[test_name] = success
            print(f"✅ {test_name} Test: {'PASSED' if success else 'FAILED'}")
        except Exception as e:
            print(f"❌ {test_name} Test: FAILED - {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{test_name:20} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! Dynamic MCP integration is working correctly.")
        print("🤖 AI can now dynamically choose which MCP tools to use!")
    else:
        print("⚠️ Some tests failed. Check the logs above for details.")
    
    print("\n🚀 Dynamic MCP Test Complete!")

if __name__ == "__main__":
    asyncio.run(main())
