#!/usr/bin/env python3
"""
Test the /ask command with a specific trading question and manually grade the response
"""

import asyncio
import os
import sys
sys.path.insert(0, 'src')

async def test_ask_question():
    """Test the ask command with a specific question"""
    print("🧪 Testing /ask command with a trading question...")
    
    try:
        from bot.core.bot import create_bot
        from dotenv import load_dotenv
        
        # Load environment
        load_dotenv()
        
        # Create bot instance
        bot = create_bot()
        print("✅ Bot created successfully")
        
        # Load extensions
        await bot._load_extensions()
        print("✅ Extensions loaded")
        
        # Start the bot
        token = os.getenv('DISCORD_BOT_TOKEN')
        if not token:
            print("❌ DISCORD_BOT_TOKEN not found")
            return False
            
        print("🔄 Starting bot...")
        bot_task = asyncio.create_task(bot.start_bot())
        
        # Wait for bot to be ready
        await asyncio.sleep(5)
        
        if bot.bot.is_ready():
            print("✅ Bot is ready and connected to Discord!")
            print("🤖 Bot user:", bot.bot.user)
            print("🌐 Connected to", len(bot.bot.guilds), "servers")
            
            # Test question
            test_question = "What's the current technical analysis for AAPL? I'm looking for support and resistance levels."
            print(f"\n📝 Test Question: {test_question}")
            
            print("\n🔍 The bot should now respond to this question in Discord.")
            print("📊 Please check Discord and manually grade the response based on:")
            print("   1. Accuracy of technical analysis")
            print("   2. Quality of support/resistance levels")
            print("   3. Relevance to the question")
            print("   4. Professional presentation")
            print("   5. Use of real market data")
            
            # Wait for user to test
            print("\n⏳ Waiting for you to test the command in Discord...")
            print("Press Ctrl+C when done testing")
            
            try:
                await asyncio.sleep(60)  # Wait 1 minute for testing
            except KeyboardInterrupt:
                print("\n✅ Testing completed!")
            
            await bot.close()
            return True
        else:
            print("❌ Bot failed to connect")
            await bot.close()
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_ask_question())
    if success:
        print("\n🎉 Ask command test completed!")
        print("Please manually grade the response in Discord.")
    else:
        print("\n❌ Ask command test failed.")
