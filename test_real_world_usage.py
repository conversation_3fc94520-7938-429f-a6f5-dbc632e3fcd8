#!/usr/bin/env python3
"""
Real-World Usage Quality Test
============================

This script tests the system with realistic user scenarios to evaluate
the actual quality of the user experience.
"""

import asyncio
import time
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

class RealWorldTester:
    """Test real-world usage scenarios"""
    
    def __init__(self):
        self.results = []
    
    async def test_import_scenarios(self):
        """Test various import scenarios a developer might use"""
        print("🔍 Testing Real-World Import Scenarios...")
        
        scenarios = [
            ("Discord Bot Developer", "from src.bot.pipeline.commands.ask import execute_ask_pipeline"),
            ("Test Script Writer", "from src.bot.pipeline.commands.ask.executor import execute_ask_pipeline"),
            ("Batch Processing", "from src.bot.pipeline.commands.ask import execute_batch_ask_pipeline"),
            ("Analysis Tool", "from src.bot.pipeline.commands.analyze import execute_analyze_pipeline"),
            ("Performance Testing", "from src.bot.pipeline.commands.analyze import execute_parallel_analyze_pipeline"),
            ("Pipeline Class Usage", "from src.bot.pipeline.commands.ask import AskPipeline")
        ]
        
        for scenario_name, import_statement in scenarios:
            start_time = time.perf_counter()
            try:
                namespace = {}
                exec(import_statement, namespace)
                import_time = (time.perf_counter() - start_time) * 1000
                
                # Check if the imported function/class is callable
                imported_items = [item for item in namespace.values() if callable(item)]
                
                if imported_items:
                    self.results.append({
                        'scenario': scenario_name,
                        'status': 'SUCCESS',
                        'time_ms': round(import_time, 2),
                        'details': f"Imported {len(imported_items)} callable(s)"
                    })
                    print(f"  ✅ {scenario_name}: {import_time:.1f}ms")
                else:
                    self.results.append({
                        'scenario': scenario_name,
                        'status': 'WARNING',
                        'time_ms': round(import_time, 2),
                        'details': "No callable items imported"
                    })
                    print(f"  ⚠️  {scenario_name}: No callables imported")
                    
            except Exception as e:
                self.results.append({
                    'scenario': scenario_name,
                    'status': 'FAILED',
                    'time_ms': 0,
                    'details': str(e)
                })
                print(f"  ❌ {scenario_name}: {e}")
    
    async def test_developer_experience(self):
        """Test the developer experience with the API"""
        print("🔍 Testing Developer Experience...")
        
        try:
            # Test 1: Can a developer easily import what they need?
            from src.bot.pipeline.commands.ask import execute_ask_pipeline, AskPipeline
            from src.bot.pipeline.commands.analyze import execute_analyze_pipeline
            
            print("  ✅ Easy imports work")
            
            # Test 2: Are the functions properly documented?
            if execute_ask_pipeline.__doc__:
                print("  ✅ Functions have documentation")
            else:
                print("  ⚠️  Functions lack documentation")
            
            # Test 3: Can a developer instantiate classes?
            try:
                pipeline = AskPipeline()
                print("  ✅ Classes can be instantiated")
            except Exception as e:
                print(f"  ⚠️  Class instantiation issue: {e}")
            
            # Test 4: Are function signatures reasonable?
            import inspect
            sig = inspect.signature(execute_ask_pipeline)
            param_count = len(sig.parameters)
            
            if param_count <= 10:  # Reasonable number of parameters
                print(f"  ✅ Function signature is reasonable ({param_count} params)")
            else:
                print(f"  ⚠️  Function has many parameters ({param_count})")
            
            self.results.append({
                'scenario': 'Developer Experience',
                'status': 'SUCCESS',
                'details': 'Good API design and usability'
            })
            
        except Exception as e:
            print(f"  ❌ Developer experience issue: {e}")
            self.results.append({
                'scenario': 'Developer Experience',
                'status': 'FAILED',
                'details': str(e)
            })
    
    async def test_error_scenarios(self):
        """Test how the system handles common error scenarios"""
        print("🔍 Testing Error Scenarios...")
        
        error_tests = [
            ("Typo in module name", "from src.bot.pipeline.commands.askk import execute_ask_pipeline"),
            ("Typo in function name", "from src.bot.pipeline.commands.ask import execute_ask_pipelinee"),
            ("Wrong import path", "from src.bot.commands.ask import execute_ask_pipeline"),
            ("Non-existent submodule", "from src.bot.pipeline.commands.ask.nonexistent import something")
        ]
        
        for test_name, bad_import in error_tests:
            try:
                namespace = {}
                exec(bad_import, namespace)
                print(f"  ❌ {test_name}: Should have failed but didn't")
                self.results.append({
                    'scenario': f"Error Test: {test_name}",
                    'status': 'FAILED',
                    'details': 'Expected error but none occurred'
                })
            except ImportError as e:
                # This is expected
                error_msg = str(e)
                if len(error_msg) > 20 and 'module' in error_msg.lower():
                    print(f"  ✅ {test_name}: Good error message")
                    self.results.append({
                        'scenario': f"Error Test: {test_name}",
                        'status': 'SUCCESS',
                        'details': 'Informative error message'
                    })
                else:
                    print(f"  ⚠️  {test_name}: Error message could be better")
                    self.results.append({
                        'scenario': f"Error Test: {test_name}",
                        'status': 'WARNING',
                        'details': 'Error message lacks detail'
                    })
            except Exception as e:
                print(f"  ⚠️  {test_name}: Unexpected error type: {type(e).__name__}")
                self.results.append({
                    'scenario': f"Error Test: {test_name}",
                    'status': 'WARNING',
                    'details': f'Unexpected error type: {type(e).__name__}'
                })
    
    async def test_data_processing_quality(self):
        """Test the quality of data processing without external dependencies"""
        print("🔍 Testing Data Processing Quality...")
        
        try:
            # Test symbol extraction
            from src.shared.utils.symbol_extraction import extract_symbols_from_query
            
            test_queries = [
                ("Simple symbol", "What is AAPL doing?", ["AAPL"]),
                ("Multiple symbols", "Compare AAPL and GOOGL", ["AAPL", "GOOGL"]),
                ("With dollar sign", "How is $TSLA performing?", ["TSLA"]),
                ("Mixed case", "Tell me about aapl and MSFT", ["AAPL", "MSFT"]),
                ("No symbols", "How are you today?", [])
            ]
            
            extraction_quality = 0
            for test_name, query, expected in test_queries:
                try:
                    result = extract_symbols_from_query(query)
                    if isinstance(result, list):
                        # Check if we got reasonable results
                        if len(expected) == 0 and len(result) == 0:
                            extraction_quality += 1
                            print(f"  ✅ {test_name}: Correctly found no symbols")
                        elif len(expected) > 0 and len(result) > 0:
                            extraction_quality += 1
                            print(f"  ✅ {test_name}: Found symbols {result}")
                        else:
                            print(f"  ⚠️  {test_name}: Expected {expected}, got {result}")
                    else:
                        print(f"  ⚠️  {test_name}: Unexpected result type")
                except Exception as e:
                    print(f"  ❌ {test_name}: {e}")
            
            quality_score = (extraction_quality / len(test_queries)) * 100
            
            self.results.append({
                'scenario': 'Data Processing Quality',
                'status': 'SUCCESS' if quality_score >= 60 else 'WARNING',
                'details': f'Symbol extraction quality: {quality_score:.0f}%'
            })
            
        except Exception as e:
            print(f"  ❌ Data processing test failed: {e}")
            self.results.append({
                'scenario': 'Data Processing Quality',
                'status': 'FAILED',
                'details': str(e)
            })
    
    def generate_report(self):
        """Generate a summary report"""
        print("\n" + "=" * 60)
        print("📊 REAL-WORLD USAGE QUALITY REPORT")
        print("=" * 60)
        
        success_count = sum(1 for r in self.results if r['status'] == 'SUCCESS')
        warning_count = sum(1 for r in self.results if r['status'] == 'WARNING')
        failed_count = sum(1 for r in self.results if r['status'] == 'FAILED')
        total_count = len(self.results)
        
        print(f"Total Tests: {total_count}")
        print(f"✅ Success: {success_count}")
        print(f"⚠️  Warning: {warning_count}")
        print(f"❌ Failed: {failed_count}")
        print()
        
        success_rate = (success_count / total_count) * 100 if total_count > 0 else 0
        
        if success_rate >= 90:
            grade = "A"
            status = "🎉 EXCELLENT"
        elif success_rate >= 80:
            grade = "B"
            status = "✅ GOOD"
        elif success_rate >= 70:
            grade = "C"
            status = "⚠️  ACCEPTABLE"
        else:
            grade = "D"
            status = "❌ NEEDS IMPROVEMENT"
        
        print(f"Overall Quality: {success_rate:.1f}% ({grade}) - {status}")
        print()
        
        # Show details for failed/warning items
        issues = [r for r in self.results if r['status'] in ['FAILED', 'WARNING']]
        if issues:
            print("🔍 Issues Found:")
            for issue in issues:
                print(f"  {issue['status']} {issue['scenario']}: {issue['details']}")
        
        return success_rate

async def main():
    """Run real-world usage tests"""
    print("🚀 Real-World Usage Quality Assessment")
    print("=" * 60)
    
    tester = RealWorldTester()
    
    await tester.test_import_scenarios()
    print()
    await tester.test_developer_experience()
    print()
    await tester.test_error_scenarios()
    print()
    await tester.test_data_processing_quality()
    
    quality_score = tester.generate_report()
    
    return quality_score

if __name__ == "__main__":
    asyncio.run(main())
