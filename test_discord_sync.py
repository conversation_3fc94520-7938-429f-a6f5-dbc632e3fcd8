#!/usr/bin/env python3
"""
Test Discord command synchronization
"""

import asyncio
import os
import sys
sys.path.insert(0, 'src')

async def test_discord_sync():
    """Test Discord command synchronization"""
    print("🔄 Testing Discord command synchronization...")
    
    try:
        from bot.core.bot import create_bot
        
        # Create bot instance
        bot = create_bot()
        print("✅ Bot created successfully")
        
        # Load extensions
        await bot._load_extensions()
        print("✅ Extensions loaded")
        
        # Check commands before sync
        commands_before = [cmd.name for cmd in bot.bot.tree.walk_commands()]
        print(f"📋 Commands before sync: {commands_before}")
        
        # Sync commands with Discord
        print("🔄 Syncing commands with Discord...")
        synced = await bot.bot.tree.sync()
        print(f"✅ Synced {len(synced)} commands with Discord")
        
        # List synced commands
        for cmd in synced:
            print(f"   - {cmd.name}: {cmd.description}")
        
        # Check if ask command is in synced commands
        ask_synced = any(cmd.name == 'ask' for cmd in synced)
        if ask_synced:
            print("✅ /ask command successfully synced with Discord!")
        else:
            print("❌ /ask command was NOT synced with Discord")
            
        return ask_synced
        
    except Exception as e:
        print(f"❌ Sync test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_discord_sync())
    if success:
        print("\n🎉 Discord sync test completed successfully!")
        print("The /ask command should now be available in Discord.")
    else:
        print("\n❌ Discord sync test failed.")
