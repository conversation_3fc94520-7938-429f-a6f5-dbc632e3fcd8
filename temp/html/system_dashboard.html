<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Codebase Explorer Dashboard</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        :root {
            --primary: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary: #7c3aed;
            --dark: #1f2937;
            --light: #f9fafb;
            --gray: #6b7280;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --card-bg: #ffffff;
            --border: #e5e7eb;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background-color: #f3f4f6;
            color: var(--dark);
            line-height: 1.6;
        }

        .dashboard {
            display: flex;
            min-height: 100vh;
        }

        /* Sidebar Styles */
        .sidebar {
            width: 260px;
            background: linear-gradient(180deg, var(--primary), var(--primary-dark));
            color: white;
            padding: 20px 0;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
            z-index: 100;
        }

        .logo {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }

        .logo h1 {
            font-size: 1.5rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo i {
            font-size: 1.8rem;
        }

        .nav-links {
            list-style: none;
            padding: 0 10px;
        }

        .nav-links li {
            margin-bottom: 5px;
        }

        .nav-links a {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            color: rgba(255, 255, 255, 0.85);
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-links a:hover, .nav-links a.active {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .nav-links a i {
            margin-right: 12px;
            font-size: 1.1rem;
            width: 20px;
            text-align: center;
        }

        /* Main Content Styles */
        .main-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid var(--border);
        }

        .header h2 {
            font-size: 1.8rem;
            font-weight: 600;
            color: var(--dark);
        }

        .header-actions {
            display: flex;
            gap: 15px;
        }

        .btn {
            padding: 10px 20px;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-dark);
        }

        .btn-outline {
            background: transparent;
            border: 1px solid var(--primary);
            color: var(--primary);
        }

        .btn-outline:hover {
            background: var(--primary);
            color: white;
        }

        /* Dashboard Grid */
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: var(--card-bg);
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            padding: 20px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border);
        }

        .card-header h3 {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--dark);
        }

        .card-header i {
            font-size: 1.3rem;
            color: var(--primary);
        }

        /* System Health Card */
        .health-status {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .status-healthy {
            background: var(--success);
        }

        .status-degraded {
            background: var(--warning);
        }

        .health-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .health-item {
            display: flex;
            flex-direction: column;
        }

        .health-item .label {
            font-size: 0.85rem;
            color: var(--gray);
            margin-bottom: 5px;
        }

        .health-item .value {
            font-weight: 600;
            font-size: 1.1rem;
        }

        /* Circuit Breakers Card */
        .breaker-list {
            list-style: none;
        }

        .breaker-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid var(--border);
        }

        .breaker-item:last-child {
            border-bottom: none;
        }

        .breaker-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .breaker-state {
            padding: 4px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .state-closed {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success);
        }

        .state-open {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger);
        }

        .state-half-open {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning);
        }

        /* Cache Card */
        .cache-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: rgba(37, 99, 235, 0.05);
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }

        .stat-card .value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary);
            margin: 5px 0;
        }

        .stat-card .label {
            font-size: 0.9rem;
            color: var(--gray);
        }

        .cache-progress {
            height: 8px;
            background: var(--border);
            border-radius: 4px;
            overflow: hidden;
            margin: 15px 0;
        }

        .cache-progress-bar {
            height: 100%;
            background: var(--primary);
            border-radius: 4px;
        }

        /* Architecture Map */
        .architecture-container {
            height: 500px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            padding: 20px;
            margin-bottom: 30px;
            overflow: auto;
        }

        .architecture-map {
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .map-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--border);
        }

        .map-header h3 {
            font-size: 1.3rem;
            font-weight: 600;
        }

        .directory-tree {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .directory-item {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .directory-item:hover {
            background: rgba(37, 99, 235, 0.05);
        }

        .directory-item.folder {
            font-weight: 500;
        }

        .directory-item.file {
            margin-left: 20px;
            font-size: 0.9rem;
        }

        .directory-item i {
            margin-right: 10px;
            color: var(--primary);
        }

        .directory-item.folder i {
            color: var(--warning);
        }

        /* Pipeline Visualization */
        .pipeline-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            padding: 20px;
            margin-bottom: 30px;
        }

        .pipeline-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--border);
        }

        .pipeline-header h3 {
            font-size: 1.3rem;
            font-weight: 600;
        }

        .pipeline-stages {
            display: flex;
            justify-content: space-between;
            position: relative;
            margin: 40px 0;
        }

        .pipeline-stages::before {
            content: '';
            position: absolute;
            top: 20px;
            left: 50px;
            right: 50px;
            height: 4px;
            background: var(--border);
            z-index: 1;
        }

        .stage {
            display: flex;
            flex-direction: column;
            align-items: center;
            z-index: 2;
            position: relative;
        }

        .stage-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--primary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .stage.completed .stage-icon {
            background: var(--success);
        }

        .stage.active .stage-icon {
            background: var(--warning);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .stage-label {
            text-align: center;
            font-size: 0.9rem;
            max-width: 100px;
        }

        /* Real-time Events */
        .events-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            padding: 20px;
        }

        .events-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--border);
        }

        .events-header h3 {
            font-size: 1.3rem;
            font-weight: 600;
        }

        .events-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .event-item {
            padding: 12px 0;
            border-bottom: 1px solid var(--border);
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }

        .event-item:last-child {
            border-bottom: none;
        }

        .event-icon {
            font-size: 1.2rem;
            min-width: 24px;
            text-align: center;
        }

        .event-content {
            flex: 1;
        }

        .event-title {
            font-weight: 500;
            margin-bottom: 5px;
        }

        .event-meta {
            display: flex;
            gap: 15px;
            font-size: 0.85rem;
            color: var(--gray);
        }

        .event-timestamp {
            font-size: 0.8rem;
            color: var(--gray);
            margin-top: 5px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .dashboard {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                padding: 15px;
            }

            .sidebar nav {
                margin-bottom: 20px;
            }

            .nav-links {
                display: flex;
                flex-wrap: wrap;
                gap: 5px;
            }

            .nav-links li {
                margin-bottom: 0;
            }

            .nav-links a {
                padding: 8px 12px;
                font-size: 0.9rem;
            }

            .nav-links a i {
                margin-right: 5px;
            }

            .main-content {
                padding: 15px;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .pipeline-stages {
                flex-direction: column;
                gap: 30px;
            }

            .pipeline-stages::before {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="logo">
                <h1><i class="fas fa-project-diagram"></i> Codebase Explorer</h1>
            </div>
            <nav>
                <ul class="nav-links">
                    <li><a href="#" class="active"><i class="fas fa-map-marked-alt"></i> Architecture Map</a></li>
                    <li><a href="#"><i class="fas fa-stream"></i> Pipeline Visualizer</a></li>
                    <li><a href="#"><i class="fas fa-cogs"></i> Configuration</a></li>
                    <li><a href="#"><i class="fas fa-heartbeat"></i> Live State</a></li>
                    <li><a href="#"><i class="fas fa-history"></i> Event Log</a></li>
                </ul>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="header">
                <h2>System Dashboard</h2>
                <div class="header-actions">
                    <button class="btn btn-outline">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                    <button class="btn btn-primary">
                        <i class="fas fa-download"></i> Export Report
                    </button>
                </div>
            </div>

            <!-- Dashboard Cards -->
            <div class="dashboard-grid">
                <!-- System Health Card -->
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-heartbeat"></i> System Health</h3>
                        <i class="fas fa-ellipsis-h"></i>
                    </div>
                    <div class="health-status">
                        <div class="status-indicator status-healthy"></div>
                        <div class="status-text">System is healthy</div>
                    </div>
                    <div class="health-details">
                        <div class="health-item">
                            <div class="label">Uptime</div>
                            <div class="value">99.98%</div>
                        </div>
                        <div class="health-item">
                            <div class="label">Response Time</div>
                            <div class="value">142ms</div>
                        </div>
                        <div class="health-item">
                            <div class="label">Active Users</div>
                            <div class="value">24</div>
                        </div>
                        <div class="health-item">
                            <div class="label">Error Rate</div>
                            <div class="value">0.02%</div>
                        </div>
                    </div>
                </div>

                <!-- Circuit Breakers Card -->
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-bolt"></i> Circuit Breakers</h3>
                        <i class="fas fa-ellipsis-h"></i>
                    </div>
                    <ul class="breaker-list">
                        <li class="breaker-item">
                            <div class="breaker-info">
                                <i class="fas fa-microchip"></i>
                                <span>AI Service</span>
                            </div>
                            <span class="breaker-state state-closed">CLOSED</span>
                        </li>
                        <li class="breaker-item">
                            <div class="breaker-info">
                                <i class="fas fa-database"></i>
                                <span>Data Provider (Yahoo)</span>
                            </div>
                            <span class="breaker-state state-open">OPEN</span>
                        </li>
                        <li class="breaker-item">
                            <div class="breaker-info">
                                <i class="fas fa-database"></i>
                                <span>Data Provider (Polygon)</span>
                            </div>
                            <span class="breaker-state state-half-open">HALF-OPEN</span>
                        </li>
                        <li class="breaker-item">
                            <div class="breaker-info">
                                <i class="fas fa-chart-line"></i>
                                <span>Market Analysis</span>
                            </div>
                            <span class="breaker-state state-closed">CLOSED</span>
                        </li>
                    </ul>
                </div>

                <!-- Cache Card -->
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-memory"></i> Cache Status</h3>
                        <i class="fas fa-ellipsis-h"></i>
                    </div>
                    <div class="cache-stats">
                        <div class="stat-card">
                            <div class="value">127</div>
                            <div class="label">Items</div>
                        </div>
                        <div class="stat-card">
                            <div class="value">87%</div>
                            <div class="label">Hit Rate</div>
                        </div>
                    </div>
                    <div class="cache-progress">
                        <div class="cache-progress-bar" style="width: 65%"></div>
                    </div>
                    <div class="health-item">
                        <div class="label">Memory Usage</div>
                        <div class="value">256 KB / 10 MB</div>
                    </div>
                </div>
            </div>

            <!-- Architecture Map -->
            <div class="architecture-container">
                <div class="map-header">
                    <h3><i class="fas fa-sitemap"></i> Architecture Map</h3>
                    <div>
                        <button class="btn btn-outline">
                            <i class="fas fa-expand"></i> Expand All
                        </button>
                    </div>
                </div>
                <div class="architecture-map">
                    <div class="directory-tree">
                        <div class="directory-item folder">
                            <i class="fas fa-folder"></i>
                            src
                        </div>
                        <div class="directory-item folder">
                            <i class="fas fa-folder"></i>
                            bot
                        </div>
                        <div class="directory-item folder">
                            <i class="fas fa-folder"></i>
                            extensions
                        </div>
                        <div class="directory-item file">
                            <i class="fas fa-file-code"></i>
                            ask.py
                        </div>
                        <div class="directory-item file">
                            <i class="fas fa-file-code"></i>
                            analyze.py
                        </div>
                        <div class="directory-item file">
                            <i class="fas fa-file-code"></i>
                            watchlist.py
                        </div>
                        <div class="directory-item folder">
                            <i class="fas fa-folder"></i>
                            pipeline
                        </div>
                        <div class="directory-item folder">
                            <i class="fas fa-folder"></i>
                            commands
                        </div>
                        <div class="directory-item folder">
                            <i class="fas fa-folder"></i>
                            ask
                        </div>
                        <div class="directory-item file">
                            <i class="fas fa-file-code"></i>
                            executor.py
                        </div>
                        <div class="directory-item folder">
                            <i class="fas fa-folder"></i>
                            stages
                        </div>
                        <div class="directory-item file">
                            <i class="fas fa-file-code"></i>
                            ai_chat_processor.py
                        </div>
                        <div class="directory-item file">
                            <i class="fas fa-file-code"></i>
                            query_analyzer.py
                        </div>
                        <div class="directory-item folder">
                            <i class="fas fa-folder"></i>
                            shared
                        </div>
                        <div class="directory-item folder">
                            <i class="fas fa-folder"></i>
                            ai_services
                        </div>
                        <div class="directory-item file">
                            <i class="fas fa-file-code"></i>
                            ai_chat_processor.py
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pipeline Visualization -->
            <div class="pipeline-container">
                <div class="pipeline-header">
                    <h3><i class="fas fa-project-diagram"></i> Pipeline Visualization</h3>
                    <div>
                        <button class="btn btn-outline">
                            <i class="fas fa-play"></i> Start Monitoring
                        </button>
                    </div>
                </div>
                <div class="pipeline-stages">
                    <div class="stage completed">
                        <div class="stage-icon">1</div>
                        <div class="stage-label">Query Analysis</div>
                    </div>
                    <div class="stage completed">
                        <div class="stage-icon">2</div>
                        <div class="stage-label">Data Collection</div>
                    </div>
                    <div class="stage active">
                        <div class="stage-icon">3</div>
                        <div class="stage-label">AI Processing</div>
                    </div>
                    <div class="stage">
                        <div class="stage-icon">4</div>
                        <div class="stage-label">Response Generation</div>
                    </div>
                </div>
            </div>

            <!-- Real-time Events -->
            <div class="events-container">
                <div class="events-header">
                    <h3><i class="fas fa-bolt"></i> Real-time Events</h3>
                    <div>
                        <button class="btn btn-outline">
                            <i class="fas fa-pause"></i> Pause
                        </button>
                    </div>
                </div>
                <div class="events-list">
                    <div class="event-item">
                        <div class="event-icon" style="color: var(--success);">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="event-content">
                            <div class="event-title">Pipeline stage completed</div>
                            <div class="event-meta">
                                <span>Pipeline: ask</span>
                                <span>Stage: data_collection</span>
                            </div>
                            <div class="event-timestamp">2 seconds ago</div>
                        </div>
                    </div>
                    <div class="event-item">
                        <div class="event-icon" style="color: var(--primary);">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <div class="event-content">
                            <div class="event-title">Metric updated</div>
                            <div class="event-meta">
                                <span>Metric: processing_time</span>
                                <span>Value: 0.45s</span>
                            </div>
                            <div class="event-timestamp">5 seconds ago</div>
                        </div>
                    </div>
                    <div class="event-item">
                        <div class="event-icon" style="color: var(--warning);">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="event-content">
                            <div class="event-title">Circuit breaker triggered</div>
                            <div class="event-meta">
                                <span>Breaker: data_provider_yahoo</span>
                                <span>State: OPEN</span>
                            </div>
                            <div class="event-timestamp">12 seconds ago</div>
                        </div>
                    </div>
                    <div class="event-item">
                        <div class="event-icon" style="color: var(--success);">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="event-content">
                            <div class="event-title">Pipeline stage started</div>
                            <div class="event-meta">
                                <span>Pipeline: ask</span>
                                <span>Stage: ai_processing</span>
                            </div>
                            <div class="event-timestamp">15 seconds ago</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Simple JavaScript for interactivity
        document.addEventListener('DOMContentLoaded', function() {
            // Navigation active state
            const navLinks = document.querySelectorAll('.nav-links a');
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    navLinks.forEach(l => l.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // Simulate real-time events
            const eventsList = document.querySelector('.events-list');
            setInterval(() => {
                // In a real implementation, this would receive events via WebSocket
                // For demo purposes, we'll just add a new event occasionally
                if (Math.random() > 0.7) {
                    const events = [
                        {
                            icon: 'fa-info-circle',
                            color: 'var(--primary)',
                            title: 'Metric updated',
                            meta: ['Metric: cache_hit_rate', 'Value: 89%'],
                            time: 'Just now'
                        },
                        {
                            icon: 'fa-check-circle',
                            color: 'var(--success)',
                            title: 'Cache item accessed',
                            meta: ['Key: ask:query_price_TSLA', 'Size: 1.2KB'],
                            time: 'Just now'
                        }
                    ];
                    
                    const event = events[Math.floor(Math.random() * events.length)];
                    
                    const eventElement = document.createElement('div');
                    eventElement.className = 'event-item';
                    eventElement.innerHTML = `
                        <div class="event-icon" style="color: ${event.color};">
                            <i class="fas ${event.icon}"></i>
                        </div>
                        <div class="event-content">
                            <div class="event-title">${event.title}</div>
                            <div class="event-meta">
                                <span>${event.meta[0]}</span>
                                <span>${event.meta[1]}</span>
                            </div>
                            <div class="event-timestamp">${event.time}</div>
                        </div>
                    `;
                    
                    eventsList.insertBefore(eventElement, eventsList.firstChild);
                    
                    // Remove oldest event if we have too many
                    if (eventsList.children.length > 10) {
                        eventsList.removeChild(eventsList.lastChild);
                    }
                }
            }, 3000);

            // Simulate pipeline progress
            const stages = document.querySelectorAll('.stage');
            let currentStage = 2; // Start at AI Processing (0-indexed)
            
            setInterval(() => {
                // Reset all stages
                stages.forEach((stage, index) => {
                    stage.classList.remove('completed', 'active');
                    if (index < currentStage) {
                        stage.classList.add('completed');
                    } else if (index === currentStage) {
                        stage.classList.add('active');
                    }
                });
                
                // Move to next stage occasionally
                if (Math.random() > 0.8 && currentStage < stages.length - 1) {
                    currentStage++;
                }
                
                // Reset if we've completed all stages
                if (currentStage >= stages.length) {
                    currentStage = 2; // Reset to AI Processing
                }
            }, 2000);
        });
    </script>
</body>
</html>