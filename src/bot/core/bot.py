"""
Core TradingBot - Clean, minimal bot initialization.
"""

import os
import asyncio
import logging
from typing import Optional
from discord.ext import commands
from discord import Intents
import discord

from src.shared.error_handling.logging import get_logger
from .services import ServiceManager

logger = get_logger(__name__)

class TradingBot:
    """Clean, modular trading bot with proper separation of concerns."""
    
    def __init__(self, token: Optional[str] = None):
        """Initialize the bot with minimal required setup."""
        self.token = token
        
        # Configure intents
        intents = Intents.default()
        intents.message_content = True
        intents.guilds = True
        
        # Create bot instance
        self.bot = commands.Bot(command_prefix='!', intents=intents)
        
        # Initialize service manager
        self.services = ServiceManager()
        
        # Attach services to bot instance for extensions to access
        self.bot.services = self.services
        
        # Setup event handlers
        self._setup_events()
        
        logger.info("TradingBot instance created")
    
    def _setup_events(self):
        """Setup core bot event handlers."""
        
        @self.bot.event
        async def on_ready():
            """Called when bot is ready."""
            logger.info(f'🤖 Bot ready: {self.bot.user} (ID: {self.bot.user.id})')
            logger.info(f'🌐 Connected to {len(self.bot.guilds)} servers')
            
            # Initialize services
            await self.services.initialize()
            
            # Load command extensions
            await self._load_extensions()
            
            # Sync commands
            try:
                synced = await self.bot.tree.sync()
                logger.info(f"✅ Synced {len(synced)} slash commands with Discord")
                logger.info(f"📋 Available slash commands: {[cmd.name for cmd in synced]}")

                # Also log command details for debugging
                for cmd in synced:
                    logger.info(f"   - {cmd.name}: {cmd.description}")

            except Exception as e:
                logger.error(f"❌ Failed to sync commands: {e}")
            logger.info("✅ Bot initialization complete")
        
        @self.bot.event
        async def on_interaction(interaction):
            """Log interactions for auditing and pass them to the command tree."""
            if interaction.type == discord.InteractionType.application_command:
                command_name = interaction.command.name if interaction.command else "unknown"
                user = interaction.user
                guild = interaction.guild

                log_extra = {
                    "command": command_name,
                    "user_id": user.id,
                    "user_name": str(user),
                    "guild_id": guild.id if guild else "DM",
                    "guild_name": guild.name if guild else "DM"
                }

                logger.info(f"Received slash command: /{command_name}", extra=log_extra)

                # The command tree will process the command automatically.
                # This is a good place to hook in an audit service without monkey-patching.
                # For example: await self.services.get_service('audit_service').log_request(interaction)
        
        @self.bot.event
        async def on_command_error(ctx, error):
            """Handle command errors."""
            if isinstance(error, commands.CommandNotFound):
                return
            logger.error(f'Command error in {ctx.command}: {error}')
        
        @self.bot.tree.error
        async def on_app_command_error(interaction: discord.Interaction, error: discord.app_commands.AppCommandError):
            """Handle slash command errors."""
            command_name = interaction.command.name if interaction.command else "unknown"
            logger.error(f'Slash command error in {command_name}: {error}')
            if not interaction.response.is_done():
                await interaction.response.send_message(
                    "❌ An error occurred while processing your command. Please try again.",
                    ephemeral=True
                )
    
    async def _load_extensions(self):
        """Load all command extensions."""
        extensions = [
            'src.bot.extensions.ask',
            'src.bot.extensions.analyze',
            'src.bot.extensions.help',
            'src.bot.extensions.portfolio',
            'src.bot.extensions.watchlist',
            'src.bot.extensions.zones',
            'src.bot.extensions.recommendations',
            'src.bot.extensions.alerts',
            'src.bot.extensions.batch_analyze',
            'src.bot.extensions.status',
            'src.bot.extensions.utility',
            'src.bot.extensions.error_handler'
        ]
        
        for ext in extensions:
            try:
                await self.bot.load_extension(ext)
                logger.info(f"✅ Loaded extension: {ext}")
            except Exception as e:
                logger.error(f"❌ Failed to load {ext}: {e}")
    
    async def start_bot(self):
        """Start the bot."""
        if not self.token:
            logger.error("DISCORD_BOT_TOKEN not provided")
            raise ValueError("Bot token is required")
        
        try:
            await self.bot.start(self.token)
        except Exception as e:
            logger.error(f"Failed to start bot: {e}")
            raise
    
    async def close(self):
        """Clean shutdown of the bot."""
        if not self.bot.is_closed():
            await self.bot.close()
        await self.services.cleanup()

def create_bot() -> TradingBot:
    """Create a new bot instance."""
    token = os.getenv('DISCORD_BOT_TOKEN')
    return TradingBot(token=token)

async def run_bot():
    """Run the bot with proper error handling."""
    bot = create_bot()
    
    try:
        await bot.start_bot()
    except KeyboardInterrupt:
        logger.info("Bot shutdown requested")
    except Exception as e:
        logger.error(f"Bot failed: {e}")
        raise
    finally:
        await bot.close()

if __name__ == "__main__":
    asyncio.run(run_bot())
