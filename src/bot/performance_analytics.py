"""
Performance Analytics System
Provides comprehensive performance tracking and analysis
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import json

logger = logging.getLogger(__name__)

class PerformanceMetric(Enum):
    """Performance metrics"""
    TOTAL_RETURN = "total_return"
    ANNUALIZED_RETURN = "annualized_return"
    SHARPE_RATIO = "sharpe_ratio"
    SORTINO_RATIO = "sortino_ratio"
    MAX_DRAWDOWN = "max_drawdown"
    CALMAR_RATIO = "calmar_ratio"
    WIN_RATE = "win_rate"
    PROFIT_FACTOR = "profit_factor"
    VOLATILITY = "volatility"
    BETA = "beta"
    ALPHA = "alpha"
    INFORMATION_RATIO = "information_ratio"

@dataclass
class PerformanceReport:
    """Performance report structure"""
    strategy_name: str
    symbol: str
    start_date: datetime
    end_date: datetime
    total_return: float
    annualized_return: float
    sharpe_ratio: float
    sortino_ratio: float
    max_drawdown: float
    calmar_ratio: float
    win_rate: float
    profit_factor: float
    volatility: float
    beta: float
    alpha: float
    information_ratio: float
    total_trades: int
    winning_trades: int
    losing_trades: int
    average_win: float
    average_loss: float
    largest_win: float
    largest_loss: float
    metrics: Dict[str, float]

class PerformanceAnalytics:
    """Comprehensive performance analytics system"""
    
    def __init__(self):
        self.performance_data = {}
        self.benchmark_data = {}
        self.risk_free_rate = 0.02  # 2% risk-free rate
        self.performance_reports = {}
        
    async def initialize(self):
        """Initialize the performance analytics system"""
        logger.info("Initializing performance analytics system...")
        
        # Load historical performance data
        await self._load_historical_data()
        
        logger.info("Performance analytics system initialized")
    
    async def calculate_performance_metrics(self, trades: List[Dict[str, Any]], 
                                          benchmark_data: Optional[pd.DataFrame] = None) -> PerformanceReport:
        """Calculate comprehensive performance metrics"""
        try:
            if not trades:
                return self._create_empty_report()
            
            # Convert trades to DataFrame
            trades_df = pd.DataFrame(trades)
            
            # Calculate basic metrics
            total_return = self._calculate_total_return(trades_df)
            annualized_return = self._calculate_annualized_return(trades_df)
            sharpe_ratio = self._calculate_sharpe_ratio(trades_df)
            sortino_ratio = self._calculate_sortino_ratio(trades_df)
            max_drawdown = self._calculate_max_drawdown(trades_df)
            calmar_ratio = self._calculate_calmar_ratio(annualized_return, max_drawdown)
            win_rate = self._calculate_win_rate(trades_df)
            profit_factor = self._calculate_profit_factor(trades_df)
            volatility = self._calculate_volatility(trades_df)
            
            # Calculate benchmark-relative metrics
            beta = 1.0  # Default beta
            alpha = 0.0  # Default alpha
            information_ratio = 0.0  # Default information ratio
            
            if benchmark_data is not None:
                beta = self._calculate_beta(trades_df, benchmark_data)
                alpha = self._calculate_alpha(annualized_return, beta, benchmark_data)
                information_ratio = self._calculate_information_ratio(trades_df, benchmark_data)
            
            # Calculate trade statistics
            trade_stats = self._calculate_trade_statistics(trades_df)
            
            # Create performance report
            report = PerformanceReport(
                strategy_name=trades_df.iloc[0].get('strategy', 'unknown'),
                symbol=trades_df.iloc[0].get('symbol', 'unknown'),
                start_date=trades_df['entry_time'].min(),
                end_date=trades_df['exit_time'].max(),
                total_return=total_return,
                annualized_return=annualized_return,
                sharpe_ratio=sharpe_ratio,
                sortino_ratio=sortino_ratio,
                max_drawdown=max_drawdown,
                calmar_ratio=calmar_ratio,
                win_rate=win_rate,
                profit_factor=profit_factor,
                volatility=volatility,
                beta=beta,
                alpha=alpha,
                information_ratio=information_ratio,
                total_trades=trade_stats['total_trades'],
                winning_trades=trade_stats['winning_trades'],
                losing_trades=trade_stats['losing_trades'],
                average_win=trade_stats['average_win'],
                average_loss=trade_stats['average_loss'],
                largest_win=trade_stats['largest_win'],
                largest_loss=trade_stats['largest_loss'],
                metrics={
                    'total_return': total_return,
                    'annualized_return': annualized_return,
                    'sharpe_ratio': sharpe_ratio,
                    'sortino_ratio': sortino_ratio,
                    'max_drawdown': max_drawdown,
                    'calmar_ratio': calmar_ratio,
                    'win_rate': win_rate,
                    'profit_factor': profit_factor,
                    'volatility': volatility,
                    'beta': beta,
                    'alpha': alpha,
                    'information_ratio': information_ratio
                }
            )
            
            return report
            
        except Exception as e:
            logger.error(f"Error calculating performance metrics: {e}")
            return self._create_empty_report()
    
    async def compare_strategies(self, strategy_reports: List[PerformanceReport]) -> Dict[str, Any]:
        """Compare multiple strategy performance reports"""
        try:
            if not strategy_reports:
                return {}
            
            # Create comparison data
            comparison_data = {
                'strategies': [],
                'best_performers': {},
                'rankings': {},
                'summary': {}
            }
            
            # Extract metrics for comparison
            metrics = ['total_return', 'sharpe_ratio', 'max_drawdown', 'win_rate', 'profit_factor']
            
            for report in strategy_reports:
                strategy_data = {
                    'name': report.strategy_name,
                    'symbol': report.symbol,
                    'metrics': {metric: getattr(report, metric) for metric in metrics}
                }
                comparison_data['strategies'].append(strategy_data)
            
            # Find best performers for each metric
            for metric in metrics:
                best_strategy = max(strategy_reports, key=lambda x: getattr(x, metric))
                comparison_data['best_performers'][metric] = {
                    'strategy': best_strategy.strategy_name,
                    'value': getattr(best_strategy, metric)
                }
            
            # Create rankings
            for metric in metrics:
                sorted_strategies = sorted(strategy_reports, key=lambda x: getattr(x, metric), reverse=True)
                comparison_data['rankings'][metric] = [
                    {'strategy': s.strategy_name, 'value': getattr(s, metric)}
                    for s in sorted_strategies
                ]
            
            # Calculate summary statistics
            comparison_data['summary'] = {
                'total_strategies': len(strategy_reports),
                'average_return': np.mean([r.total_return for r in strategy_reports]),
                'average_sharpe': np.mean([r.sharpe_ratio for r in strategy_reports]),
                'average_drawdown': np.mean([r.max_drawdown for r in strategy_reports]),
                'best_return': max([r.total_return for r in strategy_reports]),
                'worst_return': min([r.total_return for r in strategy_reports])
            }
            
            return comparison_data
            
        except Exception as e:
            logger.error(f"Error comparing strategies: {e}")
            return {}
    
    async def generate_performance_report(self, strategy_name: str, symbol: str, 
                                        trades: List[Dict[str, Any]]) -> str:
        """Generate a formatted performance report"""
        try:
            # Calculate performance metrics
            report = await self.calculate_performance_metrics(trades)
            
            # Format report
            report_text = f"""
# Performance Report: {strategy_name} - {symbol}

## Overview
- **Period**: {report.start_date.strftime('%Y-%m-%d')} to {report.end_date.strftime('%Y-%m-%d')}
- **Total Trades**: {report.total_trades}
- **Winning Trades**: {report.winning_trades}
- **Losing Trades**: {report.losing_trades}

## Returns
- **Total Return**: {report.total_return:.2%}
- **Annualized Return**: {report.annualized_return:.2%}
- **Volatility**: {report.volatility:.2%}

## Risk Metrics
- **Sharpe Ratio**: {report.sharpe_ratio:.2f}
- **Sortino Ratio**: {report.sortino_ratio:.2f}
- **Max Drawdown**: {report.max_drawdown:.2%}
- **Calmar Ratio**: {report.calmar_ratio:.2f}

## Trade Statistics
- **Win Rate**: {report.win_rate:.2%}
- **Profit Factor**: {report.profit_factor:.2f}
- **Average Win**: {report.average_win:.2%}
- **Average Loss**: {report.average_loss:.2%}
- **Largest Win**: {report.largest_win:.2%}
- **Largest Loss**: {report.largest_loss:.2%}

## Risk-Adjusted Metrics
- **Beta**: {report.beta:.2f}
- **Alpha**: {report.alpha:.2%}
- **Information Ratio**: {report.information_ratio:.2f}
"""
            
            return report_text
            
        except Exception as e:
            logger.error(f"Error generating performance report: {e}")
            return f"Error generating performance report: {e}"
    
    def _calculate_total_return(self, trades_df: pd.DataFrame) -> float:
        """Calculate total return"""
        try:
            if 'pnl' in trades_df.columns:
                return trades_df['pnl'].sum()
            elif 'capital' in trades_df.columns:
                initial_capital = trades_df['capital'].iloc[0]
                final_capital = trades_df['capital'].iloc[-1]
                return (final_capital - initial_capital) / initial_capital
            else:
                return 0.0
        except Exception as e:
            logger.error(f"Error calculating total return: {e}")
            return 0.0
    
    def _calculate_annualized_return(self, trades_df: pd.DataFrame) -> float:
        """Calculate annualized return"""
        try:
            total_return = self._calculate_total_return(trades_df)
            
            if 'entry_time' in trades_df.columns and 'exit_time' in trades_df.columns:
                start_date = trades_df['entry_time'].min()
                end_date = trades_df['exit_time'].max()
                years = (end_date - start_date).days / 365.25
                
                if years > 0:
                    return (1 + total_return) ** (1 / years) - 1
            
            return total_return
            
        except Exception as e:
            logger.error(f"Error calculating annualized return: {e}")
            return 0.0
    
    def _calculate_sharpe_ratio(self, trades_df: pd.DataFrame) -> float:
        """Calculate Sharpe ratio"""
        try:
            if 'pnl' in trades_df.columns:
                returns = trades_df['pnl']
                if len(returns) < 2 or returns.std() == 0:
                    return 0.0
                
                excess_return = returns.mean() - self.risk_free_rate
                return excess_return / returns.std() * np.sqrt(252)
            
            return 0.0
            
        except Exception as e:
            logger.error(f"Error calculating Sharpe ratio: {e}")
            return 0.0
    
    def _calculate_sortino_ratio(self, trades_df: pd.DataFrame) -> float:
        """Calculate Sortino ratio"""
        try:
            if 'pnl' in trades_df.columns:
                returns = trades_df['pnl']
                if len(returns) < 2:
                    return 0.0
                
                excess_return = returns.mean() - self.risk_free_rate
                downside_returns = returns[returns < 0]
                
                if len(downside_returns) == 0:
                    return float('inf')
                
                downside_std = downside_returns.std()
                if downside_std == 0:
                    return 0.0
                
                return excess_return / downside_std * np.sqrt(252)
            
            return 0.0
            
        except Exception as e:
            logger.error(f"Error calculating Sortino ratio: {e}")
            return 0.0
    
    def _calculate_max_drawdown(self, trades_df: pd.DataFrame) -> float:
        """Calculate maximum drawdown"""
        try:
            if 'capital' in trades_df.columns:
                capital_series = trades_df['capital']
                peak = capital_series.expanding().max()
                drawdown = (peak - capital_series) / peak
                return drawdown.max()
            else:
                return 0.0
                
        except Exception as e:
            logger.error(f"Error calculating max drawdown: {e}")
            return 0.0
    
    def _calculate_calmar_ratio(self, annualized_return: float, max_drawdown: float) -> float:
        """Calculate Calmar ratio"""
        try:
            if max_drawdown == 0:
                return float('inf')
            return annualized_return / max_drawdown
        except Exception as e:
            logger.error(f"Error calculating Calmar ratio: {e}")
            return 0.0
    
    def _calculate_win_rate(self, trades_df: pd.DataFrame) -> float:
        """Calculate win rate"""
        try:
            if 'pnl' in trades_df.columns:
                winning_trades = (trades_df['pnl'] > 0).sum()
                total_trades = len(trades_df)
                return winning_trades / total_trades if total_trades > 0 else 0.0
            return 0.0
        except Exception as e:
            logger.error(f"Error calculating win rate: {e}")
            return 0.0
    
    def _calculate_profit_factor(self, trades_df: pd.DataFrame) -> float:
        """Calculate profit factor"""
        try:
            if 'pnl' in trades_df.columns:
                gross_profit = trades_df[trades_df['pnl'] > 0]['pnl'].sum()
                gross_loss = abs(trades_df[trades_df['pnl'] < 0]['pnl'].sum())
                return gross_profit / gross_loss if gross_loss > 0 else float('inf')
            return 0.0
        except Exception as e:
            logger.error(f"Error calculating profit factor: {e}")
            return 0.0
    
    def _calculate_volatility(self, trades_df: pd.DataFrame) -> float:
        """Calculate volatility"""
        try:
            if 'pnl' in trades_df.columns:
                returns = trades_df['pnl']
                return returns.std() * np.sqrt(252) if len(returns) > 1 else 0.0
            return 0.0
        except Exception as e:
            logger.error(f"Error calculating volatility: {e}")
            return 0.0
    
    def _calculate_beta(self, trades_df: pd.DataFrame, benchmark_data: pd.DataFrame) -> float:
        """Calculate beta relative to benchmark"""
        try:
            # Simplified beta calculation
            return 1.0  # Default beta
        except Exception as e:
            logger.error(f"Error calculating beta: {e}")
            return 1.0
    
    def _calculate_alpha(self, annualized_return: float, beta: float, benchmark_data: pd.DataFrame) -> float:
        """Calculate alpha relative to benchmark"""
        try:
            # Simplified alpha calculation
            return 0.0  # Default alpha
        except Exception as e:
            logger.error(f"Error calculating alpha: {e}")
            return 0.0
    
    def _calculate_information_ratio(self, trades_df: pd.DataFrame, benchmark_data: pd.DataFrame) -> float:
        """Calculate information ratio"""
        try:
            # Simplified information ratio calculation
            return 0.0  # Default information ratio
        except Exception as e:
            logger.error(f"Error calculating information ratio: {e}")
            return 0.0
    
    def _calculate_trade_statistics(self, trades_df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate trade statistics"""
        try:
            if 'pnl' not in trades_df.columns:
                return {
                    'total_trades': 0,
                    'winning_trades': 0,
                    'losing_trades': 0,
                    'average_win': 0.0,
                    'average_loss': 0.0,
                    'largest_win': 0.0,
                    'largest_loss': 0.0
                }
            
            pnl = trades_df['pnl']
            winning_trades = pnl[pnl > 0]
            losing_trades = pnl[pnl < 0]
            
            return {
                'total_trades': len(trades_df),
                'winning_trades': len(winning_trades),
                'losing_trades': len(losing_trades),
                'average_win': winning_trades.mean() if len(winning_trades) > 0 else 0.0,
                'average_loss': losing_trades.mean() if len(losing_trades) > 0 else 0.0,
                'largest_win': winning_trades.max() if len(winning_trades) > 0 else 0.0,
                'largest_loss': losing_trades.min() if len(losing_trades) > 0 else 0.0
            }
            
        except Exception as e:
            logger.error(f"Error calculating trade statistics: {e}")
            return {
                'total_trades': 0,
                'winning_trades': 0,
                'losing_trades': 0,
                'average_win': 0.0,
                'average_loss': 0.0,
                'largest_win': 0.0,
                'largest_loss': 0.0
            }
    
    def _create_empty_report(self) -> PerformanceReport:
        """Create an empty performance report"""
        return PerformanceReport(
            strategy_name='unknown',
            symbol='unknown',
            start_date=datetime.now(),
            end_date=datetime.now(),
            total_return=0.0,
            annualized_return=0.0,
            sharpe_ratio=0.0,
            sortino_ratio=0.0,
            max_drawdown=0.0,
            calmar_ratio=0.0,
            win_rate=0.0,
            profit_factor=0.0,
            volatility=0.0,
            beta=1.0,
            alpha=0.0,
            information_ratio=0.0,
            total_trades=0,
            winning_trades=0,
            losing_trades=0,
            average_win=0.0,
            average_loss=0.0,
            largest_win=0.0,
            largest_loss=0.0,
            metrics={}
        )
    
    async def _load_historical_data(self):
        """Load historical performance data"""
        try:
            # In practice, this would load from a database
            pass
        except Exception as e:
            logger.error(f"Error loading historical data: {e}")
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            self.performance_data.clear()
            self.benchmark_data.clear()
            self.performance_reports.clear()
            logger.info("Performance analytics system cleaned up")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

# Global instance
performance_analytics = PerformanceAnalytics()
