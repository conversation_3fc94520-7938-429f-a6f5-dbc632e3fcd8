"""
Market Sentiment Analysis System
Provides comprehensive sentiment analysis from multiple sources
"""

import asyncio
import logging
import aiohttp
import json
import re
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import numpy as np
from collections import defaultdict

logger = logging.getLogger(__name__)

class SentimentSource(Enum):
    """Sources of sentiment data"""
    NEWS = "news"
    SOCIAL_MEDIA = "social_media"
    REDDIT = "reddit"
    TWITTER = "twitter"
    FINANCIAL_NEWS = "financial_news"
    ANALYST_RATINGS = "analyst_ratings"
    OPTIONS_FLOW = "options_flow"
    INSIDER_TRADING = "insider_trading"

class SentimentType(Enum):
    """Types of sentiment"""
    BULLISH = "bullish"
    BEARISH = "bearish"
    NEUTRAL = "neutral"
    VERY_BULLISH = "very_bullish"
    VERY_BEARISH = "very_bearish"

@dataclass
class SentimentData:
    """Sentiment data structure"""
    symbol: str
    sentiment_type: SentimentType
    confidence: float
    source: SentimentSource
    timestamp: datetime
    text: str
    metadata: Dict[str, Any] = None

@dataclass
class SentimentScore:
    """Aggregated sentiment score"""
    symbol: str
    overall_sentiment: SentimentType
    confidence: float
    bullish_percentage: float
    bearish_percentage: float
    neutral_percentage: float
    source_breakdown: Dict[str, float]
    timestamp: datetime
    trend_direction: str  # "improving", "declining", "stable"

class MarketSentimentAnalyzer:
    """Comprehensive market sentiment analysis system"""
    
    def __init__(self):
        self.sentiment_data = defaultdict(list)
        self.sentiment_scores = {}
        self.news_sources = [
            'reuters', 'bloomberg', 'cnbc', 'wsj', 'ft', 'yahoo_finance',
            'marketwatch', 'investing', 'seeking_alpha', 'benzinga'
        ]
        self.social_media_sources = [
            'reddit', 'twitter', 'stocktwits', 'discord', 'telegram'
        ]
        self.sentiment_keywords = {
            'bullish': [
                'bullish', 'buy', 'strong', 'growth', 'positive', 'upward',
                'momentum', 'breakout', 'rally', 'surge', 'gain', 'profit',
                'outperform', 'upgrade', 'target', 'recommendation'
            ],
            'bearish': [
                'bearish', 'sell', 'weak', 'decline', 'negative', 'downward',
                'crash', 'drop', 'fall', 'loss', 'underperform', 'downgrade',
                'warning', 'risk', 'concern', 'trouble'
            ],
            'neutral': [
                'neutral', 'hold', 'stable', 'sideways', 'consolidation',
                'wait', 'monitor', 'observe', 'maintain', 'unchanged'
            ]
        }
        self.fear_greed_indicators = {}
        self.market_sentiment_cache = {}
        
    async def initialize(self):
        """Initialize the sentiment analysis system"""
        logger.info("Initializing market sentiment analysis system...")
        
        # Initialize sentiment analysis models
        await self._initialize_sentiment_models()
        
        # Start background sentiment collection
        asyncio.create_task(self._background_sentiment_collection())
        
        logger.info("Market sentiment analysis system initialized")
    
    async def _initialize_sentiment_models(self):
        """Initialize sentiment analysis models"""
        # In practice, this would load pre-trained models
        # For now, we'll use rule-based analysis
        pass
    
    async def analyze_sentiment(self, symbol: str, text: str, source: SentimentSource) -> SentimentData:
        """Analyze sentiment of text for a specific symbol"""
        try:
            # Clean and preprocess text
            cleaned_text = self._clean_text(text)
            
            # Analyze sentiment
            sentiment_type, confidence = await self._analyze_text_sentiment(cleaned_text)
            
            # Create sentiment data
            sentiment_data = SentimentData(
                symbol=symbol.upper(),
                sentiment_type=sentiment_type,
                confidence=confidence,
                source=source,
                timestamp=datetime.now(),
                text=cleaned_text,
                metadata={
                    'original_text': text,
                    'word_count': len(cleaned_text.split()),
                    'symbol_mentions': cleaned_text.lower().count(symbol.lower())
                }
            )
            
            # Store sentiment data
            self.sentiment_data[symbol].append(sentiment_data)
            
            # Update aggregated sentiment score
            await self._update_sentiment_score(symbol)
            
            return sentiment_data
            
        except Exception as e:
            logger.error(f"Error analyzing sentiment for {symbol}: {e}")
            return None
    
    async def get_sentiment_score(self, symbol: str) -> Optional[SentimentScore]:
        """Get aggregated sentiment score for a symbol"""
        try:
            if symbol not in self.sentiment_scores:
                return None
            
            return self.sentiment_scores[symbol]
            
        except Exception as e:
            logger.error(f"Error getting sentiment score for {symbol}: {e}")
            return None
    
    async def get_market_sentiment_overview(self) -> Dict[str, Any]:
        """Get overall market sentiment overview"""
        try:
            if not self.sentiment_scores:
                return {
                    'overall_sentiment': 'neutral',
                    'market_fear_greed': 50,
                    'top_bullish_symbols': [],
                    'top_bearish_symbols': [],
                    'sentiment_trend': 'stable'
                }
            
            # Calculate overall market sentiment
            all_scores = list(self.sentiment_scores.values())
            bullish_count = sum(1 for score in all_scores if score.overall_sentiment in [SentimentType.BULLISH, SentimentType.VERY_BULLISH])
            bearish_count = sum(1 for score in all_scores if score.overall_sentiment in [SentimentType.BEARISH, SentimentType.VERY_BEARISH])
            neutral_count = sum(1 for score in all_scores if score.overall_sentiment == SentimentType.NEUTRAL)
            
            total_count = len(all_scores)
            bullish_percentage = (bullish_count / total_count) * 100 if total_count > 0 else 0
            bearish_percentage = (bearish_count / total_count) * 100 if total_count > 0 else 0
            
            # Determine overall sentiment
            if bullish_percentage > 60:
                overall_sentiment = 'bullish'
            elif bearish_percentage > 60:
                overall_sentiment = 'bearish'
            else:
                overall_sentiment = 'neutral'
            
            # Calculate fear/greed index
            fear_greed = await self._calculate_fear_greed_index()
            
            # Get top symbols by sentiment
            top_bullish = sorted(all_scores, key=lambda x: x.bullish_percentage, reverse=True)[:5]
            top_bearish = sorted(all_scores, key=lambda x: x.bearish_percentage, reverse=True)[:5]
            
            return {
                'overall_sentiment': overall_sentiment,
                'market_fear_greed': fear_greed,
                'bullish_percentage': bullish_percentage,
                'bearish_percentage': bearish_percentage,
                'neutral_percentage': 100 - bullish_percentage - bearish_percentage,
                'top_bullish_symbols': [{'symbol': s.symbol, 'sentiment': s.overall_sentiment.value, 'confidence': s.confidence} for s in top_bullish],
                'top_bearish_symbols': [{'symbol': s.symbol, 'sentiment': s.overall_sentiment.value, 'confidence': s.confidence} for s in top_bearish],
                'sentiment_trend': await self._calculate_sentiment_trend(),
                'total_symbols_analyzed': total_count
            }
            
        except Exception as e:
            logger.error(f"Error getting market sentiment overview: {e}")
            return {}
    
    async def analyze_news_sentiment(self, symbol: str, news_articles: List[Dict[str, Any]]) -> List[SentimentData]:
        """Analyze sentiment from news articles"""
        sentiment_results = []
        
        try:
            for article in news_articles:
                title = article.get('title', '')
                content = article.get('content', '')
                source = article.get('source', 'unknown')
                
                # Combine title and content
                full_text = f"{title} {content}"
                
                # Analyze sentiment
                sentiment_data = await self.analyze_sentiment(
                    symbol, full_text, SentimentSource.NEWS
                )
                
                if sentiment_data:
                    sentiment_data.metadata['article_source'] = source
                    sentiment_data.metadata['article_url'] = article.get('url', '')
                    sentiment_results.append(sentiment_data)
            
            logger.info(f"Analyzed {len(sentiment_results)} news articles for {symbol}")
            return sentiment_results
            
        except Exception as e:
            logger.error(f"Error analyzing news sentiment for {symbol}: {e}")
            return []
    
    async def analyze_social_media_sentiment(self, symbol: str, posts: List[Dict[str, Any]]) -> List[SentimentData]:
        """Analyze sentiment from social media posts"""
        sentiment_results = []
        
        try:
            for post in posts:
                text = post.get('text', '')
                platform = post.get('platform', 'unknown')
                
                # Map platform to sentiment source
                source_mapping = {
                    'reddit': SentimentSource.REDDIT,
                    'twitter': SentimentSource.TWITTER,
                    'stocktwits': SentimentSource.SOCIAL_MEDIA,
                    'discord': SentimentSource.SOCIAL_MEDIA,
                    'telegram': SentimentSource.SOCIAL_MEDIA
                }
                
                source = source_mapping.get(platform, SentimentSource.SOCIAL_MEDIA)
                
                # Analyze sentiment
                sentiment_data = await self.analyze_sentiment(symbol, text, source)
                
                if sentiment_data:
                    sentiment_data.metadata['platform'] = platform
                    sentiment_data.metadata['post_id'] = post.get('id', '')
                    sentiment_data.metadata['author'] = post.get('author', '')
                    sentiment_results.append(sentiment_data)
            
            logger.info(f"Analyzed {len(sentiment_results)} social media posts for {symbol}")
            return sentiment_results
            
        except Exception as e:
            logger.error(f"Error analyzing social media sentiment for {symbol}: {e}")
            return []
    
    async def get_fear_greed_index(self) -> Dict[str, Any]:
        """Get current fear/greed index"""
        try:
            # Calculate fear/greed index from multiple indicators
            indicators = await self._calculate_fear_greed_indicators()
            
            # Weighted average of indicators
            weights = {
                'vix': 0.25,
                'put_call_ratio': 0.20,
                'sentiment_surveys': 0.15,
                'news_sentiment': 0.15,
                'social_media_sentiment': 0.10,
                'insider_trading': 0.10,
                'options_flow': 0.05
            }
            
            fear_greed_score = 0
            for indicator, value in indicators.items():
                if indicator in weights:
                    fear_greed_score += value * weights[indicator]
            
            # Normalize to 0-100 scale
            fear_greed_score = max(0, min(100, fear_greed_score))
            
            # Determine sentiment level
            if fear_greed_score >= 80:
                sentiment_level = 'Extreme Greed'
            elif fear_greed_score >= 60:
                sentiment_level = 'Greed'
            elif fear_greed_score >= 40:
                sentiment_level = 'Neutral'
            elif fear_greed_score >= 20:
                sentiment_level = 'Fear'
            else:
                sentiment_level = 'Extreme Fear'
            
            return {
                'fear_greed_index': fear_greed_score,
                'sentiment_level': sentiment_level,
                'indicators': indicators,
                'timestamp': datetime.now(),
                'trend': await self._calculate_fear_greed_trend()
            }
            
        except Exception as e:
            logger.error(f"Error calculating fear/greed index: {e}")
            return {}
    
    async def _analyze_text_sentiment(self, text: str) -> Tuple[SentimentType, float]:
        """Analyze sentiment of text using rule-based approach"""
        try:
            text_lower = text.lower()
            
            # Count sentiment keywords
            bullish_count = sum(1 for word in self.sentiment_keywords['bullish'] if word in text_lower)
            bearish_count = sum(1 for word in self.sentiment_keywords['bearish'] if word in text_lower)
            neutral_count = sum(1 for word in self.sentiment_keywords['neutral'] if word in text_lower)
            
            total_sentiment_words = bullish_count + bearish_count + neutral_count
            
            if total_sentiment_words == 0:
                return SentimentType.NEUTRAL, 0.5
            
            # Calculate sentiment scores
            bullish_score = bullish_count / total_sentiment_words
            bearish_score = bearish_count / total_sentiment_words
            neutral_score = neutral_count / total_sentiment_words
            
            # Determine sentiment type and confidence
            if bullish_score > bearish_score and bullish_score > neutral_score:
                if bullish_score > 0.7:
                    return SentimentType.VERY_BULLISH, bullish_score
                else:
                    return SentimentType.BULLISH, bullish_score
            elif bearish_score > bullish_score and bearish_score > neutral_score:
                if bearish_score > 0.7:
                    return SentimentType.VERY_BEARISH, bearish_score
                else:
                    return SentimentType.BEARISH, bearish_score
            else:
                return SentimentType.NEUTRAL, neutral_score
                
        except Exception as e:
            logger.error(f"Error analyzing text sentiment: {e}")
            return SentimentType.NEUTRAL, 0.5
    
    async def _update_sentiment_score(self, symbol: str):
        """Update aggregated sentiment score for a symbol"""
        try:
            if symbol not in self.sentiment_data:
                return
            
            symbol_data = self.sentiment_data[symbol]
            
            if not symbol_data:
                return
            
            # Calculate sentiment percentages
            total_count = len(symbol_data)
            bullish_count = sum(1 for data in symbol_data if data.sentiment_type in [SentimentType.BULLISH, SentimentType.VERY_BULLISH])
            bearish_count = sum(1 for data in symbol_data if data.sentiment_type in [SentimentType.BEARISH, SentimentType.VERY_BEARISH])
            neutral_count = sum(1 for data in symbol_data if data.sentiment_type == SentimentType.NEUTRAL)
            
            bullish_percentage = (bullish_count / total_count) * 100 if total_count > 0 else 0
            bearish_percentage = (bearish_count / total_count) * 100 if total_count > 0 else 0
            neutral_percentage = (neutral_count / total_count) * 100 if total_count > 0 else 0
            
            # Determine overall sentiment
            if bullish_percentage > 60:
                overall_sentiment = SentimentType.BULLISH
            elif bearish_percentage > 60:
                overall_sentiment = SentimentType.BEARISH
            else:
                overall_sentiment = SentimentType.NEUTRAL
            
            # Calculate confidence
            confidence = max(bullish_percentage, bearish_percentage, neutral_percentage) / 100
            
            # Calculate source breakdown
            source_breakdown = {}
            for data in symbol_data:
                source = data.source.value
                if source not in source_breakdown:
                    source_breakdown[source] = {'bullish': 0, 'bearish': 0, 'neutral': 0, 'total': 0}
                
                source_breakdown[source]['total'] += 1
                if data.sentiment_type in [SentimentType.BULLISH, SentimentType.VERY_BULLISH]:
                    source_breakdown[source]['bullish'] += 1
                elif data.sentiment_type in [SentimentType.BEARISH, SentimentType.VERY_BEARISH]:
                    source_breakdown[source]['bearish'] += 1
                else:
                    source_breakdown[source]['neutral'] += 1
            
            # Calculate trend direction
            trend_direction = await self._calculate_sentiment_trend_for_symbol(symbol)
            
            # Create sentiment score
            sentiment_score = SentimentScore(
                symbol=symbol,
                overall_sentiment=overall_sentiment,
                confidence=confidence,
                bullish_percentage=bullish_percentage,
                bearish_percentage=bearish_percentage,
                neutral_percentage=neutral_percentage,
                source_breakdown=source_breakdown,
                timestamp=datetime.now(),
                trend_direction=trend_direction
            )
            
            self.sentiment_scores[symbol] = sentiment_score
            
        except Exception as e:
            logger.error(f"Error updating sentiment score for {symbol}: {e}")
    
    async def _calculate_fear_greed_indicators(self) -> Dict[str, float]:
        """Calculate fear/greed indicators"""
        try:
            indicators = {}
            
            # VIX (Volatility Index) - simplified
            indicators['vix'] = 50  # Default neutral
            
            # Put/Call Ratio - simplified
            indicators['put_call_ratio'] = 50  # Default neutral
            
            # Sentiment Surveys - simplified
            indicators['sentiment_surveys'] = 50  # Default neutral
            
            # News Sentiment
            if self.sentiment_scores:
                news_sentiment = await self._calculate_news_sentiment_score()
                indicators['news_sentiment'] = news_sentiment
            else:
                indicators['news_sentiment'] = 50
            
            # Social Media Sentiment
            if self.sentiment_scores:
                social_sentiment = await self._calculate_social_sentiment_score()
                indicators['social_media_sentiment'] = social_sentiment
            else:
                indicators['social_media_sentiment'] = 50
            
            # Insider Trading - simplified
            indicators['insider_trading'] = 50  # Default neutral
            
            # Options Flow - simplified
            indicators['options_flow'] = 50  # Default neutral
            
            return indicators
            
        except Exception as e:
            logger.error(f"Error calculating fear/greed indicators: {e}")
            return {}
    
    async def _calculate_news_sentiment_score(self) -> float:
        """Calculate news sentiment score"""
        try:
            if not self.sentiment_scores:
                return 50
            
            news_scores = []
            for score in self.sentiment_scores.values():
                if 'news' in score.source_breakdown:
                    news_data = score.source_breakdown['news']
                    if news_data['total'] > 0:
                        bullish_pct = (news_data['bullish'] / news_data['total']) * 100
                        news_scores.append(bullish_pct)
            
            if not news_scores:
                return 50
            
            return np.mean(news_scores)
            
        except Exception as e:
            logger.error(f"Error calculating news sentiment score: {e}")
            return 50
    
    async def _calculate_social_sentiment_score(self) -> float:
        """Calculate social media sentiment score"""
        try:
            if not self.sentiment_scores:
                return 50
            
            social_scores = []
            for score in self.sentiment_scores.values():
                for source in ['reddit', 'twitter', 'social_media']:
                    if source in score.source_breakdown:
                        social_data = score.source_breakdown[source]
                        if social_data['total'] > 0:
                            bullish_pct = (social_data['bullish'] / social_data['total']) * 100
                            social_scores.append(bullish_pct)
            
            if not social_scores:
                return 50
            
            return np.mean(social_scores)
            
        except Exception as e:
            logger.error(f"Error calculating social sentiment score: {e}")
            return 50
    
    async def _calculate_fear_greed_index(self) -> float:
        """Calculate overall fear/greed index"""
        try:
            indicators = await self._calculate_fear_greed_indicators()
            return np.mean(list(indicators.values()))
        except Exception as e:
            logger.error(f"Error calculating fear/greed index: {e}")
            return 50
    
    async def _calculate_sentiment_trend(self) -> str:
        """Calculate overall sentiment trend"""
        try:
            # Simplified trend calculation
            return 'stable'  # Default stable trend
        except Exception as e:
            logger.error(f"Error calculating sentiment trend: {e}")
            return 'stable'
    
    async def _calculate_sentiment_trend_for_symbol(self, symbol: str) -> str:
        """Calculate sentiment trend for a specific symbol"""
        try:
            if symbol not in self.sentiment_data:
                return 'stable'
            
            symbol_data = self.sentiment_data[symbol]
            if len(symbol_data) < 2:
                return 'stable'
            
            # Get recent sentiment data
            recent_data = symbol_data[-10:]  # Last 10 sentiment data points
            
            # Calculate trend
            bullish_count = sum(1 for data in recent_data if data.sentiment_type in [SentimentType.BULLISH, SentimentType.VERY_BULLISH])
            bearish_count = sum(1 for data in recent_data if data.sentiment_type in [SentimentType.BEARISH, SentimentType.VERY_BEARISH])
            
            if bullish_count > bearish_count * 1.5:
                return 'improving'
            elif bearish_count > bullish_count * 1.5:
                return 'declining'
            else:
                return 'stable'
                
        except Exception as e:
            logger.error(f"Error calculating sentiment trend for {symbol}: {e}")
            return 'stable'
    
    async def _background_sentiment_collection(self):
        """Background task to collect sentiment data"""
        while True:
            try:
                # In practice, this would collect sentiment data from various sources
                # For now, we'll just sleep
                await asyncio.sleep(300)  # 5 minutes
                
            except Exception as e:
                logger.error(f"Error in background sentiment collection: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retrying
    
    def _clean_text(self, text: str) -> str:
        """Clean and preprocess text for sentiment analysis"""
        try:
            # Remove URLs
            text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
            
            # Remove special characters but keep spaces
            text = re.sub(r'[^\w\s]', ' ', text)
            
            # Remove extra whitespace
            text = ' '.join(text.split())
            
            return text.lower()
            
        except Exception as e:
            logger.error(f"Error cleaning text: {e}")
            return text
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            self.sentiment_data.clear()
            self.sentiment_scores.clear()
            self.fear_greed_indicators.clear()
            self.market_sentiment_cache.clear()
            logger.info("Market sentiment analysis system cleaned up")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

# Global instance
sentiment_analyzer = MarketSentimentAnalyzer()
