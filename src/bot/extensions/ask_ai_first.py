"""
Truly AI-First Ask Command
Just send everything to the AI and let it decide how to respond.
"""

import discord
from discord.ext import commands
from discord import app_commands
from typing import Optional, Dict, Any
import asyncio
import time
import logging
from datetime import datetime

from src.shared.error_handling.logging import get_logger
from src.shared.utils.discord_helpers import DiscordMessageHelper
from src.bot.utils.input_sanitizer import InputSanitizer
from src.bot.utils.disclaimer_manager import add_disclaimer
from src.shared.error_handling.logging import generate_correlation_id

# Import MCP tools for when AI needs them
from src.bot.pipeline.commands.ask.dynamic_mcp_pipeline import DynamicMCPAskCommand

logger = get_logger(__name__)

class TrulyAIFirstAskCommand(commands.Cog):
    """Truly AI-first ask command - AI handles everything naturally."""
    
    def __init__(self, bot: commands.Bot):
        self.bot = bot
        self.user_locks = {}
        self.user_locks_lock = asyncio.Lock()
        
        # Initialize AI client
        self.ai_client = None
        self._init_ai_client()
        
        # Initialize MCP pipeline for when AI needs deep research
        self.mcp_pipeline = DynamicMCPAskCommand()
        
        # Performance tracking
        self.request_count = 0
        self.ai_responses = 0
        self.mcp_responses = 0
        
        logger.info("🚀 Truly AI-First Ask Command initialized")
    
    def _init_ai_client(self):
        """Initialize AI client."""
        try:
            from src.shared.ai_chat.ai_client import AIClientWrapper
            self.ai_client = AIClientWrapper(
                pipeline_id="ai_first_ask",
                model="gpt-4o-mini"
            )
            logger.info("✅ AI client initialized")
        except Exception as e:
            logger.warning(f"⚠️ AI client failed to initialize: {e}")
            self.ai_client = None
    
    async def _get_user_lock(self, user_id: int) -> asyncio.Lock():
        """Get or create a lock for a user to prevent concurrent requests."""
        async with self.user_locks_lock:
            if user_id not in self.user_locks:
                self.user_locks[user_id] = asyncio.Lock()
            return self.user_locks[user_id]
    
    async def _ai_responds(self, query: str, user_id: str) -> str:
        """Let AI respond naturally to any query."""
        try:
            if not self.ai_client:
                return "Hey! I'm your trading assistant. I can help with stock analysis, market data, and trading insights. What would you like to know?"
            
            # Simple, natural prompt - let AI decide what to do
            prompt = f"""You are a helpful trading assistant. The user said: "{query}"

Respond naturally and helpfully. You can:
- Chat casually if they're greeting you
- Help with trading questions if they ask
- Explain your capabilities if they ask
- Be friendly and conversational

If they ask about specific stocks, prices, or market data, you can mention that you'd need to look up real-time data, but don't make up specific numbers."""

            response = await self.ai_client.generate_response(prompt)
            
            if response:
                self.ai_responses += 1
                return response
            else:
                return "Hey there! I'm your trading assistant. What would you like to know about the markets?"
        
        except Exception as e:
            logger.error(f"❌ AI response failed: {e}")
            return "Hey! I'm here to help with trading questions. What would you like to know about the markets?"
    
    async def _ai_needs_data(self, query: str, user_id: str) -> Dict[str, Any]:
        """AI determines it needs real data - use MCP pipeline."""
        try:
            logger.info(f"🔬 AI needs real data for: {query[:50]}...")
            
            # Use MCP pipeline for real data
            result = await self.mcp_pipeline.process_query(query, user_id)
            
            self.mcp_responses += 1
            return result
        
        except Exception as e:
            logger.error(f"❌ MCP pipeline failed: {e}")
            return {
                "success": False,
                "response": f"Sorry, I encountered an error while getting real-time data: {str(e)}",
                "error": str(e)
            }
    
    async def _process_ask_request(self, interaction: discord.Interaction, query: str) -> Dict[str, Any]:
        """Process the ask request - AI decides everything."""
        start_time = time.time()
        
        try:
            correlation_id = generate_correlation_id()
            user_id = interaction.user.id
            
            logger.info(f"🤖 [{correlation_id}] AI processing: {query[:100]}...")
            
            # Get user lock
            user_lock = await self._get_user_lock(user_id)
            
            async with user_lock:
                # First, let AI respond naturally
                ai_response = await self._ai_responds(query, str(user_id))
                
                # Check if AI's response suggests it needs real data
                needs_real_data = self._ai_needs_real_data(ai_response, query)
                
                if needs_real_data:
                    logger.info(f"🔬 [{correlation_id}] AI needs real data - using MCP")
                    
                    # Get real data using MCP
                    mcp_result = await self._ai_needs_data(query, str(user_id))
                    
                    if mcp_result.get('success', False):
                        # Combine AI response with real data
                        combined_response = f"{ai_response}\n\n{self._format_mcp_data(mcp_result)}"
                        
                        return {
                            "success": True,
                            "response": combined_response,
                            "query_type": "trading_with_data",
                            "processing_time": time.time() - start_time,
                            "ai_used": "ai_plus_mcp",
                            "mcp_data": mcp_result
                        }
                    else:
                        # Fallback to AI response if MCP fails
                        return {
                            "success": True,
                            "response": f"{ai_response}\n\n*Note: I couldn't access real-time data right now, but I can still help with general trading questions.*",
                            "query_type": "trading_fallback",
                            "processing_time": time.time() - start_time,
                            "ai_used": "ai_only"
                        }
                else:
                    # AI response is sufficient
                    return {
                        "success": True,
                        "response": ai_response,
                        "query_type": "casual_or_general",
                        "processing_time": time.time() - start_time,
                        "ai_used": "ai_only"
                    }
        
        except Exception as e:
            logger.error(f"❌ [{correlation_id}] Ask request failed: {e}")
            return {
                "success": False,
                "response": f"Sorry, I encountered an error: {str(e)}",
                "query_type": "error",
                "processing_time": time.time() - start_time,
                "error": str(e)
            }
    
    def _ai_needs_real_data(self, ai_response: str, original_query: str) -> bool:
        """Check if AI's response suggests it needs real data."""
        # Look for indicators that AI wants real data
        data_indicators = [
            "i'd need to look up", "i'd need to check", "i'd need to get",
            "let me check", "let me look up", "let me get",
            "i can look up", "i can check", "i can get",
            "real-time data", "current data", "live data",
            "specific price", "current price", "latest price",
            "i don't have access", "i can't access", "i need to access"
        ]
        
        response_lower = ai_response.lower()
        query_lower = original_query.lower()
        
        # Check if AI response suggests it needs data
        for indicator in data_indicators:
            if indicator in response_lower:
                return True
        
        # Check if original query asks for specific data
        data_keywords = [
            "price", "current", "latest", "real-time", "live",
            "rsi", "macd", "bollinger", "technical", "analysis",
            "sentiment", "news", "earnings", "revenue", "profit"
        ]
        
        for keyword in data_keywords:
            if keyword in query_lower:
                return True
        
        return False
    
    def _format_mcp_data(self, mcp_result: Dict[str, Any]) -> str:
        """Format MCP data for display."""
        if not mcp_result.get('success', False):
            return "*Real-time data unavailable*"
        
        # Extract key information from MCP result
        response = mcp_result.get('response', '')
        tool_calls = mcp_result.get('total_tool_calls', 0)
        mcp_used = mcp_result.get('mcp_tools_used', False)
        
        # Add data source attribution
        if mcp_used and tool_calls > 0:
            return f"*Real-time data from {tool_calls} sources*"
        else:
            return "*Real-time data included*"
    
    def _format_response(self, result: Dict[str, Any], query: str) -> str:
        """Format the final response."""
        response = result.get('response', 'No response generated')
        
        # Add processing time if it took a while
        processing_time = result.get('processing_time', 0)
        if processing_time > 3.0:
            response += f"\n⏱️ *Processed in {processing_time:.1f}s*"
        
        return response
    
    @app_commands.command(
        name="ask",
        description="Ask me anything - I'll respond naturally and get real data when needed"
    )
    @app_commands.describe(
        query="Your question - I'll respond naturally and get real data if needed",
        voice="Optional voice message attachment"
    )
    async def ask_command(
        self,
        interaction: discord.Interaction,
        query: str,
        voice: Optional[discord.Attachment] = None
    ):
        """Handle the /ask slash command with truly AI-first approach."""
        await interaction.response.defer(ephemeral=False)
        
        try:
            # Handle voice attachment
            if voice:
                if not voice.content_type or not voice.content_type.startswith('audio/'):
                    await interaction.followup.send(
                        "❌ Please attach a valid audio file.",
                        ephemeral=True
                    )
                    return
                
                query = f"[Voice message attached] {query}"
            
            # Sanitize query
            sanitized_query, is_valid, error_message = await InputSanitizer.sanitize_query(query)
            
            if not is_valid:
                await interaction.followup.send(
                    f"❌ {error_message}",
                    ephemeral=True
                )
                return
            
            # Process request
            result = await self._process_ask_request(interaction, sanitized_query)
            
            if not result.get('success', False):
                await interaction.followup.send(
                    f"❌ {result.get('response', 'Unknown error occurred')}",
                    ephemeral=True
                )
                return
            
            # Format and send response
            formatted_response = self._format_response(result, sanitized_query)
            
            # Add disclaimer for trading queries
            if result.get('query_type', '').startswith('trading'):
                final_response = add_disclaimer(formatted_response)
            else:
                final_response = formatted_response
            
            # Send response
            await interaction.followup.send(final_response)
            
            # Update stats
            self.request_count += 1
            
            # Log success
            logger.info(f"✅ AI-first ask completed for {interaction.user.name} ({result.get('ai_used', 'unknown')})")
            
        except Exception as e:
            logger.error(f"❌ AI-first ask command failed: {e}")
            await interaction.followup.send(
                "❌ Sorry, I encountered an error. Please try again.",
                ephemeral=True
            )
    
    @app_commands.command(
        name="ask_stats",
        description="View ask command statistics"
    )
    async def ask_stats_command(self, interaction: discord.Interaction):
        """Show ask command statistics."""
        await interaction.response.defer(ephemeral=True)
        
        try:
            ai_rate = (self.ai_responses / self.request_count * 100) if self.request_count > 0 else 0
            mcp_rate = (self.mcp_responses / self.request_count * 100) if self.request_count > 0 else 0
            
            stats_message = f"""
📊 **Truly AI-First Ask Command Statistics**

**Performance:**
• Total Requests: {self.request_count}
• AI Responses: {self.ai_responses} ({ai_rate:.1f}%)
• MCP Responses: {self.mcp_responses} ({mcp_rate:.1f}%)

**How it works:**
• AI responds naturally to every query
• AI decides if it needs real-time data
• MCP tools used only when AI determines it's needed
• No complex decision logic - just AI being AI

**Benefits:**
• Natural conversation flow
• AI decides what data it needs
• No over-engineering
• Fast and reliable
            """
            
            await interaction.followup.send(stats_message)
            
        except Exception as e:
            logger.error(f"❌ Stats command failed: {e}")
            await interaction.followup.send(
                "❌ Failed to retrieve statistics.",
                ephemeral=True
            )
    
    async def cog_unload(self):
        """Cleanup when cog is unloaded."""
        try:
            if hasattr(self, 'mcp_pipeline'):
                await self.mcp_pipeline.close()
            logger.info("🧹 Truly AI-First Ask Command cleaned up")
        except Exception as e:
            logger.error(f"❌ Cleanup failed: {e}")

async def setup(bot: commands.Bot):
    """Setup function for the cog."""
    await bot.add_cog(TrulyAIFirstAskCommand(bot))
    logger.info("🚀 Truly AI-First Ask Command cog loaded")
