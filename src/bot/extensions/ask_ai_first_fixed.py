"""
Fixed AI-First Ask Command
AI decides upfront if it needs data, then responds with everything in one go.
"""

import discord
from discord.ext import commands
from discord import app_commands
from typing import Optional, Dict, Any
import asyncio
import time
import logging
from datetime import datetime

from src.shared.error_handling.logging import get_logger
from src.shared.utils.discord_helpers import Discord<PERSON>essageHelper
from src.bot.utils.input_sanitizer import InputSanitizer
from src.bot.utils.disclaimer_manager import add_disclaimer
from src.shared.error_handling.logging import generate_correlation_id

# Import MCP tools for when AI needs them
from src.bot.pipeline.commands.ask.dynamic_mcp_pipeline import DynamicMCPAskCommand

logger = get_logger(__name__)

class FixedAIFirstAskCommand(commands.Cog):
    """Fixed AI-first ask command - AI decides upfront and responds with everything."""
    
    def __init__(self, bot: commands.Bot):
        self.bot = bot
        self.user_locks = {}
        self.user_locks_lock = asyncio.Lock()
        
        # Initialize AI client
        self.ai_client = None
        self._init_ai_client()
        
        # Initialize MCP pipeline for when AI needs deep research
        self.mcp_pipeline = DynamicMCPAskCommand()
        
        # Performance tracking
        self.request_count = 0
        self.ai_only_responses = 0
        self.ai_plus_data_responses = 0
        
        logger.info("🚀 Fixed AI-First Ask Command initialized")
    
    def _init_ai_client(self):
        """Initialize AI client with high-quality model."""
        try:
            from src.shared.ai_chat.ai_client import AIClientWrapper
            
            # Create a mock context object
            class MockContext:
                def __init__(self):
                    self.pipeline_id = "fixed_ai_first_ask"
            
            context = MockContext()
            self.ai_client = AIClientWrapper(context)
            
            # Override the model to use DeepCogito v2
            self.ai_client.model = "deepcogito/cogito-v2-preview-deepseek-671b"
            
            logger.info("✅ AI client initialized with DeepCogito v2 model")
        except Exception as e:
            logger.warning(f"⚠️ AI client failed to initialize: {e}")
            self.ai_client = None
    
    async def _get_user_lock(self, user_id: int) -> asyncio.Lock():
        """Get or create a lock for a user to prevent concurrent requests."""
        async with self.user_locks_lock:
            if user_id not in self.user_locks:
                self.user_locks[user_id] = asyncio.Lock()
            return self.user_locks[user_id]
    
    def _ai_needs_data_upfront(self, query: str) -> bool:
        """AI decides upfront if it needs real data to answer this query."""
        query_lower = query.lower().strip()
        
        # Check for data keywords that suggest real-time data is needed
        data_keywords = [
            # Price and market data
            'price', 'current price', 'latest price', 'real-time', 'live',
            'quote', 'trading at', 'worth', 'value',
            
            # Technical analysis
            'rsi', 'macd', 'bollinger', 'technical', 'analysis', 'chart',
            'support', 'resistance', 'trend', 'momentum', 'volatility',
            'moving average', 'sma', 'ema', 'stochastic', 'williams',
            
            # Market data
            'volume', 'market cap', 'pe ratio', 'earnings', 'revenue',
            'dividend', 'yield', 'performance', 'return',
            
            # News and sentiment
            'news', 'sentiment', 'headlines', 'recent', 'latest',
            
            # Specific requests
            'show me', 'get me', 'find me', 'look up', 'check',
            'what is', 'how much', 'when did', 'where is'
        ]
        
        # Check if query contains data keywords
        for keyword in data_keywords:
            if keyword in query_lower:
                return True
        
        # Check for question patterns that suggest data needs
        question_patterns = [
            'what is the', 'what are the', 'how is the', 'how are the',
            'what\'s the', 'what\'re the', 'how\'s the', 'how\'re the',
            'tell me about', 'analyze', 'compare', 'evaluate'
        ]
        
        for pattern in question_patterns:
            if pattern in query_lower:
                return True
        
        return False
    
    async def _ai_responds_with_data(self, query: str, user_id: str) -> str:
        """AI responds with real data included."""
        try:
            if not self.ai_client:
                return "Hey! I'm your trading assistant. I can help with stock analysis, market data, and trading insights. What would you like to know?"
            
            # Get real data first
            logger.info(f"🔬 Getting real data for: {query[:50]}...")
            mcp_result = await self.mcp_pipeline.process_query(query, user_id)
            
            # Create prompt with real data
            if mcp_result.get('success', False):
                real_data = mcp_result.get('response', '')
                tool_calls = mcp_result.get('total_tool_calls', 0)
                
                prompt = f"""You are a helpful trading assistant. The user asked: "{query}"

I have real-time market data for you:
{real_data}

Please provide a comprehensive response using this real data. Be specific about the numbers and facts. If the data shows specific prices, technical indicators, or other metrics, include them in your response.

Respond naturally and helpfully, incorporating the real data into your answer."""

            else:
                # Fallback if MCP fails
                prompt = f"""You are a helpful trading assistant. The user asked: "{query}"

I tried to get real-time data but couldn't access it right now. Please respond helpfully and explain that you'd normally provide real-time data for this type of question.

Respond naturally and suggest they try again later for real-time data."""

            response = await self.ai_client.generate_response(prompt)
            
            if response:
                self.ai_plus_data_responses += 1
                return response
            else:
                return "I tried to get real-time data for you, but I'm having trouble accessing it right now. Please try again later!"
        
        except Exception as e:
            logger.error(f"❌ AI response with data failed: {e}")
            return "I tried to get real-time data for you, but I encountered an error. Please try again later!"
    
    async def _ai_responds_naturally(self, query: str) -> str:
        """AI responds naturally without needing real data."""
        try:
            if not self.ai_client:
                return "Hey! I'm your trading assistant. I can help with stock analysis, market data, and trading insights. What would you like to know?"
            
            # Simple, natural prompt
            prompt = f"""You are a helpful trading assistant. The user said: "{query}"

Respond naturally and helpfully. You can:
- Chat casually if they're greeting you
- Help with general trading questions
- Explain your capabilities
- Be friendly and conversational

Don't make up specific prices or data - just respond naturally."""

            response = await self.ai_client.generate_response(prompt)
            
            if response:
                self.ai_only_responses += 1
                return response
            else:
                return "Hey there! I'm your trading assistant. What would you like to know about the markets?"
        
        except Exception as e:
            logger.error(f"❌ AI natural response failed: {e}")
            return "Hey! I'm here to help with trading questions. What would you like to know about the markets?"
    
    async def _process_ask_request(self, interaction: discord.Interaction, query: str) -> Dict[str, Any]:
        """Process the ask request - AI decides upfront and responds with everything."""
        start_time = time.time()
        
        try:
            correlation_id = generate_correlation_id()
            user_id = interaction.user.id
            
            logger.info(f"🤖 [{correlation_id}] AI processing: {query[:100]}...")
            
            # Get user lock
            user_lock = await self._get_user_lock(user_id)
            
            async with user_lock:
                # AI decides upfront if it needs data
                needs_data = self._ai_needs_data_upfront(query)
                
                if needs_data:
                    logger.info(f"🔬 [{correlation_id}] AI needs data - getting real-time data")
                    
                    # AI responds with real data included
                    response = await self._ai_responds_with_data(query, str(user_id))
                    
                    return {
                        "success": True,
                        "response": response,
                        "query_type": "trading_with_data",
                        "processing_time": time.time() - start_time,
                        "ai_used": "ai_plus_data"
                    }
                else:
                    logger.info(f"💬 [{correlation_id}] AI responds naturally - no data needed")
                    
                    # AI responds naturally
                    response = await self._ai_responds_naturally(query)
                    
                    return {
                        "success": True,
                        "response": response,
                        "query_type": "casual_or_general",
                        "processing_time": time.time() - start_time,
                        "ai_used": "ai_only"
                    }
        
        except Exception as e:
            logger.error(f"❌ [{correlation_id}] Ask request failed: {e}")
            return {
                "success": False,
                "response": f"Sorry, I encountered an error: {str(e)}",
                "query_type": "error",
                "processing_time": time.time() - start_time,
                "error": str(e)
            }
    
    def _format_response(self, result: Dict[str, Any], query: str) -> str:
        """Format the final response."""
        response = result.get('response', 'No response generated')
        
        # Add processing time if it took a while
        processing_time = result.get('processing_time', 0)
        if processing_time > 3.0:
            response += f"\n⏱️ *Processed in {processing_time:.1f}s*"
        
        return response
    
    @app_commands.command(
        name="ask",
        description="Ask me anything - I'll respond with real data when needed"
    )
    @app_commands.describe(
        query="Your question - I'll decide if I need real data and respond with everything",
        voice="Optional voice message attachment"
    )
    async def ask_command(
        self,
        interaction: discord.Interaction,
        query: str,
        voice: Optional[discord.Attachment] = None
    ):
        """Handle the /ask slash command with fixed AI-first approach."""
        await interaction.response.defer(ephemeral=False)
        
        try:
            # Handle voice attachment
            if voice:
                if not voice.content_type or not voice.content_type.startswith('audio/'):
                    await interaction.followup.send(
                        "❌ Please attach a valid audio file.",
                        ephemeral=True
                    )
                    return
                
                query = f"[Voice message attached] {query}"
            
            # Sanitize query
            sanitized_query, is_valid, error_message = await InputSanitizer.sanitize_query(query)
            
            if not is_valid:
                await interaction.followup.send(
                    f"❌ {error_message}",
                    ephemeral=True
                )
                return
            
            # Process request
            result = await self._process_ask_request(interaction, sanitized_query)
            
            if not result.get('success', False):
                await interaction.followup.send(
                    f"❌ {result.get('response', 'Unknown error occurred')}",
                    ephemeral=True
                )
                return
            
            # Format and send response
            formatted_response = self._format_response(result, sanitized_query)
            
            # Add disclaimer for trading queries
            if result.get('query_type') == 'trading_with_data':
                final_response = add_disclaimer(formatted_response)
            else:
                final_response = formatted_response
            
            # Send response
            await interaction.followup.send(final_response)
            
            # Update stats
            self.request_count += 1
            
            # Log success
            logger.info(f"✅ Fixed AI-first ask completed for {interaction.user.name} ({result.get('ai_used', 'unknown')})")
            
        except Exception as e:
            logger.error(f"❌ Fixed AI-first ask command failed: {e}")
            await interaction.followup.send(
                "❌ Sorry, I encountered an error. Please try again.",
                ephemeral=True
            )
    
    @app_commands.command(
        name="ask_stats",
        description="View ask command statistics"
    )
    async def ask_stats_command(self, interaction: discord.Interaction):
        """Show ask command statistics."""
        await interaction.response.defer(ephemeral=True)
        
        try:
            ai_only_rate = (self.ai_only_responses / self.request_count * 100) if self.request_count > 0 else 0
            ai_plus_data_rate = (self.ai_plus_data_responses / self.request_count * 100) if self.request_count > 0 else 0
            
            stats_message = f"""
📊 **Fixed AI-First Ask Command Statistics**

**Performance:**
• Total Requests: {self.request_count}
• AI Only Responses: {self.ai_only_responses} ({ai_only_rate:.1f}%)
• AI + Data Responses: {self.ai_plus_data_responses} ({ai_plus_data_rate:.1f}%)

**How it works:**
• AI decides upfront if it needs real data
• Responds with everything in one Discord message
• No back-and-forth conversation needed
• Real data included when AI determines it's needed

**Benefits:**
• Single response per Discord command
• AI decides intelligently
• Real data when needed
• Natural conversation flow
            """
            
            await interaction.followup.send(stats_message)
            
        except Exception as e:
            logger.error(f"❌ Stats command failed: {e}")
            await interaction.followup.send(
                "❌ Failed to retrieve statistics.",
                ephemeral=True
            )
    
    async def cog_unload(self):
        """Cleanup when cog is unloaded."""
        try:
            if hasattr(self, 'mcp_pipeline'):
                await self.mcp_pipeline.close()
            logger.info("🧹 Fixed AI-First Ask Command cleaned up")
        except Exception as e:
            logger.error(f"❌ Cleanup failed: {e}")

async def setup(bot: commands.Bot):
    """Setup function for the cog."""
    await bot.add_cog(FixedAIFirstAskCommand(bot))
    logger.info("🚀 Fixed AI-First Ask Command cog loaded")
