"""
Simple AI-First Ask Command
AI decides what to do - casual chat or deep market research.
"""

import discord
from discord.ext import commands
from discord import app_commands
from typing import Optional, Dict, Any
import asyncio
import time
import logging
from datetime import datetime

from src.shared.error_handling.logging import get_logger
from src.shared.utils.discord_helpers import DiscordMessageHelper
from src.bot.utils.input_sanitizer import InputSanitizer
from src.bot.utils.disclaimer_manager import add_disclaimer
from src.shared.error_handling.logging import generate_correlation_id

# Import dynamic MCP pipeline for when AI needs deep research
from src.bot.pipeline.commands.ask.dynamic_mcp_pipeline import DynamicMCPAskCommand

logger = get_logger(__name__)

class SimpleAIAskCommand(commands.Cog):
    """Simple AI-first ask command - AI decides what to do."""
    
    def __init__(self, bot: commands.Bot):
        self.bot = bot
        self.user_locks = {}
        self.user_locks_lock = asyncio.Lock()
        
        # Initialize MCP pipeline for deep research (only when needed)
        self.deep_research_pipeline = DynamicMCPAskCommand()
        
        # Simple AI client for casual chat
        self.simple_ai_client = None
        self._init_simple_ai()
        
        # Performance tracking
        self.request_count = 0
        self.casual_responses = 0
        self.deep_research_count = 0
        
        logger.info("🚀 Simple AI-First Ask Command initialized")
    
    def _init_simple_ai(self):
        """Initialize simple AI client for casual responses."""
        try:
            from src.shared.ai_chat.ai_client import AIClientWrapper
            self.simple_ai_client = AIClientWrapper(
                pipeline_id="simple_ask",
                model="gpt-4o-mini"
            )
            logger.info("✅ Simple AI client initialized")
        except Exception as e:
            logger.warning(f"⚠️ Simple AI client failed to initialize: {e}")
            self.simple_ai_client = None
    
    async def _get_user_lock(self, user_id: int) -> asyncio.Lock:
        """Get or create a lock for a user to prevent concurrent requests."""
        async with self.user_locks_lock:
            if user_id not in self.user_locks:
                self.user_locks[user_id] = asyncio.Lock()
            return self.user_locks[user_id]
    
    def _is_casual_query(self, query: str) -> bool:
        """Determine if this is a casual query that doesn't need dee        query_lower = query.lower().strip()
        
        # Check for casual greetings and short responses
        casual_indicators = [
            'hello', 'hi', 'hey', 'waddup', 'whats up', 'how are you',
            'thanks', 'thank you', 'bye', 'goodbye', 'see you',
            'help', 'commands', 'what can you do', 'how does this work',
            'ok', 'okay', 'yes', 'no', 'sure', 'cool', 'nice'
        ]
        
        # Check for casual greetings first
        for indicator in casual_indicators:
            if indicator in query_lower:
                return True
        
        # Check if it's very short and doesn't contain trading keywords
        if len(query.split()) <= 2:
            # Only consider it casual if it doesn't have trading keywords
            trading_keywords = ['rsi', 'macd', 'price', 'stock', 'market']
            has_trading_keywords = any(keyword in query_lower for keyword in trading_keywords)
            return not has_trading_keywords
        
        # Check for trading/market keywords (more comprehensive)
        trading_keywords = [
            'price', 'stock', 'market', 'trading', 'analysis', 'chart',
            'buy', 'sell', 'hold', 'portfolio', 'investment', 'earnings',
            'rsi', 'macd', 'bollinger', 'support', 'resistance', 'trend',
            'candlestick', 'volume', 'momentum', 'volatility', 'dividend',
            'revenue', 'profit', 'loss', 'gain', 'return', 'yield',
            'sector', 'industry', 'company', 'corporation', 'inc', 'ltd',
            'nasdaq', 'nyse', 'dow', 's&p', 'spy', 'qqq', 'vix',
            'bull', 'bear', 'bullish', 'bearish', 'uptrend', 'downtrend',
            'breakout', 'breakdown', 'reversal', 'consolidation', 'range',
            'fibonacci', 'moving average', 'sma', 'ema', 'wma',
            'stochastic', 'williams', 'cci', 'adx', 'mfi', 'obv',
            'compare', 'comparison', 'vs', 'versus', 'performance',
            'sentiment', 'news', 'fundamental', 'technical', 'chart'
        ]
        
        # Check if query contains trading keywords
        has_trading_keywords = any(keyword in query_lower for keyword in trading_keywords)
        
        # If it has trading keywords, it's not casual
        if has_trading_keywords:
            return False
        
        # Check for question patterns that suggest trading interest
        question_patterns = [
            'what is', 'what are', 'how is', 'how are', 'when will',
            'should i', 'can you', 'could you', 'would you',
            'tell me about', 'show me', 'analyze', 'explain'
        ]
        
        has_question_patterns = any(pattern in query_lower for pattern in question_patterns)
        
        # If it has question patterns and is longer than 3 words, likely trading
        if has_question_patterns and len(query.split()) > 3:
            return False
        
        # Default to casual for very short queries without trading keywords
        return len(query.split()) <= 4
    
    async def _handle_casual_query(self, query: str, user_id: str) -> str:
        """Handle casual queries with simple AI responses."""
        try:
            if not self.simple_ai_client:
                return "Hey! I'm here to help with trading questions. What would you like to know about the markets?"
            
            # Simple prompt for casual responses
            prompt = f"""You are a friendly trading bot assistant. The user said: "{query}"

Respond naturally and casually. If they're greeting you, greet them back. If they're asking about your capabilities, explain that you can help with:
- Stock prices and market data
- Technical analysis (RSI, MACD, Bollinger Bands, etc.)
- Market news and sentiment
- Trading strategies and insights
- Portfolio analysis

Keep it conversational and helpful. Don't make up specific prices or data unless they ask for it."""

            response = await self.simple_ai_client.generate_response(prompt)
            
            if response:
                self.casual_responses += 1
                return response
            else:
                return "Hey there! I'm your trading assistant. I can help with stock analysis, market data, and trading insights. What would you like to know?"
        
        except Exception as e:
            logger.error(f"❌ Casual query handling failed: {e}")
            return "Hey! I'm here to help with trading questions. What would you like to know about the markets?"
    
    async def _handle_trading_query(self, query: str, user_id: str) -> Dict[str, Any]:
        """Handle trading queries with deep research capabilities."""
        try:
            logger.info(f"🔍 Deep research needed for: {query[:50]}...")
            
            # Use the dynamic MCP pipeline for deep research
            result = await self.deep_research_pipeline.process_query(query, user_id)
            
            self.deep_research_count += 1
            return result
        
        except Exception as e:
            logger.error(f"❌ Deep research failed: {e}")
            return {
                "success": False,
                "response": f"Sorry, I encountered an error while researching that for you: {str(e)}",
                "error": str(e)
            }
    
    async def _process_ask_request(self, interaction: discord.Interaction, query: str) -> Dict[str, Any]:
        """Process the ask request with AI-first approach."""
        start_time = time.time()
        
        try:
            correlation_id = generate_correlation_id()
            user_id = interaction.user.id
            
            logger.info(f"🔍 [{correlation_id}] Processing AI-first query: {query[:100]}...")
            
            # Get user lock
            user_lock = await self._get_user_lock(user_id)
            
            async with user_lock:
                # AI decides: casual chat or deep research?
                if self._is_casual_query(query):
                    logger.info(f"💬 [{correlation_id}] Casual query detected - using simple AI")
                    
                    response = await self._handle_casual_query(query, str(user_id))
                    
                    return {
                        "success": True,
                        "response": response,
                        "query_type": "casual",
                        "processing_time": time.time() - start_time,
                        "ai_used": "simple"
                    }
                else:
                    logger.info(f"🔬 [{correlation_id}] Trading query detected - using deep research")
                    
                    result = await self._handle_trading_query(query, str(user_id))
                    
                    return {
                        **result,
                        "query_type": "trading",
                        "processing_time": time.time() - start_time,
                        "ai_used": "deep_research"
                    }
        
        except Exception as e:
            logger.error(f"❌ [{correlation_id}] Ask request failed: {e}")
            return {
                "success": False,
                "response": f"Sorry, I encountered an error: {str(e)}",
                "query_type": "error",
                "processing_time": time.time() - start_time,
                "error": str(e)
            }
    
    def _format_response(self, result: Dict[str, Any], query: str) -> str:
        """Format the response based on query type."""
        response = result.get('response', 'No response generated')
        
        # Add context based on query type
        query_type = result.get('query_type', 'unknown')
        ai_used = result.get('ai_used', 'unknown')
        
        if query_type == 'casual':
            # Keep casual responses simple
            pass
        elif query_type == 'trading':
            # Add trading context
            if result.get('mcp_tools_used', False):
                response += "\n\n🤖 *Used real-time market data*"
            
            if result.get('total_tool_calls', 0) > 0:
                response += f"\n🔧 *Analyzed using {result['total_tool_calls']} data sources*"
        
        # Add processing time
        processing_time = result.get('processing_time', 0)
        if processing_time > 2.0:  # Only show if it took a while
            response += f"\n⏱️ *Processed in {processing_time:.1f}s*"
        
        return response
    
    @app_commands.command(
        name="ask",
        description="Ask me anything - casual chat or deep market research"
    )
    @app_commands.describe(
        query="Your question - I'll decide if it needs deep research or casual chat",
        voice="Optional voice message attachment"
    )
    async def ask_command(
        self,
        interaction: discord.Interaction,
        query: str,
        voice: Optional[discord.Attachment] = None
    ):
        """Handle the /ask slash command with AI-first approach."""
        await interaction.response.defer(ephemeral=False)
        
        try:
            # Handle voice attachment
            if voice:
                if not voice.content_type or not voice.content_type.startswith('audio/'):
                    await interaction.followup.send(
                        "❌ Please attach a valid audio file.",
                        ephemeral=True
                    )
                    return
                
                query = f"[Voice message attached] {query}"
            
            # Sanitize query
            sanitized_query, is_valid, error_message = await InputSanitizer.sanitize_query(query)
            
            if not is_valid:
                await interaction.followup.send(
                    f"❌ {error_message}",
                    ephemeral=True
                )
                return
            
            # Process request
            result = await self._process_ask_request(interaction, sanitized_query)
            
            if not result.get('success', False):
                await interaction.followup.send(
                    f"❌ {result.get('response', 'Unknown error occurred')}",
                    ephemeral=True
                )
                return
            
            # Format and send response
            formatted_response = self._format_response(result, sanitized_query)
            
            # Add disclaimer for trading queries
            if result.get('query_type') == 'trading':
                final_response = add_disclaimer(formatted_response)
            else:
                final_response = formatted_response
            
            # Send response
            await interaction.followup.send(final_response)
            
            # Update stats
            self.request_count += 1
            
            # Log success
            logger.info(f"✅ AI-first ask completed for {interaction.user.name} ({result.get('query_type', 'unknown')})")
            
        except Exception as e:
            logger.error(f"❌ AI-first ask command failed: {e}")
            await interaction.followup.send(
                "❌ Sorry, I encountered an error. Please try again.",
                ephemeral=True
            )
    
    @app_commands.command(
        name="ask_stats",
        description="View ask command statistics"
    )
    async def ask_stats_command(self, interaction: discord.Interaction):
        """Show ask command statistics."""
        await interaction.response.defer(ephemeral=True)
        
        try:
            casual_rate = (self.casual_responses / self.request_count * 100) if self.request_count > 0 else 0
            research_rate = (self.deep_research_count / self.request_count * 100) if self.request_count > 0 else 0
            
            stats_message = f"""
📊 **AI-First Ask Command Statistics**

**Performance:**
• Total Requests: {self.request_count}
• Casual Responses: {self.casual_responses} ({casual_rate:.1f}%)
• Deep Research: {self.deep_research_count} ({research_rate:.1f}%)

**AI Behavior:**
• Simple AI: {'✅ Available' if self.simple_ai_client else '❌ Unavailable'}
• Deep Research: {'✅ Available' if self.deep_research_pipeline else '❌ Unavailable'}

**How it works:**
• AI analyzes your query
• Casual questions → Simple, fast responses
• Trading questions → Deep research with real-time data
• No unnecessary complexity for simple questions
            """
            
            await interaction.followup.send(stats_message)
            
        except Exception as e:
            logger.error(f"❌ Stats command failed: {e}")
            await interaction.followup.send(
                "❌ Failed to retrieve statistics.",
                ephemeral=True
            )
    
    async def cog_unload(self):
        """Cleanup when cog is unloaded."""
        try:
            if hasattr(self, 'deep_research_pipeline'):
                await self.deep_research_pipeline.close()
            logger.info("🧹 Simple AI-First Ask Command cleaned up")
        except Exception as e:
            logger.error(f"❌ Cleanup failed: {e}")

async def setup(bot: commands.Bot):
    """Setup function for the cog."""
    await bot.add_cog(SimpleAIAskCommand(bot))
    logger.info("🚀 Simple AI-First Ask Command cog loaded")
