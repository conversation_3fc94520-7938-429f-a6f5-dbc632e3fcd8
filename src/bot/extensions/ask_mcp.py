"""
MCP-Enhanced Ask Command Extension
Integrates Alpha Vantage MCP for native AI data access with fallback support.
"""

import discord
from discord.ext import commands
from discord import app_commands
from typing import Optional, Dict, Any
import asyncio
import time
import logging
from datetime import datetime

from src.shared.error_handling.logging import get_logger
from src.shared.utils.symbol_extraction import extract_symbols_from_query
from src.shared.utils.discord_helpers import Discord<PERSON>essageHelper
from src.bot.utils.input_sanitizer import InputSanitizer
from src.bot.utils.disclaimer_manager import add_disclaimer
from src.shared.error_handling.logging import generate_correlation_id

# Import MCP-enhanced pipeline
from src.bot.pipeline.commands.ask.enhanced_mcp_pipeline import MCPEnhancedAskPipeline, MCPPipelineResult

logger = get_logger(__name__)

class MCPAskCommand(commands.Cog):
    """MCP-enhanced AI-powered ask command with native data access."""
    
    def __init__(self, bot: commands.Bot):
        self.bot = bot
        self.user_locks = {}
        self.user_locks_lock = asyncio.Lock()
        
        # Initialize MCP-enhanced pipeline
        self.mcp_pipeline = MCPEnhancedAskPipeline()
        
        # Performance tracking
        self.request_count = 0
        self.total_processing_time = 0.0
        self.mcp_usage_count = 0
        self.fallback_usage_count = 0
        
        logger.info("🚀 MCP Ask Command initialized")
    
    async def _get_user_lock(self, user_id: int) -> asyncio.Lock:
        """Get or create a lock for a user to prevent concurrent requests."""
        async with self.user_locks_lock:
            if user_id not in self.user_locks:
                self.user_locks[user_id] = asyncio.Lock()
            return self.user_locks[user_id]
    
    async def _check_permissions(self, interaction: discord.Interaction) -> bool:
        """Check if user has permission to use the ask command."""
        try:
            # Check if permission checker is available
            if hasattr(self.bot, 'services') and hasattr(self.bot.services, 'permission_checker'):
                permission_checker = self.bot.services.permission_checker
                if permission_checker:
                    return await permission_checker.has_permission(
                        user_id=interaction.user.id,
                        command="ask",
                        guild_id=interaction.guild_id
                    )
            
            # Default to True if no permission checker
            return True
            
        except Exception as e:
            logger.warning(f"Permission check failed: {e}")
            return True
    
    async def _sanitize_query(self, query: str) -> Tuple[str, bool, Optional[str]]:
        """Sanitize and validate the user query."""
        try:
            sanitized_query, is_valid, error_message = await InputSanitizer.sanitize_query(query)
            return sanitized_query, is_valid, error_message
        except Exception as e:
            logger.error(f"Query sanitization failed: {e}")
            return query, False, f"Sanitization error: {str(e)}"
    
    async def _process_ask_request(self, interaction: discord.Interaction, query: str) -> MCPPipelineResult:
        """Process the ask request using MCP-enhanced pipeline."""
        start_time = time.time()
        
        try:
            # Generate correlation ID for tracking
            correlation_id = generate_correlation_id()
            user_id = interaction.user.id
            
            logger.info(f"🔍 [{correlation_id}] Processing ask request from {interaction.user.name}: {query[:100]}...")
            
            # Get user lock to prevent concurrent requests
            user_lock = await self._get_user_lock(user_id)
            
            async with user_lock:
                # Process query with MCP pipeline
                result = await self.mcp_pipeline.process_query(
                    query=query,
                    user_id=str(user_id)
                )
                
                # Update performance metrics
                processing_time = time.time() - start_time
                self.request_count += 1
                self.total_processing_time += processing_time
                
                if result.mcp_used:
                    self.mcp_usage_count += 1
                if result.fallback_used:
                    self.fallback_usage_count += 1
                
                logger.info(f"✅ [{correlation_id}] Request processed in {processing_time:.2f}s")
                logger.info(f"📊 [{correlation_id}] MCP used: {result.mcp_used}, Fallback used: {result.fallback_used}")
                logger.info(f"⭐ [{correlation_id}] Data quality: {result.data_quality:.2f}")
                
                return result
        
        except Exception as e:
            logger.error(f"❌ [{correlation_id}] Ask request failed: {e}")
            return MCPPipelineResult(
                success=False,
                response=f"Sorry, I encountered an error processing your request: {str(e)}",
                data_quality=0.0,
                mcp_used=False,
                fallback_used=True,
                processing_time=time.time() - start_time,
                error=str(e)
            )
    
    def _format_response(self, result: MCPPipelineResult, query: str) -> str:
        """Format the response with additional context."""
        response = result.response
        
        # Add data source information
        if result.mcp_used and not result.fallback_used:
            response += "\n\n🤖 *Data provided by Alpha Vantage MCP*"
        elif result.fallback_used and not result.mcp_used:
            response += "\n\n🔄 *Data provided by fallback providers*"
        elif result.mcp_used and result.fallback_used:
            response += "\n\n🤖 *Data provided by Alpha Vantage MCP with fallback support*"
        
        # Add quality indicator
        if result.data_quality >= 0.8:
            response += "\n⭐ *High confidence data*"
        elif result.data_quality >= 0.6:
            response += "\n⚠️ *Medium confidence data*"
        else:
            response += "\n⚠️ *Low confidence data - please verify*"
        
        # Add processing time
        response += f"\n⏱️ *Processed in {result.processing_time:.2f}s*"
        
        return response
    
    @app_commands.command(
        name="ask",
        description="Ask me anything about trading, stocks, or market analysis with AI-powered insights"
    )
    @app_commands.describe(
        query="Your trading question or analysis request",
        voice="Optional voice message attachment"
    )
    async def ask_command(
        self,
        interaction: discord.Interaction,
        query: str,
        voice: Optional[discord.Attachment] = None
    ):
        """Handle the /ask slash command with MCP enhancement."""
        await interaction.response.defer(ephemeral=False)
        
        try:
            # Check permissions
            if not await self._check_permissions(interaction):
                await interaction.followup.send(
                    "❌ You don't have permission to use this command.",
                    ephemeral=True
                )
                return
            
            # Handle voice attachment
            if voice:
                if not voice.content_type or not voice.content_type.startswith('audio/'):
                    await interaction.followup.send(
                        "❌ Please attach a valid audio file.",
                        ephemeral=True
                    )
                    return
                
                # For now, just acknowledge voice attachment
                query = f"[Voice message attached] {query}"
            
            # Sanitize query
            sanitized_query, is_valid, error_message = await self._sanitize_query(query)
            
            if not is_valid:
                await interaction.followup.send(
                    f"❌ {error_message}",
                    ephemeral=True
                )
                return
            
            # Process request
            result = await self._process_ask_request(interaction, sanitized_query)
            
            if not result.success:
                await interaction.followup.send(
                    f"❌ {result.response}",
                    ephemeral=True
                )
                return
            
            # Format and send response
            formatted_response = self._format_response(result, sanitized_query)
            
            # Add disclaimer
            final_response = add_disclaimer(formatted_response)
            
            # Send response
            await interaction.followup.send(final_response)
            
            # Log success
            logger.info(f"✅ Ask command completed successfully for {interaction.user.name}")
            
        except Exception as e:
            logger.error(f"❌ Ask command failed: {e}")
            await interaction.followup.send(
                "❌ Sorry, I encountered an error processing your request. Please try again.",
                ephemeral=True
            )
    
    @app_commands.command(
        name="ask_stats",
        description="View statistics about the ask command performance"
    )
    async def ask_stats_command(self, interaction: discord.Interaction):
        """Show ask command statistics."""
        await interaction.response.defer(ephemeral=True)
        
        try:
            avg_processing_time = (
                self.total_processing_time / self.request_count 
                if self.request_count > 0 else 0
            )
            
            mcp_usage_rate = (
                self.mcp_usage_count / self.request_count * 100 
                if self.request_count > 0 else 0
            )
            
            fallback_usage_rate = (
                self.fallback_usage_count / self.request_count * 100 
                if self.request_count > 0 else 0
            )
            
            stats_message = f"""
📊 **Ask Command Statistics**

**Performance:**
• Total Requests: {self.request_count}
• Average Processing Time: {avg_processing_time:.2f}s
• Total Processing Time: {self.total_processing_time:.2f}s

**Data Sources:**
• MCP Usage: {self.mcp_usage_count} ({mcp_usage_rate:.1f}%)
• Fallback Usage: {self.fallback_usage_count} ({fallback_usage_rate:.1f}%)

**Status:**
• MCP Available: {'✅' if self.mcp_pipeline.hybrid_provider.mcp_client.is_configured else '❌'}
• Pipeline Status: {'✅ Active' if self.mcp_pipeline else '❌ Inactive'}
            """
            
            await interaction.followup.send(stats_message)
            
        except Exception as e:
            logger.error(f"❌ Stats command failed: {e}")
            await interaction.followup.send(
                "❌ Failed to retrieve statistics.",
                ephemeral=True
            )
    
    async def cog_unload(self):
        """Cleanup when cog is unloaded."""
        try:
            if hasattr(self, 'mcp_pipeline'):
                await self.mcp_pipeline.close()
            logger.info("🧹 MCP Ask Command cleaned up")
        except Exception as e:
            logger.error(f"❌ Cleanup failed: {e}")

async def setup(bot: commands.Bot):
    """Setup function for the cog."""
    await bot.add_cog(MCPAskCommand(bot))
    logger.info("🚀 MCP Ask Command cog loaded")
