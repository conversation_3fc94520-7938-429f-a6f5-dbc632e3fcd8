"""
Dynamic MCP Ask Command Extension
AI decides when to use MCP tools based on the query context.
"""

import discord
from discord.ext import commands
from discord import app_commands
from typing import Optional, Dict, Any
import asyncio
import time
import logging
from datetime import datetime

from src.shared.error_handling.logging import get_logger
from src.shared.utils.discord_helpers import Discord<PERSON>essageHelper
from src.bot.utils.input_sanitizer import InputSanitizer
from src.bot.utils.disclaimer_manager import add_disclaimer
from src.shared.error_handling.logging import generate_correlation_id

# Import dynamic MCP pipeline
from src.bot.pipeline.commands.ask.dynamic_mcp_pipeline import DynamicMCPAskCommand

logger = get_logger(__name__)

class DynamicMCPAskCommand(commands.Cog):
    """Dynamic MCP Ask Command - AI chooses tools as needed."""
    
    def __init__(self, bot: commands.Bot):
        self.bot = bot
        self.user_locks = {}
        self.user_locks_lock = asyncio.Lock()
        
        # Initialize dynamic MCP command
        self.ask_command = DynamicMCPAskCommand()
        
        # Performance tracking
        self.request_count = 0
        self.total_processing_time = 0.0
        self.tool_usage_count = 0
        self.mcp_tool_usage_count = 0
        
        logger.info("🚀 Dynamic MCP Ask Command initialized")
    
    async def _get_user_lock(self, user_id: int) -> asyncio.Lock:
        """Get or create a lock for a user to prevent concurrent requests."""
        async with self.user_locks_lock:
            if user_id not in self.user_locks:
                self.user_locks[user_id] = asyncio.Lock()
            return self.user_locks[user_id]
    
    async def _check_permissions(self, interaction: discord.Interaction) -> bool:
        """Check if user has permission to use the ask command."""
        try:
            # Check if permission checker is available
            if hasattr(self.bot, 'services') and hasattr(self.bot.services, 'permission_checker'):
                permission_checker = self.bot.services.permission_checker
                if permission_checker:
                    return await permission_checker.has_permission(
                        user_id=interaction.user.id,
                        command="ask",
                        guild_id=interaction.guild_id
                    )
            
            # Default to True if no permission checker
            return True
            
        except Exception as e:
            logger.warning(f"Permission check failed: {e}")
            return True
    
    async def _sanitize_query(self, query: str) -> tuple[str, bool, Optional[str]]:
        """Sanitize and validate the user query."""
        try:
            sanitized_query, is_valid, error_message = await InputSanitizer.sanitize_query(query)
            return sanitized_query, is_valid, error_message
        except Exception as e:
            logger.error(f"Query sanitization failed: {e}")
            return query, False, f"Sanitization error: {str(e)}"
    
    async def _process_ask_request(self, interaction: discord.Interaction, query: str) -> Dict[str, Any]:
        """Process the ask request using dynamic MCP pipeline."""
        start_time = time.time()
        
        try:
            # Generate correlation ID for tracking
            correlation_id = generate_correlation_id()
            user_id = interaction.user.id
            
            logger.info(f"🔍 [{correlation_id}] Processing dynamic ask request from {interaction.user.name}: {query[:100]}...")
            
            # Get user lock to prevent concurrent requests
            user_lock = await self._get_user_lock(user_id)
            
            async with user_lock:
                # Process query with dynamic MCP pipeline
                result = await self.ask_command.ask(
                    query=query,
                    user_id=str(user_id)
                )
                
                # Update performance metrics
                processing_time = time.time() - start_time
                self.request_count += 1
                self.total_processing_time += processing_time
                
                if result.get('total_tool_calls', 0) > 0:
                    self.tool_usage_count += 1
                
                if result.get('mcp_tools_used', False):
                    self.mcp_tool_usage_count += 1
                
                logger.info(f"✅ [{correlation_id}] Request processed in {processing_time:.2f}s")
                logger.info(f"🔧 [{correlation_id}] Tools used: {result.get('total_tool_calls', 0)}")
                logger.info(f"🤖 [{correlation_id}] MCP tools used: {result.get('mcp_tools_used', False)}")
                logger.info(f"⭐ [{correlation_id}] Quality score: {result.get('quality_score', 0):.2f}")
                
                return result
        
        except Exception as e:
            logger.error(f"❌ [{correlation_id}] Ask request failed: {e}")
            return {
                "success": False,
                "response": f"Sorry, I encountered an error processing your request: {str(e)}",
                "quality_score": 0.0,
                "mcp_tools_used": False,
                "tool_calls": [],
                "total_tool_calls": 0,
                "processing_time": time.time() - start_time,
                "error": str(e)
            }
    
    def _format_response(self, result: Dict[str, Any], query: str) -> str:
        """Format the response with additional context."""
        response = result.get('response', 'No response generated')
        
        # Add tool usage information
        tool_calls = result.get('total_tool_calls', 0)
        mcp_tools_used = result.get('mcp_tools_used', False)
        
        if tool_calls > 0:
            if mcp_tools_used:
                response += f"\n\n🤖 *Used {tool_calls} MCP tools for real-time data*"
            else:
                response += f"\n\n🔧 *Used {tool_calls} tools for analysis*"
        else:
            response += "\n\n💭 *General response without real-time data*"
        
        # Add quality indicator
        quality_score = result.get('quality_score', 0)
        if quality_score >= 0.8:
            response += "\n⭐ *High confidence analysis*"
        elif quality_score >= 0.6:
            response += "\n⚠️ *Medium confidence analysis*"
        else:
            response += "\n⚠️ *Low confidence analysis - please verify*"
        
        # Add processing time
        processing_time = result.get('processing_time', 0)
        response += f"\n⏱️ *Processed in {processing_time:.2f}s*"
        
        # Add tool details if available
        tool_calls_detail = result.get('tool_calls', [])
        if tool_calls_detail:
            tools_used = [call.get('tool', 'unknown') for call in tool_calls_detail]
            response += f"\n🔧 *Tools used: {', '.join(tools_used)}*"
        
        return response
    
    @app_commands.command(
        name="ask",
        description="Ask me anything about trading, stocks, or market analysis with AI-powered insights and real-time data"
    )
    @app_commands.describe(
        query="Your trading question or analysis request",
        voice="Optional voice message attachment"
    )
    async def ask_command(
        self,
        interaction: discord.Interaction,
        query: str,
        voice: Optional[discord.Attachment] = None
    ):
        """Handle the /ask slash command with dynamic MCP tool access."""
        await interaction.response.defer(ephemeral=False)
        
        try:
            # Check permissions
            if not await self._check_permissions(interaction):
                await interaction.followup.send(
                    "❌ You don't have permission to use this command.",
                    ephemeral=True
                )
                return
            
            # Handle voice attachment
            if voice:
                if not voice.content_type or not voice.content_type.startswith('audio/'):
                    await interaction.followup.send(
                        "❌ Please attach a valid audio file.",
                        ephemeral=True
                    )
                    return
                
                # For now, just acknowledge voice attachment
                query = f"[Voice message attached] {query}"
            
            # Sanitize query
            sanitized_query, is_valid, error_message = await self._sanitize_query(query)
            
            if not is_valid:
                await interaction.followup.send(
                    f"❌ {error_message}",
                    ephemeral=True
                )
                return
            
            # Process request
            result = await self._process_ask_request(interaction, sanitized_query)
            
            if not result.get('success', False):
                await interaction.followup.send(
                    f"❌ {result.get('response', 'Unknown error occurred')}",
                    ephemeral=True
                )
                return
            
            # Format and send response
            formatted_response = self._format_response(result, sanitized_query)
            
            # Add disclaimer
            final_response = add_disclaimer(formatted_response)
            
            # Send response
            await interaction.followup.send(final_response)
            
            # Log success
            logger.info(f"✅ Dynamic ask command completed successfully for {interaction.user.name}")
            
        except Exception as e:
            logger.error(f"❌ Dynamic ask command failed: {e}")
            await interaction.followup.send(
                "❌ Sorry, I encountered an error processing your request. Please try again.",
                ephemeral=True
            )
    
    @app_commands.command(
        name="ask_stats",
        description="View statistics about the ask command performance and tool usage"
    )
    async def ask_stats_command(self, interaction: discord.Interaction):
        """Show ask command statistics."""
        await interaction.response.defer(ephemeral=True)
        
        try:
            # Get pipeline stats
            pipeline_stats = await self.ask_command.get_stats()
            
            # Calculate metrics
            avg_processing_time = (
                self.total_processing_time / self.request_count 
                if self.request_count > 0 else 0
            )
            
            tool_usage_rate = (
                self.tool_usage_count / self.request_count * 100 
                if self.request_count > 0 else 0
            )
            
            mcp_usage_rate = (
                self.mcp_tool_usage_count / self.request_count * 100 
                if self.request_count > 0 else 0
            )
            
            stats_message = f"""
📊 **Dynamic Ask Command Statistics**

**Performance:**
• Total Requests: {self.request_count}
• Average Processing Time: {avg_processing_time:.2f}s
• Total Processing Time: {self.total_processing_time:.2f}s

**Tool Usage:**
• Tool Usage Rate: {tool_usage_rate:.1f}%
• MCP Tool Usage Rate: {mcp_usage_rate:.1f}%
• Tools Available: {pipeline_stats.get('tools_available', 0)}
• MCP Tools Available: {pipeline_stats.get('mcp_tools_available', 0)}

**System Status:**
• MCP Available: {'✅' if pipeline_stats.get('mcp_available', False) else '❌'}
• Model Used: {pipeline_stats.get('model_used', 'Unknown')}
• Pipeline Type: {pipeline_stats.get('pipeline_type', 'Unknown')}

**Tool Execution Stats:**
• Total Tool Executions: {pipeline_stats.get('tool_execution_stats', {}).get('total_executions', 0)}
• Success Rate: {pipeline_stats.get('tool_execution_stats', {}).get('success_rate', 0):.1f}%
            """
            
            await interaction.followup.send(stats_message)
            
        except Exception as e:
            logger.error(f"❌ Stats command failed: {e}")
            await interaction.followup.send(
                "❌ Failed to retrieve statistics.",
                ephemeral=True
            )
    
    @app_commands.command(
        name="ask_tools",
        description="View available tools that the AI can use"
    )
    async def ask_tools_command(self, interaction: discord.Interaction):
        """Show available tools."""
        await interaction.response.defer(ephemeral=True)
        
        try:
            # Get pipeline stats
            pipeline_stats = await self.ask_command.get_stats()
            
            tools_available = pipeline_stats.get('tools_available', 0)
            mcp_tools_available = pipeline_stats.get('mcp_tools_available', 0)
            
            tools_message = f"""
🔧 **Available AI Tools**

**Tool Summary:**
• Total Tools: {tools_available}
• MCP Tools: {mcp_tools_available}
• Data Tools: {tools_available - mcp_tools_available}

**MCP Tools Available:**
🤖 **Real-time Data:**
• get_global_quote - Current stock prices
• get_time_series_daily - Historical daily data
• get_time_series_intraday - Intraday data

📊 **Technical Analysis:**
• get_rsi - Relative Strength Index
• get_macd - MACD indicator
• get_bbands - Bollinger Bands
• get_sma - Simple Moving Average
• get_ema - Exponential Moving Average

📰 **News & Sentiment:**
• get_news_sentiment - News sentiment analysis
• get_company_overview - Company fundamentals

🔍 **Comprehensive Analysis:**
• get_comprehensive_analysis - Full analysis with all data

**How it works:**
The AI automatically chooses which tools to use based on your query. For example:
• "What's AAPL's price?" → Uses get_global_quote
• "Analyze MSFT technically" → Uses get_rsi, get_macd, get_bbands
• "NVDA news sentiment" → Uses get_news_sentiment
• "Full analysis of TSLA" → Uses get_comprehensive_analysis
            """
            
            await interaction.followup.send(tools_message)
            
        except Exception as e:
            logger.error(f"❌ Tools command failed: {e}")
            await interaction.followup.send(
                "❌ Failed to retrieve tool information.",
                ephemeral=True
            )
    
    async def cog_unload(self):
        """Cleanup when cog is unloaded."""
        try:
            if hasattr(self, 'ask_command'):
                await self.ask_command.close()
            logger.info("🧹 Dynamic MCP Ask Command cleaned up")
        except Exception as e:
            logger.error(f"❌ Cleanup failed: {e}")

async def setup(bot: commands.Bot):
    """Setup function for the cog."""
    await bot.add_cog(DynamicMCPAskCommand(bot))
    logger.info("🚀 Dynamic MCP Ask Command cog loaded")
