"""
Optimized Ask Command Extension - Real Performance Improvements
This implements actual caching, parallel processing, and performance optimizations
"""

import discord
from discord.ext import commands
from discord import app_commands
from typing import Optional, Dict, Any, Tuple
import asyncio
import time
import hashlib
import json
from datetime import datetime, timedelta

from src.shared.error_handling.logging import get_logger
from src.shared.utils.symbol_extraction import extract_symbols_from_query
from src.shared.utils.discord_helpers import DiscordMessageHelper
# from src.bot.pipeline.commands.ask.executor import execute_ask_pipeline  # Legacy: now using AskPipeline directly
from src.bot.pipeline.commands.ask.batch_processor import execute_batch_ask_pipeline
from src.bot.pipeline.commands.ask.pipeline import AskPipeline
from src.bot.pipeline.commands.ask.config import AskPipelineConfig
from src.bot.pipeline.commands.ask.decorators import PrePipelineResult
from src.bot.utils.input_sanitizer import InputSanitizer
from src.bot.utils.disclaimer_manager import add_disclaimer
# Rate limiter will be accessed via service container
from src.shared.error_handling.logging import generate_correlation_id

logger = get_logger(__name__)

class OptimizedAskCommand(commands.Cog):
    """Optimized AI-powered ask command with real performance improvements."""
    
    def __init__(self, bot: commands.Bot):
        self.bot = bot
        self.user_locks = {}
        self.user_locks_lock = asyncio.Lock()

        # Initialize canonical AskPipeline
        self.ask_pipeline_config = AskPipelineConfig()
        self.ask_pipeline = AskPipeline(config=self.ask_pipeline_config)

        # Performance tracking (delegated to centralized metrics service)

        # Quick response patterns for instant answers
        self.quick_patterns = {
            'price': ['price', 'cost', 'value', 'worth', 'how much'],
            'status': ['status', 'health', 'working', 'online'],
            'help': ['help', 'commands', 'how to', 'what can'],
            'time': ['time', 'date', 'when', 'now']
        }

        logger.info("🔧 Optimized Ask command initialized with canonical AskPipeline")
    
    @property
    def config(self) -> Dict[str, Any]:
        """Get configuration from the centralized config service."""
        services = getattr(self.bot, 'services', None)
        if services:
            config_service = services.get_service('config')
            if config_service:
                return config_service.all()
        return {}
    
    @property
    def cache_service(self):
        """Get cache service from the service container."""
        services = getattr(self.bot, 'services', None)
        return services.get_service('cache_service') if services else None
    
    @property
    def metrics_service(self):
        """Get metrics service from the service container."""
        services = getattr(self.bot, 'services', None)
        return services.get_service('metrics_service') if services else None
    
    @property
    def rate_limiter(self):
        """Get rate limiter from the service container."""
        services = getattr(self.bot, 'services', None)
        return services.get_service('rate_limiter') if services else None
    
    @property
    def data_provider_aggregator(self):
        """Get data provider aggregator from the service container."""
        services = getattr(self.bot, 'services', None)
        return services.get_service('data_provider_aggregator') if services else None

    @property
    def ai_service(self):
        """Get AI service from bot."""
        # Try direct access first (current structure)
        ai_service = getattr(self.bot, 'ai_service', None)
        if ai_service:
            return ai_service
        
        # Try services structure as fallback
        services = getattr(self.bot, 'services', None)
        if services and hasattr(services, 'get_service'):
            return services.get_service('ai_service')
        
        return None

    @app_commands.command(name="ask", description="Ask the AI about trading and markets (Optimized)")
    @app_commands.describe(
        query="Your question about trading and markets",
        attachment="Voice message attachment (optional)"
    )
    async def ask_command(self, interaction: discord.Interaction, query: Optional[str] = None, attachment: Optional[discord.Attachment] = None):
        """Ask the AI about trading and markets with real performance optimizations"""
        start_time = time.time()
        logger.info(f"🔧 Ask command started with query: {query}")
        
        try:
            # Defer immediately to prevent timeout
            await interaction.response.defer(thinking=True)
            logger.info("🔧 Ask command deferred response")
            
            # Check if required components are ready
            if not await self._check_required_components(interaction):
                logger.error("🔧 Ask command failed: required components not ready")
                return
            logger.info("🔧 Ask command: required components check passed")
            
            # Handle voice input if attachment is provided
            query_text = query
            if attachment is not None:
                query_text = await self._process_voice_attachment(interaction, attachment)
                if query_text is None:
                    return
            elif query is None:
                await interaction.followup.send(
                    "❌ Please provide either a text query or a voice message attachment.",
                    ephemeral=True
                )
                return
            
            # Sanitize the query
            sanitized_query, is_valid, error_message = await InputSanitizer.sanitize_query(query_text)
            if not is_valid:
                await interaction.followup.send(f"❌ {error_message}", ephemeral=True)
                return
            
            # Check for sensitive information
            if InputSanitizer.contains_sensitive_info(sanitized_query):
                await interaction.followup.send(
                    "❌ Your query appears to contain sensitive information. Please remove this information and try again.",
                    ephemeral=True
                )
                return
            
            # Try quick response first (instant answers)
            quick_response = await self._get_quick_response(sanitized_query)
            if quick_response:
                await interaction.followup.send(quick_response)
                self._track_performance(time.time() - start_time, "quick_response")
                return
            
            # Check cache for repeated queries
            cache_key = self._generate_cache_key(sanitized_query)
            cached_response = await self._get_cached_response(cache_key)
            if cached_response:
                await interaction.followup.send(cached_response)
                self._track_performance(time.time() - start_time, "cached_response")
                logger.info(f"🎯 Cache hit for query: {sanitized_query[:50]}...")
                return
            
            # Rate limiting
            if self.rate_limiter:
                user_id = str(interaction.user.id)
                async with self.user_locks_lock:
                    if user_id not in self.user_locks:
                        self.user_locks[user_id] = asyncio.Lock()
                user_lock = self.user_locks[user_id]
                
                async with user_lock:
                    if not self.rate_limiter.can_make_request(user_id):
                        remaining_time = self.rate_limiter.get_remaining_time(user_id)
                        await interaction.followup.send(
                            f"⏱️ You've reached the rate limit for AI queries. Please try again in {remaining_time:.1f} hours."
                        )
                        return
                    self.rate_limiter.record_user_query(user_id)
            
            # Process the query with canonical AskPipeline
            response, initial_analysis = await self._process_query_canonical(
                interaction, sanitized_query, start_time
            )

            if response:
                # Use smart caching policy based on intent and data needs
                should_cache = True
                cache_ttl = None
                try:
                    from src.shared.ai_services.smart_model_router import router
                    intent = getattr(initial_analysis, 'intent', 'general_question') if initial_analysis else 'general_question'
                    needs_data = getattr(initial_analysis, 'needs_data', False) if initial_analysis else False

                    # Get smart cache configuration
                    cache_config = router.get_cache_config_for_intent(intent, needs_data)
                    should_cache = cache_config.get('enabled', True) and not cache_config.get('bypass', False)
                    cache_ttl = cache_config.get('ttl_seconds', 300)

                    if not should_cache:
                        logger.info(f"⚡ Live data query detected ({intent}, needs_data={needs_data}) — bypassing cache")
                    elif cache_ttl < 300:
                        logger.info(f"⏱️ Short TTL cache ({cache_ttl}s) for {intent} query")
                except Exception as e:
                    logger.warning(f"Failed to get smart cache config: {e}")
                    # Fallback to original logic
                    if initial_analysis and getattr(initial_analysis, 'intent', '') in ('stock_analysis', 'price_check') \
                       and getattr(initial_analysis, 'needs_data', True):
                        should_cache = False
                        logger.info("⚡ Live price query detected — skipping cache (fallback)")

                if should_cache:
                    await self._cache_response(cache_key, response, ttl=cache_ttl)

                await interaction.followup.send(response)
                self._track_performance(time.time() - start_time, "ai_processing")

        except Exception as e:
            logger.error(f"Error in optimized ask command: {e}", exc_info=True)
            await interaction.followup.send(
                "❌ I encountered an error while processing your request. Please try again later.",
                ephemeral=True
            )
    
    async def _check_required_components(self, interaction: discord.Interaction) -> bool:
        """Check if required components are initialized"""
        # Check if bot has ai_service directly (current structure)
        ai_service = getattr(self.bot, 'ai_service', None)
        logger.info(f"🔍 Debug: Direct ai_service check: {ai_service is not None}")
        
        if not ai_service:
            # Try the services structure as fallback
            services = getattr(self.bot, 'services', None)
            logger.info(f"🔍 Debug: Services object: {services is not None}")
            if services and hasattr(services, 'get_service'):
                ai_service = services.get_service('ai_service')
                logger.info(f"🔍 Debug: Services ai_service: {ai_service is not None}")
        
        logger.info(f"🔍 Debug: Final ai_service check: {ai_service is not None}")
        
        if not ai_service:
            await interaction.followup.send(
                "⚠️ AI service is not available. Please try again later.",
                ephemeral=True
            )
            return False
        
        return True
    
    async def _get_quick_response(self, query: str) -> Optional[str]:
        """Get instant responses for common queries without AI processing"""
        query_lower = query.lower()
        
        # Quick status responses
        if any(word in query_lower for word in self.quick_patterns['status']):
            return "🤖 Bot is online and operational! All systems are working correctly."
        
        # Quick help responses
        if any(word in query_lower for word in self.quick_patterns['help']):
            return "📚 Use `/help` to see all available commands. I can help with trading analysis, market data, and AI-powered insights!"
        
        # Quick time responses
        if any(word in query_lower for word in self.quick_patterns['time']):
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S UTC")
            return f"🕐 Current time: {current_time}"
        
        # Quick price responses for simple symbols
        if any(word in query_lower for word in self.quick_patterns['price']):
            symbols = extract_symbols_from_query(query)

            if symbols and len(symbols) == 1 and self.data_provider_aggregator:
                try:
                    # Use optimized timeout for quick price lookup
                    quick_price_timeout = self.config.get('performance.quick_price_timeout', 2.0)
                    price_data = await asyncio.wait_for(
                        self.data_provider_aggregator.get_current_price(symbols[0]),
                        timeout=quick_price_timeout
                    )
                    
                    if price_data and price_data.get('success'):
                        price = price_data.get('price')
                        change = price_data.get('change')
                        change_percent = price_data.get('change_percent')
                        
                        if price:
                            response_parts = [
                                f"**💰 {symbols[0]} Current Price**",
                                f"📈 **Price**: ${price:.2f}"
                            ]
                            
                            if change is not None:
                                change_emoji = "📈" if change >= 0 else "📉"
                                response_parts.append(f"{change_emoji} **Change**: {change:+.2f} ({change_percent:+.2f}%)")
                            
                            response_parts.append("\n⚠️ *This is real-time price data. For detailed analysis, use the /analyze command.*")
                            
                            return "\n".join(response_parts)
                except asyncio.TimeoutError:
                    logger.warning(f"Quick price lookup timeout for {symbols[0]}")
                except Exception as e:
                    logger.warning(f"Quick price response failed for {symbols[0]}: {e}")
        
        return None
    
    async def _process_voice_attachment(self, interaction: discord.Interaction, attachment: discord.Attachment) -> Optional[str]:
        """Process voice attachment and return text (optimized)"""
        try:
            from src.bot.pipeline.commands.ask.stages.voice_processor import voice_processor
            voice_result = await voice_processor.process_voice_attachment(attachment)
            
            if voice_result['success']:
                query_text = voice_result['text']
                await interaction.followup.send(
                    f"🎤 Voice message processed: \"{query_text[:100]}{'...' if len(query_text) > 100 else ''}\"",
                    ephemeral=True
                )
                return query_text
            else:
                await interaction.followup.send(
                    f"❌ Failed to process voice message: {voice_result.get('error', 'Unknown error')}",
                    ephemeral=True
                )
                return None
        except Exception as e:
            logger.error(f"Error processing voice attachment: {e}", exc_info=True)
            await interaction.followup.send(
                "❌ Failed to process voice message. Please try again or use text input.",
                ephemeral=True
            )
            return None
    
    async def _process_query_canonical(self, interaction: discord.Interaction, query: str, start_time: float) -> Tuple[Optional[str], Optional[object]]:
        """Process query using canonical AskPipeline"""
        try:
            # Generate correlation ID
            correlation_id = generate_correlation_id()

            # Extract symbols for batch detection
            symbols = extract_symbols_from_query(query)
            is_batch_query = len(symbols) > 1

            # Use batch processing for multi-symbol queries (keep existing logic)
            if is_batch_query:
                await interaction.followup.send(
                    f"🔍 Processing batch query for {len(symbols)} symbols: {', '.join(symbols)}..."
                )

                batch_timeout = self.config.get('performance.batch_ask_timeout', 20.0)
                context = await asyncio.wait_for(
                    execute_batch_ask_pipeline(
                        query=query,
                        symbols=symbols,
                        user_id=str(interaction.user.id),
                        guild_id=str(interaction.guild_id) if interaction.guild_id else None,
                        correlation_id=correlation_id
                    ),
                    timeout=batch_timeout
                )

                response = context.processing_results.get('response', '')
                initial_analysis = None  # Batch processor handles its own analysis
            else:
                # Use canonical AskPipeline for single queries with improved timeout handling
                regular_timeout = self.config.get('performance.ask_timeout', 25.0)  # Increased for reliability
                try:
                    result = await asyncio.wait_for(
                        self.ask_pipeline.process_query(
                            query=query,
                            user_id=str(interaction.user.id),
                            username=str(interaction.user.display_name),
                            context={
                                'guild_id': str(interaction.guild_id) if interaction.guild_id else None,
                                'interaction_id': str(interaction.id)
                            },
                            correlation_id=correlation_id
                        ),
                        timeout=regular_timeout
                    )
                except asyncio.TimeoutError:
                    logger.warning(f"Pipeline timeout after {regular_timeout}s, providing fallback response")
                    # Provide a helpful fallback response instead of generic timeout
                    symbols = extract_symbols_from_query(query)

                    if symbols:
                        fallback_response = f"⏰ AI services are experiencing high demand. I found {len(symbols)} symbols: {', '.join(symbols)}. Please try again in a moment for detailed analysis."
                    else:
                        fallback_response = "⏰ AI services are experiencing high demand. Please try again in a moment, or try a more specific question with stock symbols."

                    result = {
                        'response': fallback_response,
                        'initial_analysis': None
                    }

                response = result.get('response', '')
                initial_analysis = result.get('initial_analysis')

            logger.info(f"🔧 Pipeline response: '{response[:100]}...' (length: {len(response)})")

            if not response:
                logger.warning("🔧 Empty response from pipeline, using fallback")
                response = f"I received your query: '{query}'. However, I wasn't able to generate a specific response. Could you please rephrase your question or provide more details about what you'd like to know about trading or markets?"

            # Add disclaimer
            response = add_disclaimer(response, {
                'command': 'batch_ask' if is_batch_query else 'ask',
                'query': query,
                'symbols': symbols if is_batch_query else None
            })

            # Truncate very long responses
            if len(response) > 1900:
                response = response[:1900] + "\n\n*Response truncated due to length.*"

            return response, initial_analysis

        except asyncio.TimeoutError:
            elapsed_time = time.time() - start_time
            logger.warning(f"Query timeout after {elapsed_time:.2f}s")

            # Provide helpful timeout response with symbol extraction
            symbols = extract_symbols_from_query(query)

            if symbols:
                timeout_response = f"⏰ Request timed out after {elapsed_time:.1f}s. I found symbols: {', '.join(symbols)}. AI services may be experiencing high demand - please try again."
            else:
                timeout_response = f"⏰ Request timed out after {elapsed_time:.1f}s. AI services may be experiencing high demand. Try a more specific question or try again later."

            return timeout_response, None
        except Exception as e:
            logger.error(f"Error processing query: {e}", exc_info=True)
            return "❌ I encountered an error while processing your request. Please try again later.", None

    def _generate_cache_key(self, query: str) -> str:
        """
        Generate a cache key for the query.
        Note: user_id is intentionally excluded to allow for sharing cache results
        for common, non-personalized queries across all users.
        """
        normalized_query = query.lower().strip()
        return hashlib.md5(normalized_query.encode()).hexdigest()
    
    async def _get_cached_response(self, cache_key: str) -> Optional[str]:
        """Get cached response from centralized cache service"""
        if not self.cache_service:
            return None
        
        try:
            response = await self.cache_service.get(f"ask:{cache_key}")
            if response:
                # Track cache hit
                if self.metrics_service:
                    asyncio.create_task(self.metrics_service.increment_counter(
                        "ask_command_cache_hits",
                        tags={"cache_type": "response"}
                    ))
            return response
        except Exception as e:
            logger.warning(f"Cache service error: {e}")
            return None
    
    async def _cache_response(self, cache_key: str, response: str, ttl: Optional[int] = None):
        """Cache the response using centralized cache service"""
        if not self.cache_service:
            return

        try:
            # Use provided TTL or fall back to config default
            cache_ttl = ttl if ttl is not None else self.config.get('performance.ask_cache_ttl', 1800)
            await self.cache_service.set(f"ask:{cache_key}", response, cache_ttl)
            
            # Track cache miss (since we're storing a new response)
            if self.metrics_service:
                asyncio.create_task(self.metrics_service.increment_counter(
                    "ask_command_cache_misses",
                    tags={"cache_type": "response"}
                ))
        except Exception as e:
            logger.warning(f"Cache service error: {e}")
    
    def _track_performance(self, execution_time: float, response_type: str):
        """Track performance metrics using centralized metrics service"""
        # Track metrics in centralized service
        if self.metrics_service:
            try:
                asyncio.create_task(self.metrics_service.record_timing(
                    f"ask_command_{response_type}",
                    execution_time,
                    {"response_type": response_type}
                ))
                asyncio.create_task(self.metrics_service.increment_counter(
                    "ask_command_requests",
                    tags={"response_type": response_type}
                ))
            except Exception as e:
                logger.warning(f"Metrics tracking error: {e}")
        
        # Log performance metrics
        logger.info(f"📊 Performance: {response_type} in {execution_time:.2f}s")
    
    @commands.Cog.listener()
    async def on_ready(self):
        """Log performance statistics on bot ready"""
        if self.metrics_service:
            try:
                summary = await self.metrics_service.get_metrics_summary()
                logger.info(f"🚀 Ask Command Metrics: {summary['total_metrics']} metrics tracked")
            except Exception as e:
                logger.warning(f"Failed to get metrics summary: {e}")

async def setup(bot: commands.Bot):
    """Setup the optimized ask command extension"""
    await bot.add_cog(OptimizedAskCommand(bot))
    logger.info("✅ Optimized Ask command extension loaded (Real Performance Improvements)")
