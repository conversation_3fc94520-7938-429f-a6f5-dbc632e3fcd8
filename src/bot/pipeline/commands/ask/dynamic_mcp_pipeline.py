"""
Dynamic MCP Pipeline - AI decides when to use MCP tools
The AI can call MCP tools whenever it deems necessary for the best response.
"""

import asyncio
import logging
import time
from typing import Dict, Any, Optional
from datetime import datetime

from ...core.pipeline_engine import BasePipelineStage, StageResult, PipelineEngine
from ...core.context_manager import PipelineContext, QualityScore, PipelineStatus

# Import enhanced AI client and tool management
from src.shared.ai_services.enhanced_ai_client import <PERSON>hancedA<PERSON><PERSON>
from src.shared.ai_services.ai_tool_registry import <PERSON>ToolRegistry, MCPToolManager
from src.shared.data_providers.alpha_vantage_mcp import AlphaVantageMCPClient

# Import intent detection
from src.shared.ai_services.enhanced_intent_detector import EnhancedIntentDetector

logger = logging.getLogger(__name__)

class DynamicMCPPipeline:
    """
    Dynamic MCP Pipeline where AI decides when to use MCP tools.
    The AI has access to all MCP tools and can call them as needed.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize the dynamic MCP pipeline."""
        self.config = config or {}
        
        # Initialize MCP client
        self.mcp_client = AlphaVantageMCPClient()
        
        # Initialize tool management
        self.tool_registry = AIToolRegistry()
        self.mcp_tool_manager = MCPToolManager(self.mcp_client)
        
        # Get the enhanced tool registry from MCP manager
        self.enhanced_tool_registry = self.mcp_tool_manager.get_tool_registry()
        
        # Initialize AI client with tool access
        api_key = self.config.get('openai_api_key') or self._get_openai_key()
        self.ai_client = EnhancedAIClient(
            api_key=api_key,
            model=self.config.get('model', 'gpt-4o-mini'),
            tool_registry=self.enhanced_tool_registry,
            mcp_tool_manager=self.mcp_tool_manager
        )
        
        # Initialize intent detector
        self.intent_detector = EnhancedIntentDetector()
        
        logger.info("🚀 Dynamic MCP Pipeline initialized")
        logger.info(f"🔧 Available tools: {len(self.enhanced_tool_registry.tools)}")
        logger.info(f"🤖 MCP tools: {len(self.enhanced_tool_registry.mcp_tools)}")
    
    def _get_openai_key(self) -> str:
        """Get OpenAI API key from environment."""
        import os
        return os.getenv('OPENAI_API_KEY', '')
    
    async def process_query(self, query: str, user_id: str = "unknown") -> Dict[str, Any]:
        """Process a trading query with dynamic MCP tool access."""
        start_time = time.time()
        
        try:
            logger.info(f"🚀 Processing dynamic MCP query: {query[:100]}...")
            
            # Detect intent for context
            intent_analysis = await self.intent_detector.analyze_intent(query)
            symbols = intent_analysis.entities.get("symbols", [])
            
            logger.info(f"📊 Detected intent: {intent_analysis.primary_intent}")
            logger.info(f"📈 Symbols detected: {symbols}")
            
            # Let AI decide what tools to use
            logger.info("🤖 AI is analyzing query and deciding which tools to use...")
            
            # Generate response with dynamic tool access
            ai_result = await self.ai_client.generate_response_with_tools(query)
            
            processing_time = time.time() - start_time
            
            # Extract results
            response = ai_result.get("response", "No response generated")
            tool_calls = ai_result.get("tool_calls", [])
            total_tool_calls = ai_result.get("total_tool_calls", 0)
            
            # Determine data sources used
            mcp_tools_used = any(
                call.get("tool") in self.enhanced_tool_registry.mcp_tools
                for call in tool_calls
            )
            
            # Calculate quality score based on tool usage and success
            quality_score = self._calculate_quality_score(ai_result, tool_calls)
            
            logger.info(f"✅ Dynamic MCP processing completed in {processing_time:.2f}s")
            logger.info(f"🔧 Tools used: {total_tool_calls}")
            logger.info(f"🤖 MCP tools used: {mcp_tools_used}")
            logger.info(f"⭐ Quality score: {quality_score:.2f}")
            
            return {
                "success": ai_result.get("success", False),
                "response": response,
                "quality_score": quality_score,
                "mcp_tools_used": mcp_tools_used,
                "tool_calls": tool_calls,
                "total_tool_calls": total_tool_calls,
                "processing_time": processing_time,
                "intent_analysis": {
                    "primary_intent": intent_analysis.primary_intent,
                    "symbols": symbols,
                    "confidence": intent_analysis.confidence
                },
                "tools_available": len(self.enhanced_tool_registry.tools),
                "model_used": self.ai_client.model,
                "error": ai_result.get("error")
            }
        
        except Exception as e:
            logger.error(f"❌ Dynamic MCP pipeline failed: {e}")
            return {
                "success": False,
                "response": f"Sorry, I encountered an error: {str(e)}",
                "quality_score": 0.0,
                "mcp_tools_used": False,
                "tool_calls": [],
                "total_tool_calls": 0,
                "processing_time": time.time() - start_time,
                "error": str(e)
            }
    
    def _calculate_quality_score(self, ai_result: Dict[str, Any], tool_calls: list) -> float:
        """Calculate quality score based on AI result and tool usage."""
        base_score = 0.5
        
        # Success bonus
        if ai_result.get("success", False):
            base_score += 0.2
        
        # Tool usage bonus
        if tool_calls:
            base_score += 0.2
            
            # MCP tool usage bonus
            mcp_tools_used = any(
                call.get("tool") in self.enhanced_tool_registry.mcp_tools
                for call in tool_calls
            )
            if mcp_tools_used:
                base_score += 0.1
        
        # Response length bonus (indicates comprehensive response)
        response_length = len(ai_result.get("response", ""))
        if response_length > 500:
            base_score += 0.1
        elif response_length > 200:
            base_score += 0.05
        
        return min(base_score, 1.0)
    
    async def get_pipeline_stats(self) -> Dict[str, Any]:
        """Get pipeline statistics."""
        tool_stats = await self.ai_client.get_tool_stats()
        
        return {
            "pipeline_type": "dynamic_mcp",
            "mcp_available": self.mcp_client.is_configured,
            "tools_available": len(self.enhanced_tool_registry.tools),
            "mcp_tools_available": len(self.enhanced_tool_registry.mcp_tools),
            "tool_execution_stats": tool_stats,
            "model_used": self.ai_client.model
        }
    
    async def close(self):
        """Close the pipeline and all connections."""
        try:
            if self.ai_client:
                await self.ai_client.close()
            
            if self.mcp_tool_manager:
                await self.mcp_tool_manager.close()
            
            logger.info("🧹 Dynamic MCP Pipeline closed")
        
        except Exception as e:
            logger.error(f"❌ Error closing pipeline: {e}")

class DynamicMCPAskCommand:
    """Simplified ask command using dynamic MCP pipeline."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize the dynamic MCP ask command."""
        self.pipeline = DynamicMCPPipeline(config)
        logger.info("🚀 Dynamic MCP Ask Command initialized")
    
    async def ask(self, query: str, user_id: str = "unknown") -> Dict[str, Any]:
        """Process an ask request with dynamic MCP tools."""
        return await self.pipeline.process_query(query, user_id)
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get command statistics."""
        return await self.pipeline.get_pipeline_stats()
    
    async def close(self):
        """Close the command."""
        await self.pipeline.close()
