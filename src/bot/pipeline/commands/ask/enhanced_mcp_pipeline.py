"""
Enhanced MCP-Integrated Ask Command Pipeline
Integrates Alpha Vantage MCP for native AI data access with fallback support.
"""

import asyncio
import logging
import time
from typing import Dict, Any, Optional, List
from datetime import datetime
from dataclasses import dataclass

from ...core.pipeline_engine import BasePipelineStage, StageResult, PipelineEngine
from ...core.context_manager import PipelineContext, QualityScore, PipelineStatus

# Import MCP and hybrid providers
from src.shared.data_providers.hybrid_mcp_provider import HybridMCPProvider
from src.shared.data_providers.alpha_vantage_mcp import AlphaVantageMCPClient

# Import AI services
from src.shared.ai_services.enhanced_intent_detector import EnhancedIntentDetector, IntentAnalysis
from src.shared.ai_chat.ai_client import AIClientWrapper
from src.shared.ai_services.anti_hallucination_prompt import get_anti_hallucination_prompt
from src.shared.ai_services.fact_verifier import TradingFactVerifier

# Import technical analysis
from src.shared.technical_analysis.enhanced_calculator import EnhancedTechnicalCalculator

logger = logging.getLogger(__name__)

@dataclass
class MCPPipelineResult:
    """Result from MCP-enhanced pipeline."""
    success: bool
    response: str
    data_quality: float
    mcp_used: bool
    fallback_used: bool
    technical_analysis: Optional[Dict[str, Any]] = None
    fact_verification: Optional[Dict[str, Any]] = None
    processing_time: float = 0.0
    error: Optional[str] = None

class MCPDataFetcherStage(BasePipelineStage):
    """Stage that fetches data using MCP with intelligent fallback."""
    
    def __init__(self, hybrid_provider: HybridMCPProvider):
        super().__init__("mcp_data_fetcher")
        self.hybrid_provider = hybrid_provider
        self.mcp_client = hybrid_provider.mcp_client
    
    async def execute(self, context: PipelineContext) -> StageResult:
        """Execute data fetching with MCP primary, fallback support."""
        start_time = time.time()
        
        try:
            query = context.get("query", "")
            symbols = context.get("symbols", [])
            
            if not symbols:
                return StageResult(
                    success=False,
                    data={"error": "No symbols to fetch data for"},
                    quality_score=0.0
                )
            
            # Try MCP first for AI-optimized data
            mcp_data = {}
            mcp_success = False
            
            if self.mcp_client.is_configured:
                try:
                    logger.info(f"🤖 Fetching data via MCP for symbols: {symbols}")
                    
                    # Get comprehensive data for each symbol
                    for symbol in symbols:
                        try:
                            # Use MCP for comprehensive analysis
                            symbol_data = await self.mcp_client.get_comprehensive_analysis(symbol)
                            mcp_data[symbol] = symbol_data
                            mcp_success = True
                            
                            logger.info(f"✅ MCP data fetched for {symbol}")
                            
                        except Exception as e:
                            logger.warning(f"⚠️ MCP failed for {symbol}: {e}")
                            # Continue with other symbols
                            continue
                
                except Exception as e:
                    logger.warning(f"⚠️ MCP comprehensive analysis failed: {e}")
            
            # Fallback to hybrid provider if MCP failed
            fallback_data = {}
            if not mcp_success or not mcp_data:
                logger.info("🔄 Falling back to hybrid provider")
                
                for symbol in symbols:
                    try:
                        # Get current price and historical data
                        price_data = await self.hybrid_provider.get_current_price(symbol)
                        hist_data = await self.hybrid_provider.get_historical_data(symbol)
                        
                        fallback_data[symbol] = {
                            "price": price_data,
                            "historical": hist_data,
                            "source": "hybrid_fallback"
                        }
                        
                    except Exception as e:
                        logger.error(f"❌ Fallback failed for {symbol}: {e}")
                        fallback_data[symbol] = {"error": str(e)}
            
            # Combine data
            combined_data = mcp_data if mcp_success else fallback_data
            
            processing_time = time.time() - start_time
            
            return StageResult(
                success=len(combined_data) > 0,
                data={
                    "market_data": combined_data,
                    "mcp_used": mcp_success,
                    "fallback_used": not mcp_success,
                    "processing_time": processing_time
                },
                quality_score=0.9 if mcp_success else 0.7
            )
        
        except Exception as e:
            logger.error(f"❌ MCP Data Fetcher failed: {e}")
            return StageResult(
                success=False,
                data={"error": str(e)},
                quality_score=0.0
            )

class MCPTechnicalAnalysisStage(BasePipelineStage):
    """Stage that performs technical analysis using MCP or fallback."""
    
    def __init__(self, hybrid_provider: HybridMCPProvider):
        super().__init__("mcp_technical_analysis")
        self.hybrid_provider = hybrid_provider
        self.technical_calculator = EnhancedTechnicalCalculator()
    
    async def execute(self, context: PipelineContext) -> StageResult:
        """Execute technical analysis with MCP primary, fallback support."""
        start_time = time.time()
        
        try:
            symbols = context.get("symbols", [])
            market_data = context.get("market_data", {})
            
            if not symbols:
                return StageResult(
                    success=False,
                    data={"error": "No symbols for technical analysis"},
                    quality_score=0.0
                )
            
            technical_results = {}
            
            for symbol in symbols:
                try:
                    # Try MCP technical analysis first
                    if self.hybrid_provider.mcp_client.is_configured:
                        try:
                            logger.info(f"📊 Getting MCP technical analysis for {symbol}")
                            mcp_technical = await self.hybrid_provider.get_technical_indicators(symbol)
                            
                            if mcp_technical.get("success"):
                                technical_results[symbol] = {
                                    "indicators": mcp_technical.get("indicators", {}),
                                    "source": "mcp",
                                    "quality": "high"
                                }
                                continue
                        
                        except Exception as e:
                            logger.warning(f"⚠️ MCP technical analysis failed for {symbol}: {e}")
                    
                    # Fallback to enhanced calculator
                    logger.info(f"📊 Using fallback technical analysis for {symbol}")
                    
                    if symbol in market_data and "historical" in market_data[symbol]:
                        hist_data = market_data[symbol]["historical"]
                        
                        if hist_data.get("success") and "data" in hist_data:
                            indicators = await self.technical_calculator.calculate_indicators_from_historical_data(
                                hist_data["data"]
                            )
                            
                            technical_results[symbol] = {
                                "indicators": indicators,
                                "source": "fallback",
                                "quality": "medium"
                            }
                        else:
                            technical_results[symbol] = {
                                "error": "No historical data available",
                                "source": "none",
                                "quality": "low"
                            }
                    else:
                        technical_results[symbol] = {
                            "error": "No market data available",
                            "source": "none",
                            "quality": "low"
                        }
                
                except Exception as e:
                    logger.error(f"❌ Technical analysis failed for {symbol}: {e}")
                    technical_results[symbol] = {
                        "error": str(e),
                        "source": "error",
                        "quality": "low"
                    }
            
            processing_time = time.time() - start_time
            
            return StageResult(
                success=len(technical_results) > 0,
                data={
                    "technical_analysis": technical_results,
                    "processing_time": processing_time
                },
                quality_score=0.8 if any(r.get("quality") == "high" for r in technical_results.values()) else 0.6
            )
        
        except Exception as e:
            logger.error(f"❌ MCP Technical Analysis failed: {e}")
            return StageResult(
                success=False,
                data={"error": str(e)},
                quality_score=0.0
            )

class MCPAIResponseStage(BasePipelineStage):
    """Stage that generates AI response with MCP-optimized data."""
    
    def __init__(self, ai_client: AIClientWrapper, fact_verifier: TradingFactVerifier):
        super().__init__("mcp_ai_response")
        self.ai_client = ai_client
        self.fact_verifier = fact_verifier
    
    async def execute(self, context: PipelineContext) -> StageResult:
        """Execute AI response generation with fact verification."""
        start_time = time.time()
        
        try:
            query = context.get("query", "")
            market_data = context.get("market_data", {})
            technical_analysis = context.get("technical_analysis", {})
            
            # Create comprehensive context for AI
            ai_context = {
                "query": query,
                "market_data": market_data,
                "technical_analysis": technical_analysis,
                "timestamp": datetime.now().isoformat()
            }
            
            # Generate anti-hallucination prompt
            anti_hallucination_prompt = get_anti_hallucination_prompt(
                query=query,
                real_data=market_data,
                technical_analysis=technical_analysis
            )
            
            # Generate AI response
            logger.info("🤖 Generating AI response with MCP data")
            ai_response = await self.ai_client.generate_response(anti_hallucination_prompt)
            
            if not ai_response:
                return StageResult(
                    success=False,
                    data={"error": "AI response generation failed"},
                    quality_score=0.0
                )
            
            # Verify response against facts
            logger.info("🔍 Verifying AI response against facts")
            fact_verification = await self.fact_verifier.verify_response(
                response=ai_response,
                market_data=market_data,
                technical_analysis=technical_analysis
            )
            
            processing_time = time.time() - start_time
            
            return StageResult(
                success=True,
                data={
                    "ai_response": ai_response,
                    "fact_verification": fact_verification,
                    "processing_time": processing_time
                },
                quality_score=fact_verification.get("overall_score", 0.5)
            )
        
        except Exception as e:
            logger.error(f"❌ MCP AI Response failed: {e}")
            return StageResult(
                success=False,
                data={"error": str(e)},
                quality_score=0.0
            )

class MCPEnhancedAskPipeline:
    """Enhanced Ask Pipeline with MCP integration."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize the MCP-enhanced pipeline."""
        self.config = config or {}
        
        # Initialize providers
        self.hybrid_provider = HybridMCPProvider(self.config)
        
        # Initialize AI services
        self.intent_detector = EnhancedIntentDetector()
        self.ai_client = AIClientWrapper(
            pipeline_id="mcp_ask_pipeline",
            model="gpt-4o-mini"  # Use a reliable model
        )
        self.fact_verifier = TradingFactVerifier()
        
        # Initialize pipeline
        self.pipeline = self._build_pipeline()
        
        logger.info("🚀 MCP Enhanced Ask Pipeline initialized")
    
    def _build_pipeline(self) -> PipelineEngine:
        """Build the MCP-enhanced pipeline."""
        builder = PipelineEngine.Builder()
        
        # Add stages
        builder.add_stage(MCPDataFetcherStage(self.hybrid_provider))
        builder.add_stage(MCPTechnicalAnalysisStage(self.hybrid_provider))
        builder.add_stage(MCPAIResponseStage(self.ai_client, self.fact_verifier))
        
        return builder.build()
    
    async def process_query(self, query: str, user_id: str = "unknown") -> MCPPipelineResult:
        """Process a trading query with MCP enhancement."""
        start_time = time.time()
        
        try:
            logger.info(f"🚀 Processing MCP query: {query[:100]}...")
            
            # Detect intent and extract symbols
            intent_analysis = await self.intent_detector.analyze_intent(query)
            symbols = intent_analysis.entities.get("symbols", [])
            
            logger.info(f"📊 Detected intent: {intent_analysis.primary_intent}, Symbols: {symbols}")
            
            # Create pipeline context
            context = PipelineContext()
            context.set("query", query)
            context.set("symbols", symbols)
            context.set("user_id", user_id)
            context.set("intent_analysis", intent_analysis)
            
            # Execute pipeline
            pipeline_result = await self.pipeline.execute(context)
            
            # Extract results
            market_data = context.get("market_data", {})
            technical_analysis = context.get("technical_analysis", {})
            ai_response_data = context.get("ai_response", "")
            fact_verification = context.get("fact_verification", {})
            
            processing_time = time.time() - start_time
            
            # Determine data sources used
            mcp_used = any(
                data.get("source") == "alpha_vantage_mcp" 
                for data in market_data.values()
            )
            fallback_used = not mcp_used
            
            return MCPPipelineResult(
                success=pipeline_result.success,
                response=ai_response_data,
                data_quality=fact_verification.get("overall_score", 0.5),
                mcp_used=mcp_used,
                fallback_used=fallback_used,
                technical_analysis=technical_analysis,
                fact_verification=fact_verification,
                processing_time=processing_time,
                error=pipeline_result.error if not pipeline_result.success else None
            )
        
        except Exception as e:
            logger.error(f"❌ MCP Pipeline failed: {e}")
            return MCPPipelineResult(
                success=False,
                response=f"Sorry, I encountered an error: {str(e)}",
                data_quality=0.0,
                mcp_used=False,
                fallback_used=True,
                processing_time=time.time() - start_time,
                error=str(e)
            )
    
    async def close(self):
        """Close all connections."""
        if self.hybrid_provider:
            await self.hybrid_provider.close()
        
        if self.ai_client:
            await self.ai_client.close()
