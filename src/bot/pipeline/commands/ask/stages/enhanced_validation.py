"""
Enhanced Validation Stage

This stage integrates the enhanced fact-checking and cross-validation AI systems
into the ask pipeline for maximum trustworthiness.
"""

import asyncio
import time
from typing import Dict, Any, Optional, List
from datetime import datetime

from src.shared.error_handling.logging import get_logger
from src.shared.validation.enhanced_fact_checker import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MultiSourceFactValidator
from src.shared.ai_services.cross_validation_ai import CrossValidationAI, ValidationImportance
from src.shared.monitoring.pipeline_grader import PipelineGrader

logger = get_logger(__name__)

class EnhancedValidationStage:
    """
    Enhanced validation stage that combines fact-checking and cross-validation
    for maximum response trustworthiness
    """
    
    def __init__(self):
        self.fact_checker = EnhancedFactChecker()
        self.multi_source_validator = MultiSourceFactValidator()
        self.cross_validator = CrossValidationAI()
        
        # Validation configuration
        self.validation_config = {
            'enable_fact_checking': True,
            'enable_cross_validation': True,
            'enable_multi_source_validation': True,
            'fact_check_threshold': 70.0,  # Minimum confidence for fact-checking
            'cross_validation_threshold': 75.0,  # Minimum confidence for cross-validation
            'require_human_review_threshold': 60.0  # Below this, require human review
        }
    
    async def process(self, context: Dict[str, Any], grader: Optional[PipelineGrader] = None) -> Dict[str, Any]:
        """
        Process the validation stage with comprehensive fact-checking and cross-validation
        
        Args:
            context: Pipeline context containing query, response, symbols, etc.
            grader: Optional pipeline grader for performance tracking
            
        Returns:
            Enhanced context with validation results
        """
        if grader:
            grader.start_step("enhanced_validation")
        
        logger.info("🛡️ Starting enhanced validation stage")
        
        try:
            # Extract necessary information from context
            ai_response = context.get('response', '')
            query = context.get('query', '')
            symbols = context.get('symbols', [])
            
            if not ai_response:
                logger.warning("No AI response to validate")
                if grader:
                    try:
                        grader.end_step(success=False,
                                       metrics={"error": "no_response_to_validate"})
                    except Exception as e:
                        logger.warning(f"Failed to end grader step: {e}")
                return context
            
            # Determine validation importance based on query content
            importance = self._determine_validation_importance(query, ai_response)
            
            validation_results = {}
            
            # 1. Enhanced Fact-Checking
            if self.validation_config['enable_fact_checking']:
                fact_start = time.time()
                fact_check_result = await self._perform_fact_checking(
                    ai_response, {'symbols': symbols, 'query': query}
                )
                fact_duration = time.time() - fact_start
                validation_results['fact_check'] = fact_check_result
                logger.info(f"📊 Fact-check complete in {fact_duration:.2f}s: {fact_check_result.confidence_score:.1f}% confidence")
            
            # 2. Multi-Source Validation
            if self.validation_config['enable_multi_source_validation']:
                multi_start = time.time()
                multi_source_result = await self._perform_multi_source_validation(
                    ai_response, {'symbols': symbols, 'query': query}
                )
                multi_duration = time.time() - multi_start
                validation_results['multi_source'] = multi_source_result
                logger.info(f"🔗 Multi-source validation complete in {multi_duration:.2f}s: {multi_source_result.confidence_score:.1f}% confidence")
            
            # 3. Cross-Validation with Multiple AI Models
            if self.validation_config['enable_cross_validation'] and importance in [ValidationImportance.CRITICAL, ValidationImportance.HIGH]:
                cross_start = time.time()
                cross_validation_result = await self._perform_cross_validation(
                    query, {'symbols': symbols}, importance
                )
                cross_duration = time.time() - cross_start
                validation_results['cross_validation'] = cross_validation_result
                logger.info(f"🤖 Cross-validation complete in {cross_duration:.2f}s: {cross_validation_result.consensus_level.value}")
            
            # 4. Generate Enhanced Response
            enhanced_response = await self._generate_enhanced_response(
                ai_response, validation_results, importance
            )
            
            # 5. Determine Final Validation Status
            final_validation = self._calculate_final_validation(validation_results)
            
            # Update context with validation results
            context.update({
                'response': enhanced_response,
                'validation_results': validation_results,
                'validation_confidence': final_validation['confidence'],
                'validation_status': final_validation['status'],
                'requires_human_review': final_validation['requires_review'],
                'validation_timestamp': datetime.now().isoformat()
            })
            
            if grader:
                try:
                    grader.end_step(success=True,
                                   metrics={
                                       "validation_confidence": final_validation['confidence'],
                                       "validation_status": final_validation['status'],
                                       "importance_level": importance.value,
                                       "fact_check_enabled": self.validation_config['enable_fact_checking'],
                                       "cross_validation_enabled": self.validation_config['enable_cross_validation']
                                   })
                except Exception as e:
                    logger.warning(f"Failed to end grader step: {e}")
            
            logger.info(f"✅ Enhanced validation complete: {final_validation['status']} "
                       f"({final_validation['confidence']:.1f}% confidence)")
            
            return context
            
        except Exception as e:
            logger.error(f"Enhanced validation failed: {e}", exc_info=True)
            if grader:
                try:
                    grader.end_step(success=False,
                                   metrics={"error": str(e)})
                except Exception as grader_error:
                    logger.warning(f"Failed to end grader step: {grader_error}")
            
            # Return original context on validation failure
            context['validation_error'] = str(e)
            return context
    
    def _determine_validation_importance(self, query: str, response: str) -> ValidationImportance:
        """Determine the importance level for validation based on query and response content"""
        query_lower = query.lower()
        response_lower = response.lower()
        
        # Critical importance indicators
        critical_indicators = [
            'buy', 'sell', 'invest', 'trade', 'recommendation', 'advice',
            'should i', 'what to do', 'portfolio', 'strategy'
        ]
        
        # High importance indicators
        high_indicators = [
            'analysis', 'prediction', 'forecast', 'target', 'price',
            'technical', 'fundamental', 'valuation'
        ]
        
        # Check for critical indicators
        if any(indicator in query_lower or indicator in response_lower for indicator in critical_indicators):
            return ValidationImportance.CRITICAL
        
        # Check for high importance indicators
        if any(indicator in query_lower or indicator in response_lower for indicator in high_indicators):
            return ValidationImportance.HIGH
        
        # Check for educational content
        educational_indicators = ['what is', 'how does', 'explain', 'definition']
        if any(indicator in query_lower for indicator in educational_indicators):
            return ValidationImportance.LOW
        
        return ValidationImportance.MEDIUM
    
    async def _perform_fact_checking(self, response: str, context: Dict[str, Any]) -> Any:
        """Perform enhanced fact-checking"""
        try:
            return await self.fact_checker.fact_check_response(response, context)
        except Exception as e:
            logger.error(f"Fact-checking failed: {e}")
            # Return a minimal result to avoid breaking the pipeline
            from src.shared.validation.enhanced_fact_checker import FactCheckResult
            return FactCheckResult(
                is_accurate=False,
                confidence_score=0.0,
                issues=[],
                validated_claims=0,
                total_claims=0,
                data_sources_used=[],
                validation_timestamp=datetime.now()
            )
    
    async def _perform_multi_source_validation(self, response: str, context: Dict[str, Any]) -> Any:
        """Perform multi-source validation"""
        try:
            return await self.multi_source_validator.validate_with_consensus(response, context)
        except Exception as e:
            logger.error(f"Multi-source validation failed: {e}")
            # Return a minimal result
            from src.shared.validation.enhanced_fact_checker import FactCheckResult
            return FactCheckResult(
                is_accurate=False,
                confidence_score=0.0,
                issues=[],
                validated_claims=0,
                total_claims=0,
                data_sources_used=[],
                validation_timestamp=datetime.now()
            )
    
    async def _perform_cross_validation(self, query: str, context: Dict[str, Any], importance: ValidationImportance) -> Any:
        """Perform cross-validation with multiple AI models"""
        try:
            return await self.cross_validator.cross_validate_response(query, context, importance)
        except Exception as e:
            logger.error(f"Cross-validation failed: {e}")
            # Return a minimal result
            from src.shared.ai_services.cross_validation_ai import CrossValidationResult, ConsensusLevel
            return CrossValidationResult(
                consensus_level=ConsensusLevel.DISAGREEMENT,
                consensus_confidence=0.0,
                primary_response="",
                model_responses=[],
                disagreements=[],
                consensus_points=[],
                final_recommendation=None,
                validation_timestamp=datetime.now(),
                requires_human_review=True
            )
    
    async def _generate_enhanced_response(self, original_response: str, validation_results: Dict[str, Any], importance: ValidationImportance) -> str:
        """Generate enhanced response with validation information"""
        enhanced_parts = [original_response]
        
        # Add validation summary for important queries
        if importance in [ValidationImportance.CRITICAL, ValidationImportance.HIGH]:
            enhanced_parts.append("\n---")
            enhanced_parts.append("**🛡️ Response Validation Summary**")
            
            # Add fact-check results
            if 'fact_check' in validation_results:
                fact_result = validation_results['fact_check']
                status_emoji = "✅" if fact_result.is_accurate else "⚠️"
                enhanced_parts.append(f"{status_emoji} **Fact-Check**: {fact_result.confidence_score:.1f}% confidence")
                
                if fact_result.issues:
                    enhanced_parts.append(f"⚠️ **Issues Found**: {len(fact_result.issues)} potential concerns")
            
            # Add cross-validation results
            if 'cross_validation' in validation_results:
                cross_result = validation_results['cross_validation']
                consensus_emoji = "🤖" if cross_result.consensus_level.value == "strong_agreement" else "⚠️"
                enhanced_parts.append(f"{consensus_emoji} **AI Consensus**: {cross_result.consensus_level.value.replace('_', ' ').title()}")
                
                if cross_result.disagreements:
                    enhanced_parts.append(f"🔍 **Model Disagreements**: {len(cross_result.disagreements)} detected")
            
            # Add data source information
            if 'multi_source' in validation_results:
                multi_result = validation_results['multi_source']
                if multi_result.data_sources_used:
                    enhanced_parts.append(f"📊 **Data Sources**: {', '.join(multi_result.data_sources_used[:3])}")
        
        return "\n".join(enhanced_parts)
    
    def _calculate_final_validation(self, validation_results: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate final validation status and confidence"""
        confidences = []
        issues = []
        requires_review = False
        
        # Collect confidence scores
        if 'fact_check' in validation_results:
            fact_result = validation_results['fact_check']
            confidences.append(fact_result.confidence_score)
            issues.extend(fact_result.issues)
            
        if 'multi_source' in validation_results:
            multi_result = validation_results['multi_source']
            confidences.append(multi_result.confidence_score)
            issues.extend(multi_result.issues)
        
        if 'cross_validation' in validation_results:
            cross_result = validation_results['cross_validation']
            confidences.append(cross_result.consensus_confidence)
            requires_review = requires_review or cross_result.requires_human_review
        
        # Calculate overall confidence
        if confidences:
            overall_confidence = sum(confidences) / len(confidences)
        else:
            overall_confidence = 50.0  # Neutral when no validation performed
        
        # Determine status
        if overall_confidence >= 85:
            status = "highly_validated"
        elif overall_confidence >= 70:
            status = "validated"
        elif overall_confidence >= 50:
            status = "partially_validated"
        else:
            status = "validation_concerns"
            requires_review = True
        
        # Check for critical issues
        critical_issues = [issue for issue in issues if hasattr(issue, 'severity') and issue.severity.value == 'critical']
        if critical_issues:
            status = "critical_issues_found"
            requires_review = True
        
        return {
            'confidence': overall_confidence,
            'status': status,
            'requires_review': requires_review,
            'total_issues': len(issues),
            'critical_issues': len(critical_issues) if critical_issues else 0
        }
