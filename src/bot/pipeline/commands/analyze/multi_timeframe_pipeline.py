"""
Multi-Timeframe Comprehensive Analysis Pipeline

This module implements a comprehensive multi-timeframe analysis pipeline that automatically
performs deep technical analysis across multiple timeframes with all indicators.
"""

import asyncio
from typing import Dict, Any, Optional, List
import time
import logging
from datetime import datetime, timedelta

from src.shared.error_handling.logging import get_trading_logger
from src.shared.data_providers import ProviderError
from src.bot.pipeline.core.parallel_pipeline import (
    FlexiblePipelineBuilder, ParallelPipelineEngine
)
from src.bot.pipeline.core.pipeline_engine import BasePipelineStage, StageResult
from src.bot.pipeline.core.context_manager import PipelineContext, PipelineStatus

logger = get_trading_logger("multi_timeframe_pipeline")

class MultiTimeframeDataFetchStage(BasePipelineStage):
    """Stage for fetching market data across multiple timeframes"""
    
    def __init__(self):
        super().__init__("multi_timeframe_data_fetch", {})
        self.outputs = ["multi_timeframe_data", "current_market_data"]
        self.timeframes = ['1d', '1w', '1m', '3m', '6m', '1y']
    
    async def process(self, context: PipelineContext) -> StageResult:
        """Fetch market data across all timeframes"""
        start_time = time.time()
        ticker = context.ticker
        
        try:
            from src.shared.data_providers.aggregator import DataProviderAggregator
            aggregator = DataProviderAggregator()
            
            # Fetch current market data
            current_data = await aggregator.get_ticker(ticker)
            if not current_data or 'error' in current_data:
                raise ProviderError(f"Failed to fetch current data for {ticker}")
            
            # Fetch historical data for each timeframe
            timeframe_data = {}
            
            # Define periods for each timeframe - ensuring sufficient data for all indicators
            period_mapping = {
                '1d': ('5d', '1h'),    # 5 days, hourly intervals (120 data points)
                '1w': ('1mo', '1d'),   # 1 month, daily intervals (22 data points)
                '1m': ('3mo', '1d'),   # 3 months, daily intervals (64 data points)
                '3m': ('6mo', '1d'),   # 6 months, daily intervals (127 data points)
                '6m': ('1y', '1d'),    # 1 year, daily intervals (252 data points)
                '1y': ('2y', '1wk')    # 2 years, weekly intervals (104 data points)
            }
            
            # Fetch data for each timeframe in parallel
            fetch_tasks = []
            for timeframe in self.timeframes:
                period, interval = period_mapping[timeframe]
                task = self._fetch_timeframe_data(aggregator, ticker, timeframe, period, interval)
                fetch_tasks.append(task)
            
            # Wait for all timeframe data
            timeframe_results = await asyncio.gather(*fetch_tasks, return_exceptions=True)
            
            # Process results
            for i, result in enumerate(timeframe_results):
                timeframe = self.timeframes[i]
                if isinstance(result, Exception):
                    logger.warning(f"Failed to fetch {timeframe} data for {ticker}: {result}")
                    timeframe_data[timeframe] = None
                else:
                    timeframe_data[timeframe] = result
            
            # Ensure we have at least some data
            valid_timeframes = [tf for tf, data in timeframe_data.items() if data is not None]
            if not valid_timeframes:
                raise ProviderError(f"Failed to fetch data for any timeframe for {ticker}")
            
            logger.info(f"Successfully fetched data for {len(valid_timeframes)}/{len(self.timeframes)} timeframes for {ticker}")
            
            return StageResult(
                success=True,
                output_data={
                    "multi_timeframe_data": timeframe_data,
                    "current_market_data": current_data,
                    "valid_timeframes": valid_timeframes
                },
                execution_time=time.time() - start_time
            )
            
        except Exception as e:
            logger.error(f"Failed to fetch multi-timeframe data for {ticker}: {e}")
            return StageResult(
                success=False,
                output_data={},
                execution_time=time.time() - start_time,
                error_message=f"Multi-timeframe data fetch failed: {str(e)}"
            )
    
    async def _fetch_timeframe_data(self, aggregator, ticker: str, timeframe: str, period: str, interval: str):
        """Fetch data for a specific timeframe"""
        try:
            data = await aggregator.get_history(ticker, period=period, interval=interval)
            if data and 'error' not in data:
                return data
            return None
        except Exception as e:
            logger.warning(f"Failed to fetch {timeframe} data for {ticker}: {e}")
            return None

class ComprehensiveTechnicalAnalysisStage(BasePipelineStage):
    """Stage for comprehensive technical analysis across all timeframes"""

    def __init__(self):
        super().__init__("comprehensive_technical_analysis", {})
        self.required_inputs = ["multi_timeframe_data", "current_market_data"]
        self.outputs = ["comprehensive_technical_analysis"]
    
    async def process(self, context: PipelineContext) -> StageResult:
        """Perform comprehensive technical analysis across all timeframes"""
        start_time = time.time()
        
        try:
            multi_timeframe_data = context.processing_results.get("multi_timeframe_data", {})
            current_data = context.processing_results.get("current_market_data", {})
            
            if not multi_timeframe_data:
                raise ValueError("No multi-timeframe data available for analysis")
            
            # Perform analysis for each timeframe
            timeframe_analysis = {}
            
            for timeframe, data in multi_timeframe_data.items():
                if data is None:
                    continue
                
                analysis = await self._analyze_timeframe(data, timeframe, current_data)
                timeframe_analysis[timeframe] = analysis
            
            # Generate comprehensive summary
            comprehensive_summary = self._generate_comprehensive_summary(timeframe_analysis, current_data)
            
            return StageResult(
                success=True,
                output_data={
                    "comprehensive_technical_analysis": {
                        "timeframe_analysis": timeframe_analysis,
                        "comprehensive_summary": comprehensive_summary,
                        "analysis_timestamp": datetime.now().isoformat()
                    }
                },
                execution_time=time.time() - start_time
            )
            
        except Exception as e:
            logger.error(f"Comprehensive technical analysis failed: {e}")
            return StageResult(
                success=False,
                output_data={},
                execution_time=time.time() - start_time,
                error_message=f"Technical analysis failed: {str(e)}"
            )
    
    async def _analyze_timeframe(self, data: Dict[str, Any], timeframe: str, current_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze a specific timeframe with all indicators"""
        try:
            from src.shared.technical_analysis.calculator import TechnicalAnalysisCalculator

            # Initialize analyzers
            indicators = TechnicalAnalysisCalculator()
            
            # Convert data to DataFrame format expected by calculator
            df_data = _convert_to_dataframe(data)

            # Calculate all technical indicators
            technical_indicators = indicators.calculate_all_indicators(df_data, timeframe)

            # For now, use placeholder for pattern recognition and support/resistance
            patterns = {"patterns": [], "note": "Pattern recognition not yet implemented"}
            support_resistance = {"support_levels": [], "resistance_levels": [], "note": "Support/resistance not yet implemented"}
            
            # Calculate trend analysis
            trend_analysis = self._calculate_trend_analysis(data, technical_indicators)
            
            # Calculate momentum indicators
            momentum_analysis = self._calculate_momentum_analysis(technical_indicators)
            
            # Calculate volume analysis
            volume_analysis = self._calculate_volume_analysis(data)
            
            return {
                "timeframe": timeframe,
                "technical_indicators": technical_indicators,
                "patterns": patterns,
                "support_resistance": support_resistance,
                "trend_analysis": trend_analysis,
                "momentum_analysis": momentum_analysis,
                "volume_analysis": volume_analysis,
                "analysis_quality": self._assess_analysis_quality(data, technical_indicators)
            }
            
        except Exception as e:
            logger.error(f"Failed to analyze {timeframe} timeframe: {e}")
            return {
                "timeframe": timeframe,
                "error": str(e),
                "analysis_quality": "poor"
            }
    
    def _calculate_trend_analysis(self, data: Dict[str, Any], indicators: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate comprehensive trend analysis"""
        try:
            # Get current price from indicators (more reliable)
            current_price = indicators.get('price', 0)
            if not current_price:
                return {"trend": "insufficient_data"}

            # Get calculated indicators
            sma_20 = indicators.get('sma_20')
            sma_50 = indicators.get('sma_50')
            ema_20 = indicators.get('ema_20')
            ema_50 = indicators.get('ema_50')
            rsi = indicators.get('rsi')

            # Ensure we have basic indicators
            if not sma_20:
                return {"trend": "insufficient_data"}
            
            # Determine trend direction
            trend_signals = []
            if sma_20 and current_price > sma_20:
                trend_signals.append("bullish_sma20")
            if sma_50 and current_price > sma_50:
                trend_signals.append("bullish_sma50")
            if ema_20 and ema_50 and ema_20 > ema_50:
                trend_signals.append("bullish_ema_cross")

            # RSI analysis
            if rsi:
                if rsi > 70:
                    trend_signals.append("overbought")
                elif rsi > 50:
                    trend_signals.append("bullish_momentum")
                elif rsi < 30:
                    trend_signals.append("oversold")
                else:
                    trend_signals.append("bearish_momentum")
            
            # Calculate trend strength
            if len(trend_signals) >= 2:
                trend = "strong_bullish" if len(trend_signals) == 3 else "bullish"
            elif len(trend_signals) == 1:
                trend = "weak_bullish"
            else:
                trend = "bearish"
            
            # Calculate trend strength (capped at 100%)
            max_signals = 5  # Maximum expected signals
            trend_strength = min(len(trend_signals) / max_signals, 1.0)

            return {
                "trend": trend,
                "trend_signals": trend_signals,
                "trend_strength": trend_strength
            }
            
        except Exception as e:
            logger.error(f"Trend analysis calculation failed: {e}")
            return {"trend": "error", "error": str(e)}
    
    def _calculate_momentum_analysis(self, indicators: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate momentum analysis from indicators"""
        try:
            rsi = indicators.get('rsi')
            macd = indicators.get('macd')
            macd_signal = indicators.get('macd_signal')
            bb_width = indicators.get('bb_width')

            momentum_signals = []

            # RSI analysis
            if rsi:
                if rsi > 70:
                    momentum_signals.append("overbought_rsi")
                elif rsi < 30:
                    momentum_signals.append("oversold_rsi")
                elif 40 <= rsi <= 60:
                    momentum_signals.append("neutral_rsi")

            # MACD analysis
            if macd and macd_signal:
                if macd > macd_signal:
                    momentum_signals.append("bullish_macd")
                else:
                    momentum_signals.append("bearish_macd")

            # Bollinger Band width analysis
            if bb_width:
                if bb_width > 0.1:
                    momentum_signals.append("high_volatility")
                elif bb_width < 0.05:
                    momentum_signals.append("low_volatility")

            return {
                "momentum_signals": momentum_signals,
                "rsi_value": rsi,
                "macd_status": "bullish" if "bullish_macd" in momentum_signals else "bearish",
                "volatility": "high" if "high_volatility" in momentum_signals else "low" if "low_volatility" in momentum_signals else "normal"
            }
            
        except Exception as e:
            logger.error(f"Momentum analysis calculation failed: {e}")
            return {"error": str(e)}
    
    def _calculate_volume_analysis(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate volume analysis"""
        try:
            volumes = data.get('Volume', [])
            if not volumes or len(volumes) < 20:
                return {"volume_trend": "insufficient_data"}
            
            # Calculate average volume
            recent_volume = sum(volumes[-5:]) / 5 if len(volumes) >= 5 else volumes[-1]
            avg_volume = sum(volumes[-20:]) / 20 if len(volumes) >= 20 else sum(volumes) / len(volumes)
            
            volume_ratio = recent_volume / avg_volume if avg_volume > 0 else 1
            
            if volume_ratio > 1.5:
                volume_trend = "high_volume"
            elif volume_ratio < 0.7:
                volume_trend = "low_volume"
            else:
                volume_trend = "normal_volume"
            
            return {
                "volume_trend": volume_trend,
                "volume_ratio": volume_ratio,
                "recent_volume": recent_volume,
                "average_volume": avg_volume
            }
            
        except Exception as e:
            logger.error(f"Volume analysis calculation failed: {e}")
            return {"error": str(e)}
    
    def _assess_analysis_quality(self, data: Dict[str, Any], indicators: Dict[str, Any]) -> str:
        """Assess the quality of analysis based on data availability"""
        try:
            # Extract data points from HistoricalData object
            data_points = 0
            if 'data' in data and hasattr(data['data'], 'closes'):
                data_points = len(data['data'].closes)

            # Count non-None indicators
            indicator_count = len([v for v in indicators.values() if v is not None and v != 0])

            # More realistic quality thresholds
            if data_points >= 200 and indicator_count >= 8:
                return "excellent"
            elif data_points >= 100 and indicator_count >= 6:
                return "good"
            elif data_points >= 50 and indicator_count >= 4:
                return "fair"
            elif data_points >= 20 and indicator_count >= 2:
                return "limited"
            else:
                return "poor"

        except Exception:
            return "unknown"
    
    def _generate_comprehensive_summary(self, timeframe_analysis: Dict[str, Any], current_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive summary across all timeframes"""
        try:
            # Aggregate trends across timeframes
            trend_consensus = {}
            momentum_consensus = {}
            
            for timeframe, analysis in timeframe_analysis.items():
                if 'error' in analysis:
                    continue
                
                trend = analysis.get('trend_analysis', {}).get('trend', 'unknown')
                momentum = analysis.get('momentum_analysis', {}).get('momentum_signals', [])
                
                trend_consensus[timeframe] = trend
                momentum_consensus[timeframe] = momentum
            
            # Calculate overall consensus
            bullish_trends = sum(1 for trend in trend_consensus.values() if 'bullish' in trend)
            bearish_trends = sum(1 for trend in trend_consensus.values() if 'bearish' in trend)
            
            if bullish_trends > bearish_trends:
                overall_trend = "bullish"
            elif bearish_trends > bullish_trends:
                overall_trend = "bearish"
            else:
                overall_trend = "neutral"
            
            return {
                "overall_trend": overall_trend,
                "trend_consensus": trend_consensus,
                "momentum_consensus": momentum_consensus,
                "analysis_confidence": self._calculate_confidence(timeframe_analysis),
                "key_insights": self._generate_key_insights(timeframe_analysis, current_data)
            }
            
        except Exception as e:
            logger.error(f"Failed to generate comprehensive summary: {e}")
            return {"error": str(e)}
    
    def _calculate_confidence(self, timeframe_analysis: Dict[str, Any]) -> float:
        """Calculate realistic confidence score for the analysis"""
        try:
            total_timeframes = len(timeframe_analysis)
            if total_timeframes == 0:
                return 0.0

            # Calculate confidence based on analysis quality, not just success
            quality_scores = []
            for analysis in timeframe_analysis.values():
                if 'error' in analysis:
                    quality_scores.append(0.0)
                else:
                    quality = analysis.get('analysis_quality', 'poor')
                    quality_score = {
                        'excellent': 0.95,
                        'good': 0.80,
                        'fair': 0.65,
                        'limited': 0.45,
                        'poor': 0.25,
                        'unknown': 0.10
                    }.get(quality, 0.10)
                    quality_scores.append(quality_score)

            # Average quality score across timeframes
            avg_confidence = sum(quality_scores) / len(quality_scores) if quality_scores else 0.0

            # Cap confidence at 85% for financial analysis (never 100% certain)
            return min(avg_confidence, 0.85)

        except Exception:
            return 0.25
    
    def _generate_key_insights(self, timeframe_analysis: Dict[str, Any], current_data: Dict[str, Any]) -> List[str]:
        """Generate key insights from the analysis"""
        insights = []
        
        try:
            # Check for consistent trends across timeframes
            trends = []
            for analysis in timeframe_analysis.values():
                if 'error' not in analysis:
                    trend = analysis.get('trend_analysis', {}).get('trend', '')
                    if trend:
                        trends.append(trend)
            
            if len(set(trends)) == 1 and trends:
                insights.append(f"Strong consensus: {trends[0]} trend across all timeframes")
            elif len(trends) > 0:
                bullish_count = sum(1 for t in trends if 'bullish' in t)
                if bullish_count > len(trends) / 2:
                    insights.append("Majority of timeframes show bullish signals")
                else:
                    insights.append("Mixed signals across timeframes - exercise caution")
            
            # Add current price context
            current_price = current_data.get('current_price')
            if current_price:
                insights.append(f"Current price: ${current_price:.2f}")
            
            return insights
            
        except Exception as e:
            logger.error(f"Failed to generate key insights: {e}")
            return ["Analysis completed with limited insights due to processing error"]

class RiskAssessmentStage(BasePipelineStage):
    """Stage for comprehensive risk assessment"""

    def __init__(self):
        super().__init__("risk_assessment", {})
        self.required_inputs = ["multi_timeframe_data", "current_market_data"]
        self.outputs = ["risk_assessment"]

    async def process(self, context: PipelineContext) -> StageResult:
        """Perform comprehensive risk assessment"""
        start_time = time.time()

        try:
            multi_timeframe_data = context.processing_results.get("multi_timeframe_data", {})
            current_data = context.processing_results.get("current_market_data", {})

            # Calculate volatility across timeframes
            volatility_analysis = self._calculate_volatility_analysis(multi_timeframe_data)

            # Calculate beta and correlation metrics
            market_correlation = await self._calculate_market_correlation(multi_timeframe_data)

            # Calculate risk metrics
            risk_metrics = self._calculate_risk_metrics(multi_timeframe_data, current_data)

            # Generate risk assessment
            risk_assessment = {
                "volatility_analysis": volatility_analysis,
                "market_correlation": market_correlation,
                "risk_metrics": risk_metrics,
                "overall_risk_level": self._determine_risk_level(volatility_analysis, risk_metrics, multi_timeframe_data),
                "risk_factors": self._identify_risk_factors(volatility_analysis, market_correlation, risk_metrics)
            }

            return StageResult(
                success=True,
                output_data={"risk_assessment": risk_assessment},
                execution_time=time.time() - start_time
            )

        except Exception as e:
            logger.error(f"Risk assessment failed: {e}")
            return StageResult(
                success=False,
                output_data={},
                execution_time=time.time() - start_time,
                error_message=f"Risk assessment failed: {str(e)}"
            )

    def _calculate_volatility_analysis(self, multi_timeframe_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate volatility analysis across timeframes"""
        try:
            volatility_by_timeframe = {}

            for timeframe, data in multi_timeframe_data.items():
                if data is None or 'data' not in data:
                    continue

                hist_data = data['data']
                if not hasattr(hist_data, 'closes') or len(hist_data.closes) < 2:
                    continue

                prices = hist_data.closes

                # Calculate daily returns
                returns = [(prices[i] - prices[i-1]) / prices[i-1] for i in range(1, len(prices))]

                # Calculate volatility (standard deviation of returns)
                if returns:
                    import statistics
                    volatility = statistics.stdev(returns) * 100  # Convert to percentage
                    volatility_by_timeframe[timeframe] = {
                        "volatility": volatility,
                        "volatility_level": self._classify_volatility(volatility)
                    }

            # Calculate average volatility
            volatilities = [v["volatility"] for v in volatility_by_timeframe.values()]
            avg_volatility = sum(volatilities) / len(volatilities) if volatilities else 0

            return {
                "by_timeframe": volatility_by_timeframe,
                "average_volatility": avg_volatility,
                "volatility_trend": self._analyze_volatility_trend(volatility_by_timeframe)
            }

        except Exception as e:
            logger.error(f"Volatility analysis failed: {e}")
            return {"error": str(e)}

    def _classify_volatility(self, volatility: float) -> str:
        """Classify volatility level"""
        if volatility < 1.0:
            return "low"
        elif volatility < 2.5:
            return "moderate"
        elif volatility < 5.0:
            return "high"
        else:
            return "very_high"

    def _analyze_volatility_trend(self, volatility_by_timeframe: Dict[str, Any]) -> str:
        """Analyze volatility trend across timeframes"""
        try:
            timeframe_order = ['1d', '1w', '1m', '3m', '6m', '1y']
            volatilities = []

            for tf in timeframe_order:
                if tf in volatility_by_timeframe:
                    volatilities.append(volatility_by_timeframe[tf]["volatility"])

            if len(volatilities) < 2:
                return "insufficient_data"

            # Check if volatility is increasing or decreasing
            increasing = sum(1 for i in range(1, len(volatilities)) if volatilities[i] > volatilities[i-1])
            decreasing = sum(1 for i in range(1, len(volatilities)) if volatilities[i] < volatilities[i-1])

            if increasing > decreasing:
                return "increasing"
            elif decreasing > increasing:
                return "decreasing"
            else:
                return "stable"

        except Exception:
            return "unknown"

    async def _calculate_market_correlation(self, multi_timeframe_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate correlation with market indices"""
        try:
            # For now, return placeholder correlation data
            # In a full implementation, this would fetch SPY/market data and calculate correlation
            return {
                "spy_correlation": 0.75,  # Placeholder
                "sector_correlation": 0.65,  # Placeholder
                "correlation_strength": "moderate"
            }

        except Exception as e:
            logger.error(f"Market correlation calculation failed: {e}")
            return {"error": str(e)}

    def _calculate_risk_metrics(self, multi_timeframe_data: Dict[str, Any], current_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate comprehensive risk metrics"""
        try:
            # Get the longest timeframe data for comprehensive analysis
            longest_data = None
            for timeframe in ['1y', '6m', '3m', '1m']:
                if timeframe in multi_timeframe_data and multi_timeframe_data[timeframe]:
                    longest_data = multi_timeframe_data[timeframe]
                    break

            if not longest_data or 'Close' not in longest_data:
                return {"error": "Insufficient data for risk metrics"}

            prices = longest_data['Close']
            if len(prices) < 20:
                return {"error": "Insufficient price history"}

            # Calculate maximum drawdown
            max_drawdown = self._calculate_max_drawdown(prices)

            # Calculate price range metrics
            price_range = (max(prices) - min(prices)) / min(prices) * 100

            # Calculate current price position
            current_price = current_data.get('current_price', prices[-1])
            price_position = (current_price - min(prices)) / (max(prices) - min(prices)) * 100

            return {
                "max_drawdown": max_drawdown,
                "price_range_percent": price_range,
                "current_price_position": price_position,
                "support_distance": self._calculate_support_distance(prices, current_price),
                "resistance_distance": self._calculate_resistance_distance(prices, current_price)
            }

        except Exception as e:
            logger.error(f"Risk metrics calculation failed: {e}")
            return {"error": str(e)}

    def _calculate_max_drawdown(self, prices: List[float]) -> float:
        """Calculate maximum drawdown"""
        try:
            peak = prices[0]
            max_dd = 0

            for price in prices:
                if price > peak:
                    peak = price
                drawdown = (peak - price) / peak * 100
                if drawdown > max_dd:
                    max_dd = drawdown

            return max_dd

        except Exception:
            return 0.0

    def _calculate_support_distance(self, prices: List[float], current_price: float) -> float:
        """Calculate distance to nearest support level"""
        try:
            # Simple support calculation - lowest price in recent period
            recent_low = min(prices[-20:]) if len(prices) >= 20 else min(prices)
            distance = (current_price - recent_low) / current_price * 100
            return distance

        except Exception:
            return 0.0

    def _calculate_resistance_distance(self, prices: List[float], current_price: float) -> float:
        """Calculate distance to nearest resistance level"""
        try:
            # Simple resistance calculation - highest price in recent period
            recent_high = max(prices[-20:]) if len(prices) >= 20 else max(prices)
            distance = (recent_high - current_price) / current_price * 100
            return distance

        except Exception:
            return 0.0

    def _determine_risk_level(self, volatility_analysis: Dict[str, Any], risk_metrics: Dict[str, Any], multi_timeframe_data: Dict[str, Any]) -> str:
        """Determine overall risk level considering data quality"""
        try:
            avg_volatility = volatility_analysis.get("average_volatility", 0)
            max_drawdown = risk_metrics.get("max_drawdown", 0)

            risk_score = 0

            # Data quality risk - insufficient data increases risk
            total_data_points = 0
            for timeframe, data in multi_timeframe_data.items():
                if data and 'data' in data and hasattr(data['data'], 'closes'):
                    total_data_points += len(data['data'].closes)

            avg_data_points = total_data_points / len(multi_timeframe_data) if multi_timeframe_data else 0

            # Insufficient data increases risk
            if avg_data_points < 30:
                risk_score += 2  # High risk due to insufficient data
            elif avg_data_points < 50:
                risk_score += 1  # Moderate risk due to limited data

            # Volatility contribution
            if avg_volatility > 5.0:
                risk_score += 3
            elif avg_volatility > 2.5:
                risk_score += 2
            elif avg_volatility > 1.0:
                risk_score += 1

            # Drawdown contribution
            if max_drawdown > 30:
                risk_score += 3
            elif max_drawdown > 20:
                risk_score += 2
            elif max_drawdown > 10:
                risk_score += 1

            # Determine risk level
            if risk_score >= 6:
                return "very_high"
            elif risk_score >= 4:
                return "high"
            elif risk_score >= 2:
                return "moderate"
            else:
                return "low"

        except Exception:
            return "unknown"

    def _identify_risk_factors(self, volatility_analysis: Dict[str, Any], market_correlation: Dict[str, Any], risk_metrics: Dict[str, Any]) -> List[str]:
        """Identify key risk factors"""
        risk_factors = []

        try:
            # Volatility risks
            avg_volatility = volatility_analysis.get("average_volatility", 0)
            if avg_volatility > 5.0:
                risk_factors.append("Very high volatility detected")
            elif avg_volatility > 2.5:
                risk_factors.append("High volatility present")

            # Drawdown risks
            max_drawdown = risk_metrics.get("max_drawdown", 0)
            if max_drawdown > 30:
                risk_factors.append("Significant historical drawdowns")
            elif max_drawdown > 20:
                risk_factors.append("Moderate historical drawdowns")

            # Position risks
            price_position = risk_metrics.get("current_price_position", 50)
            if price_position > 90:
                risk_factors.append("Price near historical highs")
            elif price_position < 10:
                risk_factors.append("Price near historical lows")

            # Correlation risks
            spy_correlation = market_correlation.get("spy_correlation", 0)
            if spy_correlation > 0.8:
                risk_factors.append("High market correlation")

            if not risk_factors:
                risk_factors.append("No significant risk factors identified")

            return risk_factors

        except Exception as e:
            logger.error(f"Risk factor identification failed: {e}")
            return ["Unable to assess risk factors"]

class PriceTargetStage(BasePipelineStage):
    """Stage for calculating price targets across timeframes"""

    def __init__(self):
        super().__init__("price_targets", {})
        self.required_inputs = ["comprehensive_technical_analysis", "risk_assessment", "current_market_data"]
        self.outputs = ["price_targets"]

    async def process(self, context: PipelineContext) -> StageResult:
        """Calculate price targets based on technical analysis"""
        start_time = time.time()

        try:
            technical_analysis = context.processing_results.get("comprehensive_technical_analysis", {})
            risk_assessment = context.processing_results.get("risk_assessment", {})
            current_data = context.processing_results.get("current_market_data", {})

            current_price = current_data.get('current_price') or current_data.get('price') or current_data.get('close', 0)
            if not current_price:
                raise ValueError("Current price not available")

            # Calculate targets for different timeframes
            price_targets = {}

            timeframe_analysis = technical_analysis.get("timeframe_analysis", {})
            for timeframe, analysis in timeframe_analysis.items():
                if 'error' in analysis:
                    continue

                targets = self._calculate_timeframe_targets(analysis, current_price, timeframe)
                price_targets[timeframe] = targets

            # Calculate consensus targets
            consensus_targets = self._calculate_consensus_targets(price_targets, current_price)

            return StageResult(
                success=True,
                output_data={
                    "price_targets": {
                        "by_timeframe": price_targets,
                        "consensus": consensus_targets,
                        "current_price": current_price,
                        "calculation_method": "technical_analysis_based"
                    }
                },
                execution_time=time.time() - start_time
            )

        except Exception as e:
            logger.error(f"Price target calculation failed: {e}")
            return StageResult(
                success=False,
                output_data={},
                execution_time=time.time() - start_time,
                error_message=f"Price target calculation failed: {str(e)}"
            )

    def _calculate_timeframe_targets(self, analysis: Dict[str, Any], current_price: float, timeframe: str) -> Dict[str, Any]:
        """Calculate price targets for a specific timeframe"""
        try:
            support_resistance = analysis.get("support_resistance", {})
            trend_analysis = analysis.get("trend_analysis", {})

            # Get support and resistance levels
            resistance_levels = support_resistance.get("resistance_levels", [])
            support_levels = support_resistance.get("support_levels", [])

            # Calculate targets based on trend
            trend = trend_analysis.get("trend", "neutral")

            if "bullish" in trend:
                # For bullish trend, target resistance levels
                upside_targets = [r for r in resistance_levels if r > current_price]
                downside_targets = [s for s in support_levels if s < current_price]

                target = upside_targets[0] if upside_targets else current_price * 1.05
                stop_loss = downside_targets[-1] if downside_targets else current_price * 0.95

            elif "bearish" in trend:
                # For bearish trend, target support levels
                downside_targets = [s for s in support_levels if s < current_price]
                upside_targets = [r for r in resistance_levels if r > current_price]

                target = downside_targets[0] if downside_targets else current_price * 0.95
                stop_loss = upside_targets[0] if upside_targets else current_price * 1.05

            else:
                # Neutral trend - use nearest levels
                target = current_price
                stop_loss = current_price * 0.95

            return {
                "target_price": target,
                "stop_loss": stop_loss,
                "upside_potential": (target - current_price) / current_price * 100,
                "downside_risk": (current_price - stop_loss) / current_price * 100,
                "risk_reward_ratio": abs((target - current_price) / (current_price - stop_loss)) if stop_loss != current_price else 1.0
            }

        except Exception as e:
            logger.error(f"Target calculation failed for {timeframe}: {e}")
            return {"error": str(e)}

    def _calculate_consensus_targets(self, price_targets: Dict[str, Any], current_price: float) -> Dict[str, Any]:
        """Calculate consensus price targets across timeframes"""
        try:
            valid_targets = []
            valid_stops = []

            for timeframe, targets in price_targets.items():
                if 'error' not in targets:
                    target_price = targets.get("target_price")
                    stop_loss = targets.get("stop_loss")

                    if target_price and target_price > 0:
                        valid_targets.append(target_price)
                    if stop_loss and stop_loss > 0:
                        valid_stops.append(stop_loss)

            if not valid_targets:
                return {"error": "No valid targets calculated"}

            # Calculate consensus as weighted average (longer timeframes get more weight)
            timeframe_weights = {'1d': 1, '1w': 2, '1m': 3, '3m': 4, '6m': 5, '1y': 6}

            weighted_target = 0
            weighted_stop = 0
            total_weight = 0

            for timeframe, targets in price_targets.items():
                if 'error' not in targets and timeframe in timeframe_weights:
                    weight = timeframe_weights[timeframe]
                    target_price = targets.get("target_price", 0)
                    stop_loss = targets.get("stop_loss", 0)

                    if target_price > 0:
                        weighted_target += target_price * weight
                        total_weight += weight

                    if stop_loss > 0:
                        weighted_stop += stop_loss * weight

            if total_weight > 0:
                consensus_target = weighted_target / total_weight
                consensus_stop = weighted_stop / total_weight
            else:
                consensus_target = sum(valid_targets) / len(valid_targets)
                consensus_stop = sum(valid_stops) / len(valid_stops) if valid_stops else current_price * 0.95

            return {
                "consensus_target": consensus_target,
                "consensus_stop_loss": consensus_stop,
                "upside_potential": (consensus_target - current_price) / current_price * 100,
                "downside_risk": (current_price - consensus_stop) / current_price * 100,
                "confidence": len(valid_targets) / 6.0  # Confidence based on number of valid timeframes
            }

        except Exception as e:
            logger.error(f"Consensus target calculation failed: {e}")
            return {"error": str(e)}

class MultiTimeframeReportStage(BasePipelineStage):
    """Stage for generating comprehensive multi-timeframe report"""

    def __init__(self):
        super().__init__("multi_timeframe_report", {})
        self.required_inputs = ["comprehensive_technical_analysis", "risk_assessment", "price_targets", "current_market_data"]
        self.outputs = ["response"]

    async def process(self, context: PipelineContext) -> StageResult:
        """Generate comprehensive multi-timeframe analysis report"""
        start_time = time.time()

        try:
            technical_analysis = context.processing_results.get("comprehensive_technical_analysis", {})
            risk_assessment = context.processing_results.get("risk_assessment", {})
            price_targets = context.processing_results.get("price_targets", {})
            current_data = context.processing_results.get("current_market_data", {})

            # Generate comprehensive report
            report = self._generate_comprehensive_report(
                context.ticker,
                technical_analysis,
                risk_assessment,
                price_targets,
                current_data
            )

            return StageResult(
                success=True,
                output_data={"response": report},
                execution_time=time.time() - start_time
            )

        except Exception as e:
            logger.error(f"Report generation failed: {e}")
            return StageResult(
                success=False,
                output_data={},
                execution_time=time.time() - start_time,
                error_message=f"Report generation failed: {str(e)}"
            )

    def _generate_comprehensive_report(
        self,
        ticker: str,
        technical_analysis: Dict[str, Any],
        risk_assessment: Dict[str, Any],
        price_targets: Dict[str, Any],
        current_data: Dict[str, Any]
    ) -> str:
        """Generate comprehensive multi-timeframe analysis report"""

        try:
            current_price = current_data.get('current_price') or current_data.get('price') or current_data.get('close', 'N/A')
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S UTC")

            # Start building the report
            report = f"""# 📊 **COMPREHENSIVE MULTI-TIMEFRAME ANALYSIS**
## **{ticker.upper()}** | ${current_price} | {timestamp}

"""

            # Executive Summary
            comprehensive_summary = technical_analysis.get("comprehensive_summary", {})
            overall_trend = comprehensive_summary.get("overall_trend", "unknown")
            confidence = comprehensive_summary.get("analysis_confidence", 0)
            key_insights = comprehensive_summary.get("key_insights", [])

            report += f"""## 🎯 **EXECUTIVE SUMMARY**
**Overall Trend:** {overall_trend.upper().replace('_', ' ')}
**Analysis Confidence:** {confidence:.1%}
**Key Insights:**
"""
            for insight in key_insights[:3]:  # Top 3 insights
                report += f"• {insight}\n"

            # Multi-Timeframe Analysis
            report += f"\n## 📈 **MULTI-TIMEFRAME TECHNICAL ANALYSIS**\n"

            timeframe_analysis = technical_analysis.get("timeframe_analysis", {})
            timeframe_order = ['1d', '1w', '1m', '3m', '6m', '1y']
            timeframe_names = {
                '1d': 'Daily', '1w': 'Weekly', '1m': 'Monthly',
                '3m': '3-Month', '6m': '6-Month', '1y': 'Yearly'
            }

            for timeframe in timeframe_order:
                if timeframe in timeframe_analysis:
                    analysis = timeframe_analysis[timeframe]
                    if 'error' not in analysis:
                        name = timeframe_names.get(timeframe, timeframe)
                        trend = analysis.get('trend_analysis', {}).get('trend', 'unknown')
                        quality = analysis.get('analysis_quality', 'unknown')

                        report += f"**{name} ({timeframe.upper()}):** {trend.replace('_', ' ').title()} | Quality: {quality.title()}\n"

            # Risk Assessment
            report += f"\n## ⚠️ **RISK ASSESSMENT**\n"

            overall_risk = risk_assessment.get("overall_risk_level", "unknown")
            volatility_analysis = risk_assessment.get("volatility_analysis", {})
            risk_factors = risk_assessment.get("risk_factors", [])

            report += f"**Overall Risk Level:** {overall_risk.upper().replace('_', ' ')}\n"

            avg_volatility = volatility_analysis.get("average_volatility", 0)
            if avg_volatility:
                report += f"**Average Volatility:** {avg_volatility:.2f}%\n"

            report += f"**Risk Factors:**\n"
            for factor in risk_factors[:3]:  # Top 3 risk factors
                report += f"• {factor}\n"

            # Price Targets
            report += f"\n## 🎯 **PRICE TARGETS & PROJECTIONS**\n"

            consensus = price_targets.get("consensus", {})
            if consensus and 'error' not in consensus:
                consensus_target = consensus.get("consensus_target", 0)
                consensus_stop = consensus.get("consensus_stop_loss", 0)
                upside_potential = consensus.get("upside_potential", 0)
                downside_risk = consensus.get("downside_risk", 0)
                target_confidence = consensus.get("confidence", 0)

                if consensus_target:
                    report += f"**Consensus Target:** ${consensus_target:.2f} ({upside_potential:+.1f}%)\n"
                if consensus_stop:
                    report += f"**Stop Loss Level:** ${consensus_stop:.2f} ({-downside_risk:.1f}%)\n"
                report += f"**Target Confidence:** {target_confidence:.1%}\n"

            # Detailed Timeframe Breakdown
            report += f"\n## 📊 **DETAILED TIMEFRAME BREAKDOWN**\n"

            for timeframe in timeframe_order:
                if timeframe in timeframe_analysis:
                    analysis = timeframe_analysis[timeframe]
                    if 'error' not in analysis:
                        name = timeframe_names.get(timeframe, timeframe)

                        report += f"\n### **{name} Analysis ({timeframe.upper()})**\n"

                        # Technical indicators
                        indicators = analysis.get('technical_indicators', {})
                        if indicators:
                            rsi = indicators.get('RSI', [])
                            if rsi:
                                report += f"• **RSI:** {rsi[-1]:.1f}\n"

                        # Trend analysis
                        trend_analysis_detail = analysis.get('trend_analysis', {})
                        if trend_analysis_detail:
                            trend = trend_analysis_detail.get('trend', 'unknown')
                            trend_strength = trend_analysis_detail.get('trend_strength', 0)
                            report += f"• **Trend:** {trend.replace('_', ' ').title()} (Strength: {trend_strength:.1%})\n"

                        # Support/Resistance
                        sr = analysis.get('support_resistance', {})
                        if sr:
                            resistance = sr.get('resistance_levels', [])
                            support = sr.get('support_levels', [])
                            if resistance:
                                report += f"• **Next Resistance:** ${resistance[0]:.2f}\n"
                            if support:
                                report += f"• **Next Support:** ${support[-1]:.2f}\n"

            # Footer
            report += f"\n---\n"
            report += f"*Analysis generated at {timestamp}*\n"
            report += f"*This comprehensive analysis covers {len([tf for tf in timeframe_analysis.values() if 'error' not in tf])}/6 timeframes*\n"

            return report

        except Exception as e:
            logger.error(f"Report generation failed: {e}")
            return f"❌ **ANALYSIS ERROR FOR {ticker.upper()}**\n\nFailed to generate comprehensive report: {str(e)}\n\n*Please try again later.*"

def create_multi_timeframe_pipeline() -> ParallelPipelineEngine:
    """Create the multi-timeframe analysis pipeline"""

    builder = FlexiblePipelineBuilder("multi_timeframe_analysis")

    # Add stages
    builder.add_stage(MultiTimeframeDataFetchStage())
    builder.add_stage(ComprehensiveTechnicalAnalysisStage())
    builder.add_stage(RiskAssessmentStage())
    builder.add_stage(PriceTargetStage())
    builder.add_stage(MultiTimeframeReportStage())

    # Build and return pipeline
    return builder.build()

async def execute_multi_timeframe_pipeline(
    ticker: str,
    analysis_type: str = "comprehensive_multi_timeframe",
    timeframes: List[str] = None,
    include_all_indicators: bool = True,
    include_support_resistance: bool = True,
    include_volume_analysis: bool = True,
    include_risk_assessment: bool = True,
    include_price_targets: bool = True,
    user_id: Optional[str] = None,
    guild_id: Optional[str] = None,
    correlation_id: Optional[str] = None,
    strict_mode: bool = False
) -> PipelineContext:
    """Execute the multi-timeframe analysis pipeline"""

    # Create context
    context = PipelineContext()
    context.ticker = ticker.upper()
    context.analysis_type = analysis_type
    context.timeframes = timeframes or ['1d', '1w', '1m', '3m', '6m', '1y']
    context.include_all_indicators = include_all_indicators
    context.include_support_resistance = include_support_resistance
    context.include_volume_analysis = include_volume_analysis
    context.include_risk_assessment = include_risk_assessment
    context.include_price_targets = include_price_targets
    context.user_id = user_id
    context.guild_id = guild_id
    context.correlation_id = correlation_id
    context.strict_mode = strict_mode

    # Create and execute pipeline
    pipeline = create_multi_timeframe_pipeline()

    try:
        # Execute pipeline
        context = await pipeline.execute(context)

        # Log completion
        if context.status == PipelineStatus.COMPLETED:
            logger.info(f"Multi-timeframe analysis pipeline completed successfully for {ticker}")
        else:
            logger.warning(f"Multi-timeframe analysis pipeline completed with status {context.status} for {ticker}")

    except Exception as e:
        logger.error(f"Multi-timeframe analysis pipeline failed for {ticker}: {e}")
        context.status = PipelineStatus.FAILED
        context.error_log.append({
            "error_message": str(e),
            "error_type": type(e).__name__,
            "stage": "pipeline_execution"
        })

        # Generate error response
        error_response = f"""❌ **COMPREHENSIVE ANALYSIS ERROR FOR {ticker.upper()}**

Error: {str(e)}

*Please try again later or contact support if the issue persists.*
*This analysis is for informational purposes only and does not constitute investment advice.*
"""
        context.processing_results["response"] = error_response

    return context


def _convert_to_dataframe(data):
    """Convert data to DataFrame format expected by TechnicalAnalysisCalculator"""
    import pandas as pd

    if isinstance(data, dict) and 'data' in data:
        hist_data = data['data']
        if hasattr(hist_data, 'closes'):
            # Convert HistoricalData object to DataFrame
            df = pd.DataFrame({
                'open': hist_data.opens,
                'high': hist_data.highs,
                'low': hist_data.lows,
                'close': hist_data.closes,
                'volume': hist_data.volumes
            })
            df.index = pd.to_datetime(hist_data.dates)
            return df

    # Fallback: return empty DataFrame with required columns
    return pd.DataFrame(columns=['open', 'high', 'low', 'close', 'volume'])
