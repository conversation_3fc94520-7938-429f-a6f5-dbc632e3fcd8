"""WebSocket Handler for Real-Time Market Data Streaming

Implements Polygon.io WebSocket client for live stock data (quotes, trades).
Uses asyncio and websockets library for async connection management.
Integrates with existing pipeline by providing update callbacks.

Dependencies: pip install websockets (add to requirements.txt later).
"""

import asyncio
import json
import logging
from typing import Callable, Dict, List, Optional
from datetime import datetime

from src.shared.error_handling.logging import get_trading_logger

logger = get_trading_logger(__name__)

class PolygonWebSocketClient:
    """
    Async WebSocket client for Polygon.io real-time data.
    
    Handles connection, subscription to tickers, message parsing,
    and reconnection with exponential backoff.
    
    Usage:
        client = PolygonWebSocketClient(api_key="your_key")
        client.on_update = lambda data: process_update(data)
        await client.connect()
        await client.subscribe(["AAPL", "GOOG"])
    """
    
    def __init__(self, api_key: str, max_retries: int = 5, base_delay: float = 1.0):
        self.api_key = api_key
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.ws: Optional[asyncio.Queue] = None  # For internal message queue
        self.subscriptions: set = set()
        self.is_connected = False
        self.on_update: Optional[Callable[[Dict], None]] = None  # Callback for parsed updates
        self.on_error: Optional[Callable[[str], None]] = None
        self.on_disconnect: Optional[Callable[[], None]] = None
        
    async def connect(self):
        """Establish WebSocket connection to Polygon.io."""
        uri = f"wss://socket.polygon.io/stocks"
        retry_count = 0
        
        while retry_count < self.max_retries:
            try:
                logger.info("Connecting to Polygon.io WebSocket...")
                self.ws = await asyncio.wait_for(
                    asyncio.open_connection(uri.replace("wss://", "ws://")),  # Use ws for local testing if needed
                    timeout=10.0
                )
                # Authenticate
                auth_msg = json.dumps({"action": "auth", "params": self.api_key})
                self.ws.send(auth_msg)
                auth_response = await self.ws.recv()
                if "auth_success" not in auth_response:
                    raise ValueError(f"Auth failed: {auth_response}")
                
                self.is_connected = True
                logger.info("Polygon.io WebSocket connected and authenticated.")
                
                # Start listener task
                asyncio.create_task(self._listen_for_messages())
                return
                
            except Exception as e:
                retry_count += 1
                delay = self.base_delay * (2 ** (retry_count - 1))
                logger.warning(f"Connection attempt {retry_count} failed: {e}. Retrying in {delay}s...")
                await asyncio.sleep(delay)
        
        logger.error("Max retries exceeded. WebSocket connection failed.")
        if self.on_error:
            self.on_error("Failed to connect after max retries")
    
    async def _listen_for_messages(self):
        """Internal loop to receive and process WebSocket messages."""
        try:
            while self.is_connected:
                message = await asyncio.wait_for(self.ws.recv(), timeout=30.0)
                data = json.loads(message)
                await self._process_message(data)
        except asyncio.TimeoutError:
            logger.warning("WebSocket timeout. Checking connection...")
            self.is_connected = False
        except Exception as e:
            logger.error(f"WebSocket listen error: {e}")
            self.is_connected = False
            if self.on_error:
                self.on_error(str(e))
            await self._handle_disconnect()
    
    async def _process_message(self, data: Dict):
        """Parse incoming message and trigger update callback."""
        if data.get("ev") == "status" and data.get("status") == "success":
            logger.debug("WebSocket status update received.")
            return
        
        # Parse trade/quote data
        if data.get("ev") in ["T", "Q"]:  # T=trade, Q=quote
            ticker = data.get("sym")
            if ticker in self.subscriptions:
                # Normalize to pipeline format
                parsed = {
                    "symbol": ticker,
                    "price": float(data.get("p", 0)),
                    "size": data.get("s", 0),
                    "timestamp": datetime.fromtimestamp(data.get("t", 0) / 1000).isoformat(),
                    "type": data.get("ev"),  # 'T' or 'Q'
                    "provider": "polygon_live"
                }
                logger.debug(f"Live update for {ticker}: {parsed['price']}")
                if self.on_update:
                    self.on_update(parsed)
    
    async def subscribe(self, tickers: List[str]):
        """Subscribe to real-time updates for given tickers."""
        if not self.is_connected:
            raise RuntimeError("Not connected. Call connect() first.")
        
        for ticker in tickers:
            if ticker not in self.subscriptions:
                sub_msg = json.dumps({
                    "action": "subscribe",
                    "params": f"T.{ticker}"  # Subscribe to trades; use 'Q.{ticker}' for quotes
                })
                self.ws.send(sub_msg)
                self.subscriptions.add(ticker)
                logger.info(f"Subscribed to live trades for {ticker}")
    
    async def unsubscribe(self, tickers: List[str]):
        """Unsubscribe from tickers."""
        for ticker in tickers:
            if ticker in self.subscriptions:
                unsub_msg = json.dumps({
                    "action": "unsubscribe",
                    "params": f"T.{ticker}"
                })
                self.ws.send(unsub_msg)
                self.subscriptions.discard(ticker)
                logger.info(f"Unsubscribed from {ticker}")
    
    async def _handle_disconnect(self):
        """Handle disconnection and trigger callback."""
        self.is_connected = False
        if self.on_disconnect:
            self.on_disconnect()
        logger.info("WebSocket disconnected. Attempting reconnect...")
        await self.connect()  # Auto-reconnect
    
    async def close(self):
        """Gracefully close the connection."""
        if self.is_connected:
            for ticker in list(self.subscriptions):
                await self.unsubscribe([ticker])
            self.ws.close()
            await self.ws.wait_closed()
            self.is_connected = False
            logger.info("WebSocket closed.")


# Example usage (for testing)
async def example_usage():
    client = PolygonWebSocketClient(api_key="demo")  # Use demo key for testing
    client.on_update = lambda data: print(f"Update: {data}")
    
    await client.connect()
    await client.subscribe(["AAPL"])
    await asyncio.sleep(60)  # Listen for 1 min
    await client.close()

if __name__ == "__main__":
    asyncio.run(example_usage())