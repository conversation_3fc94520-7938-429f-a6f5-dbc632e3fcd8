"""
Response Formatter for AI Chat Processor
Handles formatting of AI responses and data integration
"""

import logging
import re
from typing import Dict, Any, List, Optional
from datetime import datetime

from src.shared.error_handling.logging import get_logger
logger = get_logger(__name__)

class ResponseFormatter:
    """Handles formatting of AI responses with data integration"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def format(self, ai_response: str, data: Dict[str, Any], tool_results: Dict[str, Any]) -> str:
        """
        Format the final response by integrating AI response with data and tool results.
        
        Args:
            ai_response: The base AI response
            data: Market data dictionary
            tool_results: Results from tool execution
            
        Returns:
            Formatted response string
        """
        if not ai_response:
            return self._generate_fallback_response(data, tool_results)
        
        # Start with the AI response
        formatted_response = ai_response
        
        # Re-enable defensive validation to prevent price/data hallucinations
        try:
            if data:
                # First, validate prices and data binding against provided market data
                symbol = data.get('symbol') or data.get('SYMBOL') or 'SYMBOL'
                formatted_response = self._validate_price_accuracy(formatted_response, {symbol: data})
                indicators = data.get('indicators')
                current_price = data.get('current_price') or data.get('price') or 0
                if isinstance(indicators, dict) and current_price:
                    formatted_response = self._validate_data_binding(
                        formatted_response, symbol, indicators, float(current_price)
                    )
        except Exception as e:
            self.logger.warning(f"Validation step skipped due to error: {e}")

        # Add data integration if available
        if data:
            formatted_response = self._integrate_market_data(formatted_response, data)

        # Add tool results if available
        if tool_results:
            formatted_response = self._integrate_tool_results(formatted_response, tool_results)
        
        # Add disclaimer
        formatted_response = self._add_disclaimer(formatted_response)
        
        return formatted_response
    
    def _integrate_market_data(self, response: str, data: Dict[str, Any]) -> str:
        """Integrate market data into the response"""
        if not data:
            return response
        
        # Extract key data points
        symbol = data.get('symbol', 'Unknown')
        current_price = data.get('current_price', 0)
        change_percent = data.get('change_percent', 0)
        volume = data.get('volume', 0)
        
        # Format price data
        from src.core.formatting.text_formatting import format_price
        price_info = f"**{symbol}**: {format_price(current_price)}"
        if change_percent != 0:
            change_symbol = "📈" if change_percent > 0 else "📉"
            price_info += f" ({change_symbol} {change_percent:+.2f}%)"
        
        if volume > 0:
            price_info += f" | Volume: {volume:,}"
        
        # Insert price info after the first sentence
        sentences = response.split('. ')
        if len(sentences) > 1:
            sentences[0] += f". {price_info}"
            response = '. '.join(sentences)
        else:
            response = f"{price_info}\n\n{response}"
        
        return response
    
    def _integrate_tool_results(self, response: str, tool_results: Dict[str, Any]) -> str:
        """Integrate tool execution results into the response"""
        if not tool_results:
            return response
        
        # Add tool results section
        tool_section = "\n\n**Analysis Results:**\n"
        
        for tool_name, result in tool_results.items():
            if isinstance(result, dict) and result.get('success', False):
                tool_section += f"• {tool_name}: {result.get('message', 'Completed')}\n"
            elif isinstance(result, str):
                tool_section += f"• {tool_name}: {result}\n"
        
        return response + tool_section
    
    def _add_disclaimer(self, response: str) -> str:
        """Add standard disclaimer to the response"""
        disclaimer = (
            "\n\n---\n"
            "*This analysis is for informational purposes only and does not constitute "
            "financial advice. Always do your own research before making investment decisions.*"
        )
        return response + disclaimer
    
    def _generate_fallback_response(self, data: Dict[str, Any], tool_results: Dict[str, Any]) -> str:
        """Generate a fallback response when AI response is empty"""
        if data:
            symbol = data.get('symbol', 'the stock')
            current_price = data.get('current_price', 0)
            change_percent = data.get('change_percent', 0)
            
            response = f"I can see that {symbol} is currently trading at ${current_price:.2f}"
            if change_percent != 0:
                change_symbol = "up" if change_percent > 0 else "down"
                response += f", {change_symbol} {abs(change_percent):.2f}%"
            response += "."
        else:
            response = "I'm sorry, but I'm unable to provide a detailed analysis at this time."
        
        return self._add_disclaimer(response)
    
    def format_enhanced_response(self, base_response: str, analysis_data: Dict[str, Any]) -> str:
        """
        Format an enhanced response with additional analysis data.
        
        Args:
            base_response: The base AI response
            analysis_data: Additional analysis data to integrate
            
        Returns:
            Enhanced formatted response
        """
        if not analysis_data:
            return base_response
        
        # Add technical analysis section
        if 'technical_indicators' in analysis_data:
            tech_section = self._format_technical_indicators(analysis_data['technical_indicators'])
            base_response += f"\n\n**Technical Analysis:**\n{tech_section}"
        
        # Add signals section
        if 'signals' in analysis_data:
            signals_section = self._format_signals(analysis_data['signals'])
            base_response += f"\n\n**Market Signals:**\n{signals_section}"
        
        return base_response
    
    def _format_technical_indicators(self, indicators: List[Dict[str, Any]]) -> str:
        """Format technical indicators for display"""
        if not indicators:
            return "No technical indicators available."
        
        formatted = []
        for indicator in indicators:
            name = indicator.get('name', 'Unknown')
            value = indicator.get('value', 0)
            signal = indicator.get('signal', 'neutral')
            
            signal_emoji = {
                'bullish': '🟢',
                'bearish': '🔴',
                'neutral': '🟡'
            }.get(signal, '⚪')
            
            formatted.append(f"• {name}: {value:.2f} {signal_emoji}")
        
        return '\n'.join(formatted)
    
    def _format_signals(self, signals: List[Dict[str, Any]]) -> str:
        """Format market signals for display"""
        if not signals:
            return "No signals detected."
        
        formatted = []
        for signal in signals:
            signal_type = signal.get('type', 'Unknown')
            strength = signal.get('strength', 'medium')
            direction = signal.get('direction', 'neutral')
            
            strength_emoji = {
                'strong': '🔥',
                'medium': '⚡',
                'weak': '💡'
            }.get(strength, '⚪')
            
            direction_emoji = {
                'bullish': '📈',
                'bearish': '📉',
                'neutral': '➡️'
            }.get(direction, '⚪')
            
            formatted.append(f"• {signal_type}: {strength_emoji} {direction_emoji}")
        
        return '\n'.join(formatted)
    
    def _validate_price_accuracy(self, response: str, data: Dict[str, Any]) -> str:
        """
        Validate that AI response price levels match the provided market data.
        This prevents AI hallucination of incorrect price levels.
        """
        if not data:
            return response
        
        # Extract current prices and calculated indicators from data
        current_prices = {}
        calculated_indicators = {}
        
        for symbol, symbol_data in data.items():
            if isinstance(symbol_data, dict):
                # Get current price
                if 'current_price' in symbol_data:
                    current_prices[symbol] = symbol_data['current_price']
                elif 'price' in symbol_data:
                    current_prices[symbol] = symbol_data['price']
                
                # Get calculated indicators
                if 'indicators' in symbol_data and isinstance(symbol_data['indicators'], dict):
                    calculated_indicators[symbol] = symbol_data['indicators']
            elif isinstance(symbol_data, list) and len(symbol_data) > 0:
                # Try to get latest price from historical data
                latest_data = symbol_data[-1]
                if isinstance(latest_data, dict) and 'close' in latest_data:
                    current_prices[symbol] = latest_data['close']
        
        if not current_prices:
            return response
        
        # Check for price hallucination and data binding violations
        for symbol, current_price in current_prices.items():
            if not isinstance(current_price, (int, float)) or current_price <= 0:
                continue
            
            # Check if AI is using calculated indicators instead of generating its own
            if symbol in calculated_indicators:
                response = self._validate_data_binding(response, symbol, calculated_indicators[symbol], current_price)
            
            # First fix price ranges (e.g., $875-885)
            response = self._fix_price_ranges(response, current_price)
                
            # Find all individual price mentions in the response
            price_patterns = [
                r'\$(\d+(?:\.\d{1,2})?)',  # $123.45 format
                r'(\d+(?:\.\d{1,2})?)\s*\$',  # 123.45$ format
            ]
            
            for pattern in price_patterns:
                matches = re.findall(pattern, response, re.IGNORECASE)
                for match in matches:
                    try:
                        mentioned_price = float(match)
                        if mentioned_price > 0:
                            # Check if price is significantly different from current price
                            ratio = max(mentioned_price / current_price, current_price / mentioned_price)
                            if ratio > 2.0:  # More than 2x different
                                self.logger.warning(
                                    f"🚨 Price hallucination detected: AI mentioned ${mentioned_price:.2f} "
                                    f"but {symbol} current price is ${current_price:.2f} (ratio: {ratio:.2f}x)"
                                )
                                # Replace the hallucinated price with a realistic one
                                realistic_price = self._calculate_realistic_price(mentioned_price, current_price)
                                # Replace both $123.45 and 123.45$ formats
                                response = response.replace(f"${mentioned_price:.2f}", f"${realistic_price:.2f}")
                                response = response.replace(f"{mentioned_price:.2f}$", f"{realistic_price:.2f}$")
                                # Also replace without decimal places for whole numbers
                                if mentioned_price == int(mentioned_price):
                                    response = response.replace(f"${int(mentioned_price)}", f"${int(realistic_price)}")
                                    response = response.replace(f"{int(mentioned_price)}$", f"{int(realistic_price)}$")
                    except ValueError:
                        continue
        
        return response
    
    def _calculate_realistic_price(self, hallucinated_price: float, current_price: float) -> float:
        """
        Calculate a realistic price level based on current price instead of hallucinated price.
        """
        # If the hallucinated price is much higher, assume it should be a resistance level
        if hallucinated_price > current_price * 1.5:
            return round(current_price * 1.05, 2)  # 5% above current price
        # If the hallucinated price is much lower, assume it should be a support level
        elif hallucinated_price < current_price * 0.5:
            return round(current_price * 0.95, 2)  # 5% below current price
        # If it's in a reasonable range, keep it
        else:
            return round(hallucinated_price, 2)
    
    def _fix_price_ranges(self, response: str, current_price: float) -> str:
        """
        Fix price ranges that contain hallucinated prices.
        """
        # Pattern to match price ranges like $875-885, $950-970, etc.
        range_pattern = r'\$(\d+(?:\.\d{1,2})?)-(\d+(?:\.\d{1,2})?)'
        
        def replace_range(match):
            start_price = float(match.group(1))
            end_price = float(match.group(2))
            
            # Check if both prices are hallucinated
            start_ratio = max(start_price / current_price, current_price / start_price) if start_price > 0 else 0
            end_ratio = max(end_price / current_price, current_price / end_price) if end_price > 0 else 0
            
            if start_ratio > 2.0 or end_ratio > 2.0:
                # Both prices are hallucinated, create realistic range
                if start_price > current_price * 1.5:  # Resistance range
                    new_start = round(current_price * 1.02, 2)
                    new_end = round(current_price * 1.08, 2)
                else:  # Support range
                    new_start = round(current_price * 0.92, 2)
                    new_end = round(current_price * 0.98, 2)
                
                return f"${new_start}-{new_end}"
            else:
                return match.group(0)  # Keep original if reasonable
        
        return re.sub(range_pattern, replace_range, response)
    
    def _validate_data_binding(self, response: str, symbol: str, calculated_indicators: Dict[str, Any], current_price: float) -> str:
        """
        Validate that AI is using calculated indicators instead of generating its own.
        This ensures data binding integrity.
        """
        violations = []
        
        # Check if AI mentions support/resistance levels that don't match calculated ones
        if 'support_level' in calculated_indicators and calculated_indicators['support_level']:
            calc_support = calculated_indicators['support_level']
            # Look for support mentions that don't match calculated value
            support_pattern = r'support.*?\$(\d+(?:\.\d{1,2})?)'
            matches = re.findall(support_pattern, response, re.IGNORECASE)
            for match in matches:
                mentioned_support = float(match)
                if abs(mentioned_support - calc_support) > 0.01:  # More than 1 cent difference
                    violations.append(f"AI mentioned support ${mentioned_support:.2f} but calculated value is ${calc_support:.2f}")
                    # Replace with calculated value - be more specific
                    response = re.sub(
                        rf'support.*?\${mentioned_support:.2f}',
                        f'support at ${calc_support:.2f}',
                        response,
                        flags=re.IGNORECASE
                    )
        
        if 'resistance_level' in calculated_indicators and calculated_indicators['resistance_level']:
            calc_resistance = calculated_indicators['resistance_level']
            # Look for resistance mentions that don't match calculated value
            resistance_pattern = r'resistance.*?\$(\d+(?:\.\d{1,2})?)'
            matches = re.findall(resistance_pattern, response, re.IGNORECASE)
            for match in matches:
                mentioned_resistance = float(match)
                if abs(mentioned_resistance - calc_resistance) > 0.01:  # More than 1 cent difference
                    violations.append(f"AI mentioned resistance ${mentioned_resistance:.2f} but calculated value is ${calc_resistance:.2f}")
                    # Replace with calculated value - be more specific
                    response = re.sub(
                        rf'resistance.*?\${mentioned_resistance:.2f}',
                        f'resistance at ${calc_resistance:.2f}',
                        response,
                        flags=re.IGNORECASE
                    )
        
        # Check if AI mentions RSI values that don't match calculated ones
        if 'rsi' in calculated_indicators and calculated_indicators['rsi']:
            calc_rsi = calculated_indicators['rsi']
            rsi_pattern = r'RSI.*?(\d+(?:\.\d{1,2})?)'
            matches = re.findall(rsi_pattern, response, re.IGNORECASE)
            for match in matches:
                mentioned_rsi = float(match)
                if abs(mentioned_rsi - calc_rsi) > 0.1:  # More than 0.1 difference
                    violations.append(f"AI mentioned RSI {mentioned_rsi:.1f} but calculated value is {calc_rsi:.1f}")
                    # Replace with calculated value - be more specific
                    response = re.sub(
                        rf'RSI.*?{mentioned_rsi:.1f}',
                        f'RSI is at {calc_rsi:.1f}',
                        response,
                        flags=re.IGNORECASE
                    )
        
        # Log violations for monitoring
        if violations:
            self.logger.warning(f"🚨 Data binding violations detected for {symbol}:")
            for violation in violations:
                self.logger.warning(f"  - {violation}")
        
        return response
