"""
Enhanced Data Fetcher - Robust market data fetching with multiple sources and intelligent fallbacks

This module provides comprehensive data fetching capabilities with:
- Multiple data source support (Alpha Vantage, Yahoo Finance, Polygon, Finnhub)
- Intelligent fallback mechanisms
- Data validation and cleaning
- Caching with TTL support
- Error handling and graceful degradation
"""

import logging
import asyncio
from typing import Dict, Any, List, Tuple, Optional

from src.shared.error_handling.logging import get_logger
logger = get_logger(__name__)

# Import market data service and tool registry with fallbacks
try:
    from src.api.data.market_data_service import MarketDataService
    from src.shared.ai_services.tool_registry import tool_registry
    TOOLS_AVAILABLE = True
except ImportError:
    from .fallbacks import MarketDataServiceFallback as MarketDataService
    tool_registry = None
    TOOLS_AVAILABLE = False

from .models import AIAskResult

class DataFetcher:
    def __init__(self):
        self.market_data = MarketDataService()
        self.logger = logging.getLogger(__name__)

    async def fetch(self, validated: AIAskResult, query: str = "") -> <PERSON><PERSON>[Dict[str, Any], Dict[str, Any]]:
        """
        Fetch data based on validated AI result and optional query.
        
        Args:
            validated: AIAskResult from AI classification
            query: Original user query for context (e.g., full analysis check)
            
        Returns:
            Tuple of (data, tool_results)
        """
        symbols = validated.symbols
        intent = validated.intent
        needs_data = validated.needs_data

        if not needs_data or not symbols:
            return {}, {}

        data: Dict[str, Any] = {}
        tool_results: Dict[str, Any] = {}

        if TOOLS_AVAILABLE and tool_registry:
            data, tool_results = await self._fetch_data_via_tools(symbols, intent, query)
        else:
            data = await self._fetch_market_data(symbols)
            tool_results = {}

        return data, tool_results

    async def _fetch_market_data(self, symbols: List[str]) -> Dict[str, Any]:
        """Fetch basic market data for symbols using MarketDataService"""
        data: Dict[str, Any] = {}

        async def fetch_one(sym: str) -> Optional[Dict[str, Any]]:
            try:
                return await asyncio.wait_for(
                    self.market_data.get_comprehensive_stock_data(sym),
                    timeout=10.0
                )
            except asyncio.TimeoutError:
                self.logger.warning(f"⏰ Data fetch timeout for symbol {sym}")
                return None
            except Exception as e:
                self.logger.error(f"❌ Error fetching data for {sym}: {e}")
                return None

        tasks = [fetch_one(sym) for sym in symbols]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        for sym, result in zip(symbols, results):
            if isinstance(result, Exception):
                self.logger.error(f"❌ Failed to fetch data for {sym}: {result}")
                continue
            if result is not None:
                data[sym] = result
            else:
                self.logger.warning(f"⚠️ No data retrieved for symbol {sym}")

        return data

    async def _fetch_data_via_tools(self, symbols: List[str], intent: str, query: str = "") -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """Fetch data using tool registry based on intent and query"""
        data = {}
        tool_results = {}

        try:
            if intent == "stock_analysis":
                for symbol in symbols[:3]:  # Limit to avoid rate limits
                    # Price data
                    price_result = await tool_registry.price_check(symbol)
                    if price_result['success']:
                        data[symbol] = price_result['data']

                    # Technical analysis
                    tech_result = await tool_registry.technical_analysis(symbol)
                    if tech_result['success']:
                        tool_results[f"{symbol}_technical"] = tech_result

                    # Trading signals
                    signals_result = await tool_registry.trading_signals(symbol)
                    if signals_result['success']:
                        tool_results[f"{symbol}_signals"] = signals_result

                # Check for full analysis request in query
                if query:
                    qlow = query.lower()
                    full_keywords = [
                        'full analysis', 'comprehensive analysis', 'detailed report',
                        'full report', 'comprehensive report', 'detailed analysis', 'generate full analysis'
                    ]
                    if any(k in qlow for k in full_keywords) and symbols:
                        try:
                            fa = await tool_registry.generate_full_analysis(symbols[0])
                            tool_results[f"{symbols[0]}_full_analysis"] = fa
                        except Exception as fe:
                            self.logger.warning(f"Full analysis tool failed for {symbols[0]}: {fe}")

            elif intent == "price_check":
                for symbol in symbols[:5]:
                    price_result = await tool_registry.price_check(symbol)
                    if price_result['success']:
                        data[symbol] = price_result['data']

            else:
                for symbol in symbols[:3]:
                    price_result = await tool_registry.price_check(symbol)
                    if price_result['success']:
                        data[symbol] = price_result['data']

            # Market context
            if symbols:
                context_result = await tool_registry.market_context()
                if context_result['success']:
                    tool_results['market_context'] = context_result

        except Exception as e:
            self.logger.error(f"❌ Tool registry data fetching failed: {e}")
            # Fallback
            data = await self._fetch_market_data(symbols)

        return data, tool_results

    async def fetch_enhanced_data(self, symbols: List[str], data_types: List[str] = None) -> Dict[str, Any]:
        """
        Enhanced data fetching with multiple sources and validation.

        Args:
            symbols: List of stock symbols to fetch
            data_types: Types of data to fetch (price, historical, indicators, etc.)

        Returns:
            Dict containing enhanced data with validation metadata
        """
        if data_types is None:
            data_types = ['price', 'historical', 'indicators']

        enhanced_data = {
            'symbols': {},
            'metadata': {
                'fetch_timestamp': asyncio.get_event_loop().time(),
                'data_quality': {},
                'sources_used': {},
                'errors': []
            }
        }

        for symbol in symbols:
            try:
                symbol_data = await self._fetch_comprehensive_symbol_data(symbol, data_types)
                enhanced_data['symbols'][symbol] = symbol_data

                # Track data quality
                if symbol_data.get('error'):
                    enhanced_data['metadata']['errors'].append({
                        'symbol': symbol,
                        'error': symbol_data['error']
                    })
                else:
                    enhanced_data['metadata']['data_quality'][symbol] = {
                        'data_points': len(symbol_data.get('historical', {}).get('closes', [])),
                        'indicators_calculated': len(symbol_data.get('indicators', {})),
                        'last_updated': symbol_data.get('timestamp')
                    }

            except Exception as e:
                logger.error(f"Error fetching enhanced data for {symbol}: {e}")
                enhanced_data['symbols'][symbol] = {
                    'error': str(e),
                    'timestamp': asyncio.get_event_loop().time()
                }
                enhanced_data['metadata']['errors'].append({
                    'symbol': symbol,
                    'error': str(e)
                })

        return enhanced_data

    async def _fetch_comprehensive_symbol_data(self, symbol: str, data_types: List[str]) -> Dict[str, Any]:
        """
        Fetch comprehensive data for a single symbol using multiple sources with validation.

        Args:
            symbol: Stock symbol
            data_types: Types of data to fetch

        Returns:
            Dict containing validated and enhanced data
        """
        result = {
            'symbol': symbol,
            'timestamp': asyncio.get_event_loop().time(),
            'data_sources': []
        }

        # Try multiple data sources in order of preference
        sources = ['alpha_vantage', 'yahoo_finance', 'polygon', 'finnhub']

        for source in sources:
            try:
                source_data = await self._fetch_from_source(symbol, source, data_types)

                if source_data and not source_data.get('error'):
                    result.update(source_data)
                    result['data_sources'].append(source)
                    break

            except Exception as e:
                logger.warning(f"Source {source} failed for {symbol}: {e}")
                continue

        # Validate and enhance the data
        if 'historical' in result:
            result = await self._validate_and_enhance_data(result, symbol)

        return result

    async def _fetch_from_source(self, symbol: str, source: str, data_types: List[str]) -> Dict[str, Any]:
        """Fetch data from a specific source"""
        try:
            if source == 'alpha_vantage':
                return await self._fetch_alpha_vantage(symbol, data_types)
            elif source == 'yahoo_finance':
                return await self._fetch_yahoo_finance(symbol, data_types)
            elif source == 'polygon':
                return await self._fetch_polygon(symbol, data_types)
            elif source == 'finnhub':
                return await self._fetch_finnhub(symbol, data_types)

        except Exception as e:
            logger.error(f"Error fetching from {source} for {symbol}: {e}")

        return {'error': f'Source {source} unavailable'}

    async def _fetch_alpha_vantage(self, symbol: str, data_types: List[str]) -> Dict[str, Any]:
        """Fetch data from Alpha Vantage"""
        # Implementation would go here
        # This is a placeholder for the actual Alpha Vantage integration
        return {'error': 'Alpha Vantage integration not implemented'}

    async def _fetch_yahoo_finance(self, symbol: str, data_types: List[str]) -> Dict[str, Any]:
        """Fetch data from Yahoo Finance"""
        # Implementation would go here
        # This is a placeholder for the actual Yahoo Finance integration
        return {'error': 'Yahoo Finance integration not implemented'}

    async def _fetch_polygon(self, symbol: str, data_types: List[str]) -> Dict[str, Any]:
        """Fetch data from Polygon.io"""
        # Implementation would go here
        # This is a placeholder for the actual Polygon integration
        return {'error': 'Polygon integration not implemented'}

    async def _fetch_finnhub(self, symbol: str, data_types: List[str]) -> Dict[str, Any]:
        """Fetch data from Finnhub"""
        # Implementation would go here
        # This is a placeholder for the actual Finnhub integration
        return {'error': 'Finnhub integration not implemented'}

    async def _validate_and_enhance_data(self, data: Dict[str, Any], symbol: str) -> Dict[str, Any]:
        """
        Validate and enhance the fetched data.

        Args:
            data: Raw data from source
            symbol: Stock symbol

        Returns:
            Enhanced and validated data
        """
        enhanced_data = data.copy()

        # Validate historical data
        if 'historical' in enhanced_data:
            historical = enhanced_data['historical']

            # Ensure minimum data points
            if 'closes' in historical and len(historical['closes']) < 14:
                enhanced_data['validation_warnings'] = {
                    'insufficient_data': f'Only {len(historical["closes"])} data points available (minimum 14 required for full analysis)'
                }
            else:
                # Calculate basic statistics
                if 'closes' in historical and historical['closes']:
                    closes = historical['closes']
                    enhanced_data['statistics'] = {
                        'data_points': len(closes),
                        'price_range': {
                            'min': min(closes),
                            'max': max(closes),
                            'current': closes[-1] if closes else None
                        },
                        'volatility': self._calculate_basic_volatility(closes)
                    }

        # Add data quality indicators
        enhanced_data['data_quality'] = {
            'has_price': 'price' in enhanced_data and enhanced_data['price'] is not None,
            'has_historical': 'historical' in enhanced_data and enhanced_data['historical'],
            'has_indicators': 'indicators' in enhanced_data and enhanced_data['indicators'],
            'validation_passed': not enhanced_data.get('validation_warnings', False)
        }

        return enhanced_data

    def _calculate_basic_volatility(self, prices: List[float]) -> float:
        """Calculate basic volatility from price series"""
        if len(prices) < 2:
            return 0.0

        returns = []
        for i in range(1, len(prices)):
            if prices[i-1] != 0:
                return_val = (prices[i] - prices[i-1]) / prices[i-1]
                returns.append(return_val)

        if not returns:
            return 0.0

        # Standard deviation of returns
        mean_return = sum(returns) / len(returns)
        variance = sum((r - mean_return) ** 2 for r in returns) / len(returns)
        return variance ** 0.5