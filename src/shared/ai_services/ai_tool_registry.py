"""
AI Tool Registry - Dynamic tool availability for AI models
Makes MCP tools and other services available to AI whenever needed.
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List, Callable, Union
from dataclasses import dataclass
from datetime import datetime
import json

logger = logging.getLogger(__name__)

@dataclass
class AITool:
    """Represents a tool available to AI models."""
    name: str
    description: str
    parameters: Dict[str, Any]
    handler: Callable
    category: str = "general"
    requires_auth: bool = False
    rate_limited: bool = False
    mcp_tool: bool = False

class AIToolRegistry:
    """
    Registry for AI tools that can be called dynamically by AI models.
    Supports MCP tools, data providers, and custom functions.
    """
    
    def __init__(self):
        self.tools: Dict[str, AITool] = {}
        self.mcp_tools: Dict[str, AITool] = {}
        self.data_tools: Dict[str, AITool] = {}
        self.analysis_tools: Dict[str, AITool] = {}
        
        # Tool execution tracking
        self.execution_count = 0
        self.success_count = 0
        self.error_count = 0
        
        logger.info("🔧 AI Tool Registry initialized")
    
    def register_tool(self, tool: AITool):
        """Register a tool with the AI registry."""
        self.tools[tool.name] = tool
        
        # Categorize tools
        if tool.mcp_tool:
            self.mcp_tools[tool.name] = tool
        elif tool.category == "data":
            self.data_tools[tool.name] = tool
        elif tool.category == "analysis":
            self.analysis_tools[tool.name] = tool
        
        logger.info(f"🔧 Registered tool: {tool.name} ({tool.category})")
    
    def register_mcp_tool(self, name: str, description: str, parameters: Dict[str, Any], handler: Callable):
        """Register an MCP tool."""
        tool = AITool(
            name=name,
            description=description,
            parameters=parameters,
            handler=handler,
            category="mcp",
            mcp_tool=True
        )
        self.register_tool(tool)
    
    def register_data_tool(self, name: str, description: str, parameters: Dict[str, Any], handler: Callable):
        """Register a data provider tool."""
        tool = AITool(
            name=name,
            description=description,
            parameters=parameters,
            handler=handler,
            category="data"
        )
        self.register_tool(tool)
    
    def register_analysis_tool(self, name: str, description: str, parameters: Dict[str, Any], handler: Callable):
        """Register an analysis tool."""
        tool = AITool(
            name=name,
            description=description,
            parameters=parameters,
            handler=handler,
            category="analysis"
        )
        self.register_tool(tool)
    
    async def execute_tool(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a tool by name with given parameters."""
        self.execution_count += 1
        
        try:
            if tool_name not in self.tools:
                raise ValueError(f"Tool '{tool_name}' not found")
            
            tool = self.tools[tool_name]
            logger.info(f"🔧 Executing tool: {tool_name} with params: {list(parameters.keys())}")
            
            # Execute the tool
            result = await tool.handler(**parameters)
            
            self.success_count += 1
            logger.info(f"✅ Tool {tool_name} executed successfully")
            
            return {
                "success": True,
                "result": result,
                "tool_name": tool_name,
                "execution_time": datetime.now().isoformat()
            }
        
        except Exception as e:
            self.error_count += 1
            logger.error(f"❌ Tool {tool_name} execution failed: {e}")
            
            return {
                "success": False,
                "error": str(e),
                "tool_name": tool_name,
                "execution_time": datetime.now().isoformat()
            }
    
    def get_available_tools(self, category: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get list of available tools, optionally filtered by category."""
        tools = []
        
        for tool_name, tool in self.tools.items():
            if category is None or tool.category == category:
                tools.append({
                    "name": tool.name,
                    "description": tool.description,
                    "parameters": tool.parameters,
                    "category": tool.category,
                    "requires_auth": tool.requires_auth,
                    "rate_limited": tool.rate_limited,
                    "mcp_tool": tool.mcp_tool
                })
        
        return tools
    
    def get_tool_schema(self) -> Dict[str, Any]:
        """Get the complete tool schema for AI models."""
        return {
            "tools": self.get_available_tools(),
            "categories": {
                "mcp": list(self.mcp_tools.keys()),
                "data": list(self.data_tools.keys()),
                "analysis": list(self.analysis_tools.keys())
            },
            "total_tools": len(self.tools)
        }
    
    def get_execution_stats(self) -> Dict[str, Any]:
        """Get tool execution statistics."""
        success_rate = (self.success_count / self.execution_count * 100) if self.execution_count > 0 else 0
        
        return {
            "total_executions": self.execution_count,
            "successful_executions": self.success_count,
            "failed_executions": self.error_count,
            "success_rate": success_rate,
            "available_tools": len(self.tools),
            "mcp_tools": len(self.mcp_tools),
            "data_tools": len(self.data_tools),
            "analysis_tools": len(self.analysis_tools)
        }

class MCPToolManager:
    """Manages MCP tools and their integration with AI."""
    
    def __init__(self, mcp_client):
        self.mcp_client = mcp_client
        self.registry = AIToolRegistry()
        self._setup_mcp_tools()
    
    def _setup_mcp_tools(self):
        """Setup MCP tools for AI use."""
        if not self.mcp_client.is_configured:
            logger.warning("MCP client not configured, skipping MCP tool setup")
            return
        
        # Core Stock APIs
        self.registry.register_mcp_tool(
            name="get_global_quote",
            description="Get real-time quote for a stock symbol",
            parameters={
                "type": "object",
                "properties": {
                    "symbol": {
                        "type": "string",
                        "description": "Stock symbol (e.g., AAPL, MSFT)"
                    }
                },
                "required": ["symbol"]
            },
            handler=self.mcp_client.get_global_quote
        )
        
        self.registry.register_mcp_tool(
            name="get_time_series_daily",
            description="Get daily historical data for a stock symbol",
            parameters={
                "type": "object",
                "properties": {
                    "symbol": {
                        "type": "string",
                        "description": "Stock symbol (e.g., AAPL, MSFT)"
                    },
                    "outputsize": {
                        "type": "string",
                        "description": "Output size: 'compact' or 'full'",
                        "default": "compact"
                    }
                },
                "required": ["symbol"]
            },
            handler=self.mcp_client.get_time_series_daily
        )
        
        self.registry.register_mcp_tool(
            name="get_time_series_intraday",
            description="Get intraday historical data for a stock symbol",
            parameters={
                "type": "object",
                "properties": {
                    "symbol": {
                        "type": "string",
                        "description": "Stock symbol (e.g., AAPL, MSFT)"
                    },
                    "interval": {
                        "type": "string",
                        "description": "Time interval: 1min, 5min, 15min, 30min, 60min",
                        "default": "5min"
                    },
                    "outputsize": {
                        "type": "string",
                        "description": "Output size: 'compact' or 'full'",
                        "default": "compact"
                    }
                },
                "required": ["symbol"]
            },
            handler=self.mcp_client.get_time_series_intraday
        )
        
        # Technical Indicators
        self.registry.register_mcp_tool(
            name="get_rsi",
            description="Get RSI (Relative Strength Index) values for a stock",
            parameters={
                "type": "object",
                "properties": {
                    "symbol": {
                        "type": "string",
                        "description": "Stock symbol (e.g., AAPL, MSFT)"
                    },
                    "interval": {
                        "type": "string",
                        "description": "Time interval: daily, weekly, monthly",
                        "default": "daily"
                    },
                    "time_period": {
                        "type": "integer",
                        "description": "Number of periods for RSI calculation",
                        "default": 14
                    },
                    "series_type": {
                        "type": "string",
                        "description": "Price series type: close, open, high, low",
                        "default": "close"
                    }
                },
                "required": ["symbol"]
            },
            handler=self.mcp_client.get_rsi
        )
        
        self.registry.register_mcp_tool(
            name="get_macd",
            description="Get MACD (Moving Average Convergence Divergence) values for a stock",
            parameters={
                "type": "object",
                "properties": {
                    "symbol": {
                        "type": "string",
                        "description": "Stock symbol (e.g., AAPL, MSFT)"
                    },
                    "interval": {
                        "type": "string",
                        "description": "Time interval: daily, weekly, monthly",
                        "default": "daily"
                    },
                    "series_type": {
                        "type": "string",
                        "description": "Price series type: close, open, high, low",
                        "default": "close"
                    }
                },
                "required": ["symbol"]
            },
            handler=self.mcp_client.get_macd
        )
        
        self.registry.register_mcp_tool(
            name="get_bbands",
            description="Get Bollinger Bands values for a stock",
            parameters={
                "type": "object",
                "properties": {
                    "symbol": {
                        "type": "string",
                        "description": "Stock symbol (e.g., AAPL, MSFT)"
                    },
                    "interval": {
                        "type": "string",
                        "description": "Time interval: daily, weekly, monthly",
                        "default": "daily"
                    },
                    "time_period": {
                        "type": "integer",
                        "description": "Number of periods for Bollinger Bands calculation",
                        "default": 20
                    },
                    "series_type": {
                        "type": "string",
                        "description": "Price series type: close, open, high, low",
                        "default": "close"
                    }
                },
                "required": ["symbol"]
            },
            handler=self.mcp_client.get_bbands
        )
        
        # Alpha Intelligence
        self.registry.register_mcp_tool(
            name="get_news_sentiment",
            description="Get news sentiment analysis for a stock",
            parameters={
                "type": "object",
                "properties": {
                    "symbol": {
                        "type": "string",
                        "description": "Stock symbol (e.g., AAPL, MSFT)"
                    },
                    "limit": {
                        "type": "integer",
                        "description": "Maximum number of news articles to analyze",
                        "default": 50
                    }
                },
                "required": ["symbol"]
            },
            handler=self.mcp_client.get_news_sentiment
        )
        
        self.registry.register_mcp_tool(
            name="get_company_overview",
            description="Get company overview and fundamental data",
            parameters={
                "type": "object",
                "properties": {
                    "symbol": {
                        "type": "string",
                        "description": "Stock symbol (e.g., AAPL, MSFT)"
                    }
                },
                "required": ["symbol"]
            },
            handler=self.mcp_client.get_company_overview
        )
        
        # Comprehensive analysis tool
        self.registry.register_mcp_tool(
            name="get_comprehensive_analysis",
            description="Get comprehensive analysis including price, technical indicators, and sentiment",
            parameters={
                "type": "object",
                "properties": {
                    "symbol": {
                        "type": "string",
                        "description": "Stock symbol (e.g., AAPL, MSFT)"
                    }
                },
                "required": ["symbol"]
            },
            handler=self.mcp_client.get_comprehensive_analysis
        )
        
        logger.info(f"🔧 Registered {len(self.registry.mcp_tools)} MCP tools")
    
    def get_tool_registry(self) -> AIToolRegistry:
        """Get the tool registry."""
        return self.registry
    
    async def close(self):
        """Close MCP client."""
        if self.mcp_client:
            await self.mcp_client.close()
