"""
Smart Model Router - Selects optimal AI models based on task complexity and requirements

This router intelligently chooses between different AI models based on:
- Task complexity (quick regex-like tasks vs complex analysis)
- Speed requirements (real-time vs batch processing)
- Cost optimization (free models for simple tasks, premium for complex)
- Quality requirements (high accuracy for financial decisions)
"""

import os
import yaml
import logging
from enum import Enum
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from pathlib import Path

# Lazy loading for heavy dependencies
try:
    from dotenv import load_dotenv
    DOTENV_AVAILABLE = True
except ImportError:
    DOTENV_AVAILABLE = False

# Define PROJECT_ROOT globally for config paths
PROJECT_ROOT = os.path.abspath(os.path.join(Path(__file__).parent, '../../../'))

# Lazy load .env if available
if DOTENV_AVAILABLE:
    load_dotenv(os.path.join(PROJECT_ROOT, '.env'))

DEFAULT_MODEL_ENV = os.getenv('AI_MODEL')

logger = logging.getLogger(__name__)

class TaskType(Enum):
    """Different types of AI tasks with different model requirements"""
    
    # QUICK TASKS - Use fast, cheap models
    SYMBOL_EXTRACTION = "symbol_extraction"      # Extract stock symbols from text
    INTENT_CLASSIFICATION = "intent_classification"  # Classify user intent
    SIMPLE_VALIDATION = "simple_validation"      # Basic validation tasks
    QUICK_FORMATTING = "quick_formatting"        # Format responses
    
    # ANALYSIS TASKS - Use balanced models  
    MARKET_ANALYSIS = "market_analysis"          # Standard financial analysis
    TECHNICAL_ANALYSIS = "technical_analysis"    # Chart analysis
    SENTIMENT_ANALYSIS = "sentiment_analysis"    # Market sentiment
    PRICE_PREDICTION = "price_prediction"        # Price forecasting
    
    # HEAVY TASKS - Use premium models
    COMPLEX_REASONING = "complex_reasoning"      # Multi-step reasoning
    RISK_ASSESSMENT = "risk_assessment"          # Critical risk decisions
    PORTFOLIO_OPTIMIZATION = "portfolio_optimization"  # Complex optimization
    REGULATORY_COMPLIANCE = "regulatory_compliance"    # Legal/compliance analysis

@dataclass
class ModelConfig:
    """Configuration for a specific AI model"""
    name: str
    model_id: str
    max_tokens: int
    temperature: float
    cost_per_1k_tokens: float
    avg_response_time_ms: int
    accuracy_score: float
    use_cases: list

class SmartModelRouter:
    """Intelligently routes AI tasks to optimal models"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.logger = logging.getLogger(__name__)
        self.config_path = config_path or self._get_default_config_path()
        self.config = self._load_yaml_config()
        self._load_model_configs()
    
    def _get_default_config_path(self) -> str:
        """Get the default config file path"""
        # Try multiple possible locations
        possible_paths = [
            '/app/config/services/ai_models.yaml',  # Docker container path
            'config/services/ai_models.yaml',       # Relative to working directory
            Path(__file__).parent.parent.parent.parent / 'config' / 'ai_models.yaml',  # Relative to this file
            PROJECT_ROOT + '/config/services/ai_models.yaml',  # Relative to project root
        ]

        for path in possible_paths:
            if Path(path).exists():
                return str(path)

        # Default fallback
        return str(Path(PROJECT_ROOT) / 'config' / 'services' / 'ai_models.yaml')

    def _load_yaml_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file with lazy loading optimization"""
        try:
            # Check if file exists and was modified recently
            config_path = Path(self.config_path)
            if not config_path.exists():
                self.logger.warning(f"Config file not found at {self.config_path}, using env vars")
                return {}

            # Use cached config if available and fresh
            if hasattr(self, '_cached_config') and hasattr(self, '_config_timestamp'):
                file_modified = config_path.stat().st_mtime
                if file_modified <= self._config_timestamp:
                    return self._cached_config

            # Load config with timing
            import time
            start_time = time.time()

            with open(self.config_path, 'r') as f:
                config = yaml.safe_load(f)

            load_time = time.time() - start_time
            if load_time > 0.1:  # Log slow config loads
                self.logger.warning(f"Slow config load: {self.config_path} took {load_time:.3f}s")

            # Cache the config
            self._cached_config = config
            self._config_timestamp = config_path.stat().st_mtime

            self.logger.info(f"Loaded AI model config from {self.config_path}")
            return config

        except Exception as e:
            self.logger.error(f"Failed to load config from {self.config_path}: {e}")
            return {}

    def _get_config_value(self, path: str, default: Any = None) -> Any:
        """Get a value from config with dot notation (e.g., 'models.quick.temperature')"""
        keys = path.split('.')
        value = self.config

        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        return value

    def _load_model_configs(self):
        """Load model configurations from YAML config or environment variables with lazy loading"""

        # Get current environment with lazy loading
        current_env = os.getenv('ENVIRONMENT', 'development')

        # Apply environment-specific overrides if they exist
        env_overrides = self._get_config_value(f'environments.{current_env}.models', {})

        # Add performance monitoring
        import time
        start_time = time.time()

        self.models = {
            # QUICK MODELS - Fast, cheap, good for simple tasks
            "quick": ModelConfig(
                name="Quick Model",
                model_id=env_overrides.get('quick', {}).get('model_id') or
                        self._get_config_value('models.quick.model_id',
                                               os.getenv('MODEL_QUICK', DEFAULT_MODEL_ENV or 'moonshotai/kimi-k2-0905')),
                max_tokens=self._get_config_value('models.quick.max_tokens', 2000),
                temperature=self._get_config_value('models.quick.temperature', 0.1),
                cost_per_1k_tokens=self._get_config_value('models.quick.cost_per_1k_tokens', 0.0),
                avg_response_time_ms=self._get_config_value('models.quick.avg_response_time_ms', 500),
                accuracy_score=self._get_config_value('models.quick.accuracy_rating', 0.85),
                use_cases=[TaskType.SYMBOL_EXTRACTION, TaskType.INTENT_CLASSIFICATION,
                          TaskType.SIMPLE_VALIDATION, TaskType.QUICK_FORMATTING]
            ),

            # ANALYSIS MODELS - Balanced speed/intelligence
            "analysis": ModelConfig(
                name="Analysis Model",
                model_id=env_overrides.get('analysis', {}).get('model_id') or
                        self._get_config_value('models.analysis.model_id',
                                               os.getenv('MODEL_ANALYSIS', DEFAULT_MODEL_ENV or 'anthropic/claude-3.5-sonnet')),
                max_tokens=self._get_config_value('models.analysis.max_tokens', 4000),
                temperature=self._get_config_value('models.analysis.temperature', 0.3),
                cost_per_1k_tokens=self._get_config_value('models.analysis.cost_per_1k_tokens', 0.003),
                avg_response_time_ms=self._get_config_value('models.analysis.avg_response_time_ms', 2000),
                accuracy_score=self._get_config_value('models.analysis.accuracy_rating', 0.95),
                use_cases=[TaskType.MARKET_ANALYSIS, TaskType.TECHNICAL_ANALYSIS,
                          TaskType.SENTIMENT_ANALYSIS, TaskType.PRICE_PREDICTION]
            ),

            # HEAVY MODELS - Premium intelligence for critical tasks
            "heavy": ModelConfig(
                name="Heavy Model",
                model_id=env_overrides.get('heavy', {}).get('model_id') or
                        self._get_config_value('models.heavy.model_id',
                                               os.getenv('MODEL_HEAVY', DEFAULT_MODEL_ENV or 'anthropic/claude-3.5-sonnet')),
                max_tokens=self._get_config_value('models.heavy.max_tokens', 8000),
                temperature=self._get_config_value('models.heavy.temperature', 0.2),
                cost_per_1k_tokens=self._get_config_value('models.heavy.cost_per_1k_tokens', 0.003),
                avg_response_time_ms=self._get_config_value('models.heavy.avg_response_time_ms', 4000),
                accuracy_score=self._get_config_value('models.heavy.accuracy_rating', 0.98),
                use_cases=[TaskType.COMPLEX_REASONING, TaskType.RISK_ASSESSMENT,
                          TaskType.PORTFOLIO_OPTIMIZATION, TaskType.REGULATORY_COMPLIANCE]
            ),

            # FALLBACK MODEL
            "fallback": ModelConfig(
                name="Fallback Model",
                model_id=env_overrides.get('fallback', {}).get('model_id') or
                        self._get_config_value('models.fallback.model_id',
                                               os.getenv('MODEL_GLOBAL_FALLBACK', DEFAULT_MODEL_ENV or 'moonshotai/kimi-k2-0905')),
                max_tokens=self._get_config_value('models.fallback.max_tokens', 2000),
                temperature=self._get_config_value('models.fallback.temperature', 0.5),
                cost_per_1k_tokens=self._get_config_value('models.fallback.cost_per_1k_tokens', 0.0),
                avg_response_time_ms=self._get_config_value('models.fallback.avg_response_time_ms', 1000),
                accuracy_score=self._get_config_value('models.fallback.accuracy_rating', 0.80),
                use_cases=list(TaskType)  # Can handle any task as fallback
            )
        }

        # Log the loaded configuration with performance metrics
        load_time = time.time() - start_time
        self.logger.info(f"Loaded AI models for environment: {current_env} (took {load_time:.3f}s)")
        for model_type, config in self.models.items():
            self.logger.info(f"  {model_type}: {config.model_id} (accuracy: {config.accuracy_score})")

        # Store performance metrics
        if not hasattr(self, '_performance_metrics'):
            self._performance_metrics = {}

        self._performance_metrics['model_load_time'] = load_time
    
    def get_optimal_model(self, task_type: TaskType,
                         priority: str = "balanced",
                         max_cost_per_1k: float = None) -> ModelConfig:
        """
        Get the optimal model for a specific task

        Args:
            task_type: Type of AI task to perform
            priority: "speed", "cost", "accuracy", or "balanced"
            max_cost_per_1k: Maximum cost per 1K tokens (optional)

        Returns:
            ModelConfig: Optimal model configuration
        """

        # Check for task-specific routing preferences in YAML config
        task_routing = self._get_config_value('task_routing', {})
        task_config = task_routing.get(task_type.value, {})

        # Use task-specific preferences if available
        if task_config:
            preferred_model = task_config.get('preferred_model')
            if preferred_model and preferred_model in self.models:
                task_max_cost = task_config.get('max_cost_per_1k', max_cost_per_1k)

                # Check if preferred model meets cost constraints
                preferred_config = self.models[preferred_model]
                if task_max_cost is None or preferred_config.cost_per_1k_tokens <= task_max_cost:
                    self.logger.info(f"Using preferred model {preferred_config.name} for {task_type.value}")
                    return preferred_config

        # Find models that can handle this task type
        suitable_models = []
        for model_key, model_config in self.models.items():
            if task_type in model_config.use_cases:
                if max_cost_per_1k is None or model_config.cost_per_1k_tokens <= max_cost_per_1k:
                    suitable_models.append((model_key, model_config))

        if not suitable_models:
            self.logger.warning(f"No suitable models found for {task_type}, using fallback")
            return self.models["fallback"]

        # Select best model based on priority
        if priority == "speed":
            # Fastest model
            best_model = min(suitable_models, key=lambda x: x[1].avg_response_time_ms)[1]
        elif priority == "cost":
            # Cheapest model
            best_model = min(suitable_models, key=lambda x: x[1].cost_per_1k_tokens)[1]
        elif priority == "accuracy":
            # Most accurate model
            best_model = max(suitable_models, key=lambda x: x[1].accuracy_score)[1]
        else:  # balanced
            # Best overall score (weighted combination)
            def score_model(model_config):
                # Normalize metrics (lower is better for cost/time, higher for accuracy)
                speed_score = 1.0 / (model_config.avg_response_time_ms / 1000.0)  # Inverse of seconds
                cost_score = 1.0 / (model_config.cost_per_1k_tokens + 0.001)  # Inverse of cost
                accuracy_score = model_config.accuracy_score

                # Weighted combination
                return (speed_score * 0.3 + cost_score * 0.3 + accuracy_score * 0.4)

            best_model = max(suitable_models, key=lambda x: score_model(x[1]))[1]

        self.logger.info(f"Selected {best_model.name} for {task_type.value} (priority: {priority})")
        return best_model
    
    def get_model_for_query_analysis(self) -> str:
        """Get model ID for quick query analysis (symbol extraction, intent classification)"""
        model = self.get_optimal_model(TaskType.INTENT_CLASSIFICATION, priority="speed")
        return model.model_id

    def get_available_models(self) -> Dict[str, str]:
        """Get all available models"""
        return {name: model.model_id for name, model in self.models.items()}
    
    def get_model_for_market_analysis(self) -> str:
        """Get model ID for market analysis tasks"""
        model = self.get_optimal_model(TaskType.MARKET_ANALYSIS, priority="balanced")
        return model.model_id
    
    def get_model_for_complex_reasoning(self) -> str:
        """Get model ID for complex reasoning tasks"""
        model = self.get_optimal_model(TaskType.COMPLEX_REASONING, priority="accuracy")
        return model.model_id

    def get_preferred_provider_for_task(self, task_name: str) -> str:
        """
        Get the preferred provider for a specific task type.
        This maps to the model_id of the optimal model for the task.

        Args:
            task_name: Name of the task (e.g., "market_analysis", "general_question")

        Returns:
            str: The model ID to use for this task
        """
        try:
            # Map task names to TaskType enum
            task_mapping = {
                "market_analysis": TaskType.MARKET_ANALYSIS,
                "technical_analysis": TaskType.TECHNICAL_ANALYSIS,
                "sentiment_analysis": TaskType.SENTIMENT_ANALYSIS,
                "price_prediction": TaskType.PRICE_PREDICTION,
                "complex_reasoning": TaskType.COMPLEX_REASONING,
                "risk_assessment": TaskType.RISK_ASSESSMENT,
                "general_question": TaskType.INTENT_CLASSIFICATION,  # Default for general questions
                "symbol_extraction": TaskType.SYMBOL_EXTRACTION,
                "intent_classification": TaskType.INTENT_CLASSIFICATION,
                "simple_validation": TaskType.SIMPLE_VALIDATION
            }

            # Get the task type or default to intent classification
            task_type = task_mapping.get(task_name, TaskType.INTENT_CLASSIFICATION)

            # Get the optimal model for this task
            optimal_model = self.get_optimal_model(task_type)

            # Return the model ID
            return optimal_model.model_id

        except Exception as e:
            self.logger.error(f"Error getting preferred provider for task {task_name}: {e}")
            # Fallback to a safe default (via OpenRouter)
            return "moonshotai/kimi-k2-0905"

    def get_fallback_providers(self, task_type: Optional[TaskType] = None) -> List[str]:
        """
        Get fallback providers/models for a task type.

        Args:
            task_type: The task type to get fallbacks for

        Returns:
            List[str]: List of fallback model IDs
        """
        try:
            if task_type is None:
                # Default fallbacks
                return ["anthropic/claude-3.5-sonnet", "openai/gpt-4o-mini"]

            # Get all models suitable for this task
            suitable_models = []
            for model_key, model_config in self.models.items():
                if task_type in model_config.use_cases:
                    suitable_models.append(model_config.model_id)

            # Return all but the first one (which should be the primary)
            return suitable_models[1:] if len(suitable_models) > 1 else ["anthropic/claude-3.5-sonnet"]

        except Exception as e:
            self.logger.error(f"Error getting fallback providers: {e}")
            # Fallback to safe defaults
            return ["anthropic/claude-3.5-sonnet", "openai/gpt-4o-mini"]

    def log_model_usage(self, task_type: TaskType, model_used: str,
                       response_time_ms: int, tokens_used: int):
        """Log model usage for performance tracking"""
        self.logger.info(f"Model Usage: {model_used} for {task_type.value} "
                        f"({response_time_ms}ms, {tokens_used} tokens)")

    def get_cache_config_for_intent(self, intent: str, needs_data: bool = False) -> Dict[str, Any]:
        """Get caching configuration for a specific intent"""
        cache_config = self._get_config_value('caching', {})

        # Check for live price queries
        live_price_config = cache_config.get('live_price_queries', {})
        if intent in live_price_config.get('intents', []) and needs_data:
            return {
                'enabled': live_price_config.get('enabled', True),
                'ttl_seconds': live_price_config.get('ttl_seconds', 30),
                'bypass': live_price_config.get('bypass_conditions', {}).get('force_fresh', False)
            }

        # Check for analysis results
        analysis_config = cache_config.get('analysis_results', {})
        if intent in analysis_config.get('intents', []):
            return {
                'enabled': analysis_config.get('enabled', True),
                'ttl_seconds': analysis_config.get('ttl_seconds', 180),
                'bypass': False
            }

        # Default to general queries config
        general_config = cache_config.get('general_queries', {})
        return {
            'enabled': general_config.get('enabled', True),
            'ttl_seconds': general_config.get('ttl_seconds', 300),
            'bypass': False
        }

    def should_bypass_cache(self, intent: str, needs_data: bool = False) -> bool:
        """Determine if cache should be bypassed for this query"""
        cache_config = self.get_cache_config_for_intent(intent, needs_data)
        return cache_config.get('bypass', False)

    def get_cache_ttl(self, intent: str, needs_data: bool = False) -> int:
        """Get cache TTL in seconds for this query type"""
        cache_config = self.get_cache_config_for_intent(intent, needs_data)
        return cache_config.get('ttl_seconds', 300)

    def get_cost_budget_for_model_type(self, model_type: str) -> float:
        """Get daily budget limit for a specific model type"""
        cost_config = self._get_config_value('cost_management.daily_budget', {})
        model_budget_key = f"{model_type}_models"
        return cost_config.get(model_budget_key, 10.0)  # Default $10 daily budget

    def should_downgrade_for_budget(self, model_type: str, estimated_cost: float) -> bool:
        """Check if we should downgrade model due to budget constraints"""
        cost_config = self._get_config_value('cost_management.optimization', {})
        if not cost_config.get('auto_downgrade_on_budget', False):
            return False

        budget = self.get_cost_budget_for_model_type(model_type)
        # This would need to be implemented with actual usage tracking
        # For now, just check if single query exceeds 10% of daily budget
        return estimated_cost > (budget * 0.1)

    def get_provider_config(self, provider_name: str) -> Dict[str, Any]:
        """Get configuration for a specific AI provider"""
        providers_config = self._get_config_value('providers', {})
        return providers_config.get(provider_name, {})

    def get_performance_config(self) -> Dict[str, Any]:
        """Get performance monitoring configuration"""
        return self._get_config_value('performance', {
            'enable_metrics': True,
            'metrics_retention_days': 30,
            'circuit_breaker': {
                'failure_threshold': 5,
                'timeout_seconds': 300
            }
        })

    def get_model_by_name(self, model_name: str) -> Optional[ModelConfig]:
        """Get a specific model configuration by name"""
        return self.models.get(model_name)

    def get_all_models_for_task(self, task_type: TaskType) -> Dict[str, ModelConfig]:
        """Get all models that can handle a specific task type"""
        suitable_models = {}
        for model_key, model_config in self.models.items():
            if task_type in model_config.use_cases:
                suitable_models[model_key] = model_config
        return suitable_models

    def estimate_cost(self, model_name: str, estimated_tokens: int) -> float:
        """Estimate cost for a query with given token count"""
        model = self.models.get(model_name)
        if not model:
            return 0.0
        return (estimated_tokens / 1000.0) * model.cost_per_1k_tokens

    def get_cheapest_model_for_task(self, task_type: TaskType) -> ModelConfig:
        """Get the cheapest model that can handle a specific task"""
        suitable_models = self.get_all_models_for_task(task_type)
        if not suitable_models:
            return self.models["fallback"]

        cheapest = min(suitable_models.values(), key=lambda x: x.cost_per_1k_tokens)
        return cheapest

    def get_fastest_model_for_task(self, task_type: TaskType) -> ModelConfig:
        """Get the fastest model that can handle a specific task"""
        suitable_models = self.get_all_models_for_task(task_type)
        if not suitable_models:
            return self.models["fallback"]

        fastest = min(suitable_models.values(), key=lambda x: x.avg_response_time_ms)
        return fastest

    def get_most_accurate_model_for_task(self, task_type: TaskType) -> ModelConfig:
        """Get the most accurate model that can handle a specific task"""
        suitable_models = self.get_all_models_for_task(task_type)
        if not suitable_models:
            return self.models["fallback"]

        most_accurate = max(suitable_models.values(), key=lambda x: x.accuracy_score)
        return most_accurate

class PerformanceOptimizedRouter(SmartModelRouter):
    """Performance-optimized version of SmartModelRouter with lazy loading"""

    def __init__(self, config_path: Optional[str] = None):
        # Delay heavy initialization until needed
        self._models_loaded = False
        self._config_loaded = False
        super().__init__(config_path)

    def _ensure_models_loaded(self):
        """Ensure models are loaded only when needed"""
        if not self._models_loaded:
            import time
            start_time = time.time()

            # Load the models
            self._load_model_configs()

            load_time = time.time() - start_time
            self.logger.info(f"Lazy-loaded AI models in {load_time:.3f}s")
            self._models_loaded = True

    def _ensure_config_loaded(self):
        """Ensure config is loaded only when needed"""
        if not self._config_loaded:
            import time
            start_time = time.time()

            # Load the config
            self.config = self._load_yaml_config()

            load_time = time.time() - start_time
            self.logger.info(f"Lazy-loaded config in {load_time:.3f}s")
            self._config_loaded = True

    def get_optimal_model(self, task_type: TaskType, priority: str = "balanced", max_cost_per_1k: float = None) -> ModelConfig:
        """Override to ensure lazy loading"""
        self._ensure_models_loaded()
        self._ensure_config_loaded()
        return super().get_optimal_model(task_type, priority, max_cost_per_1k)

    def _get_config_value(self, path: str, default: Any = None) -> Any:
        """Override to ensure lazy loading"""
        self._ensure_config_loaded()
        return super()._get_config_value(path, default)

# Use performance-optimized router by default
router = PerformanceOptimizedRouter()
