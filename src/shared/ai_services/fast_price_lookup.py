"""
Fast Price Lookup Service
Provides rapid price lookup for simple queries without AI processing
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

from src.shared.error_handling.logging import get_logger
from src.shared.data_providers.aggregator import data_provider_aggregator
from src.shared.ai_services.simple_query_analyzer import SymbolInfo

logger = get_logger(__name__)

class FastPriceLookup:
    """Fast price lookup service for simple queries"""
    
    def __init__(self):
        self.logger = logger
    
    async def get_quick_price_response(self, symbols: List[SymbolInfo], 
                                     query: str, user_id: str) -> Optional[str]:
        """
        Get a quick price response for simple queries.
        
        Args:
            symbols: List of detected symbols
            query: Original query string
            user_id: User ID for context
            
        Returns:
            Formatted price response or None if unable to process
        """
        if not symbols:
            return None
        
        try:
            # For now, only handle single symbol queries
            if len(symbols) != 1:
                return None
            
            symbol = symbols[0].text
            
            # Get current price data
            price_data = await self._get_current_price(symbol)
            
            if not price_data or not price_data.get('success'):
                return None
            
            # Format quick response
            response = self._format_price_response(symbol, price_data)
            
            return response
            
        except Exception as e:
            self.logger.warning(f"Quick price lookup failed for symbols {[s.text for s in symbols]}: {e}")
            return None
    
    async def _get_current_price(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        Get current price for a symbol.
        
        Args:
            symbol: Stock/crypto symbol
            
        Returns:
            Price data dictionary or None
        """
        try:
            # Use the data provider aggregator for fast price lookup
            price_data = await data_provider_aggregator.get_current_price(symbol)
            
            if price_data and price_data.get('success'):
                return price_data
            else:
                self.logger.warning(f"No price data available for {symbol}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error getting current price for {symbol}: {e}")
            return None
    
    def _format_price_response(self, symbol: str, price_data: Dict[str, Any]) -> str:
        """
        Format price data into a user-friendly response.
        
        Args:
            symbol: Stock/crypto symbol
            price_data: Price data dictionary
            
        Returns:
            Formatted response string
        """
        try:
            price = price_data.get('price')
            change = price_data.get('change')
            change_percent = price_data.get('change_percent')
            volume = price_data.get('volume')
            timestamp = price_data.get('timestamp')
            
            if price is None:
                return f"❌ Unable to retrieve current price for {symbol}"
            
            # Build response parts
            from src.core.formatting.text_formatting import format_price
            response_parts = [
                f"**💰 {symbol} Current Price**",
                f"📈 **Price**: {format_price(price)}"
            ]
            
            # Add change information if available
            if change is not None and change_percent is not None:
                change_emoji = "📈" if change >= 0 else "📉"
                formatted_change = format_price(abs(change)) if abs(change) >= 1.0 else f"${abs(change):.2f}"
                response_parts.append(f"{change_emoji} **Change**: {'+' if change >= 0 else '-'}{formatted_change} ({change_percent:+.2f}%)")
            
            # Add volume if available
            if volume is not None:
                response_parts.append(f"📊 **Volume**: {volume:,.0f}")
            
            # Add timestamp if available
            if timestamp:
                try:
                    dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    response_parts.append(f"⏰ **Last Updated**: {dt.strftime('%Y-%m-%d %H:%M:%S')} UTC")
                except Exception:
                    pass  # Skip timestamp if parsing fails
            
            # Add note about simple price queries
            response_parts.append("\n💡 *This is a quick price lookup. For detailed analysis, use the `/analyze` command.*")
            
            return "\n".join(response_parts)
            
        except Exception as e:
            self.logger.error(f"Error formatting price response for {symbol}: {e}")
            return f"**💰 {symbol} Current Price**: ${price_data.get('price', 0):.2f}"

# Global instance
fast_price_lookup = FastPriceLookup()

# Convenience function
async def get_quick_price_response(symbols: List[SymbolInfo], 
                                 query: str, user_id: str) -> Optional[str]:
    """Get quick price response for simple queries"""
    return await fast_price_lookup.get_quick_price_response(symbols, query, user_id)