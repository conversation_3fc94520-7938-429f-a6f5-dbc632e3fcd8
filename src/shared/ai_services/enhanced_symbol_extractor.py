"""
Enhanced AI-Powered Symbol Extractor

This module provides intelligent symbol extraction that combines AI understanding
with regex fallback for maximum accuracy and reliability.
"""

import asyncio
import logging
import re
import time
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from src.shared.error_handling.logging import get_logger
from src.shared.ai_chat.ai_client import AIClientWrapper

logger = get_logger(__name__)

class ExtractionMethod(Enum):
    """Methods used for symbol extraction"""
    AI_POWERED = "ai_powered"
    REGEX_FALLBACK = "regex_fallback"
    COMPANY_MAPPING = "company_mapping"
    HYBRID = "hybrid"

@dataclass
class SymbolExtractionResult:
    """Result of symbol extraction with metadata"""
    symbol: str
    confidence: float
    method: ExtractionMethod
    context: str
    original_text: str
    company_name: Optional[str] = None
    reasoning: Optional[str] = None
    alternatives: List[str] = None
    processing_time: float = 0.0

class EnhancedSymbolExtractor:
    """
    AI-powered symbol extractor with intelligent fallback mechanisms
    """
    
    def __init__(self):
        self.ai_client = AIClientWrapper()
        
        # Company name to ticker mapping (expanded)
        self.company_mapping = {
            # Major Tech Companies
            'apple': 'AAPL',
            'apple inc': 'AAPL',
            'apple computer': 'AAPL',
            'microsoft': 'MSFT',
            'microsoft corporation': 'MSFT',
            'google': 'GOOGL',
            'alphabet': 'GOOGL',
            'alphabet inc': 'GOOGL',
            'amazon': 'AMZN',
            'amazon.com': 'AMZN',
            'tesla': 'TSLA',
            'tesla motors': 'TSLA',
            'tesla inc': 'TSLA',
            'meta': 'META',
            'facebook': 'META',
            'meta platforms': 'META',
            'nvidia': 'NVDA',
            'nvidia corporation': 'NVDA',
            'amd': 'AMD',
            'advanced micro devices': 'AMD',
            'intel': 'INTC',
            'intel corporation': 'INTC',
            'netflix': 'NFLX',
            'disney': 'DIS',
            'walt disney': 'DIS',
            'boeing': 'BA',
            'boeing company': 'BA',
            
            # Financial Companies
            'jpmorgan': 'JPM',
            'jp morgan': 'JPM',
            'jpmorgan chase': 'JPM',
            'goldman sachs': 'GS',
            'bank of america': 'BAC',
            'wells fargo': 'WFC',
            'morgan stanley': 'MS',
            'citigroup': 'C',
            'american express': 'AXP',
            
            # Other Major Companies
            'berkshire hathaway': 'BRK.B',
            'johnson & johnson': 'JNJ',
            'procter & gamble': 'PG',
            'coca cola': 'KO',
            'coca-cola': 'KO',
            'pepsi': 'PEP',
            'pepsico': 'PEP',
            'walmart': 'WMT',
            'exxon mobil': 'XOM',
            'chevron': 'CVX',
            
            # Crypto (common ones)
            'bitcoin': 'BTC',
            'ethereum': 'ETH',
            'binance coin': 'BNB',
            'cardano': 'ADA',
            'solana': 'SOL',
            'dogecoin': 'DOGE',
            'polygon': 'MATIC',
            'chainlink': 'LINK'
        }
        
        # Regex patterns for fallback
        self.regex_patterns = {
            'dollar_prefix': r'\$([A-Z]{1,5})\b',
            'standalone_symbol': r'\b([A-Z]{2,5})\b',
            'ticker_context': r'(?:ticker|symbol|stock)\s*:?\s*([A-Z]{1,5})\b',
            'trading_context': r'([A-Z]{1,5})\s+(?:stock|shares|is\s+trading)',
            'crypto_context': r'\b([A-Z]{2,5})\s+(?:crypto|coin|token)'
        }
        
        # Words to exclude (not stock symbols)
        self.exclusions = {
            'THE', 'AND', 'FOR', 'YOU', 'ARE', 'BUT', 'NOT', 'CAN', 'ALL',
            'USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD', 'CHF', 'CNY',
            'CEO', 'CFO', 'CTO', 'IPO', 'ETF', 'NYSE', 'NASDAQ',
            'AI', 'ML', 'API', 'URL', 'HTTP', 'HTTPS', 'HTML', 'CSS', 'JS'
        }
        
        # AI prompt template
        self.ai_prompt_template = """
Analyze this financial text and extract all stock symbols, tickers, and cryptocurrency symbols mentioned.

Text: "{text}"

Instructions:
1. Look for explicit symbols like $AAPL, TSLA, BTC
2. Identify company names and map them to their stock symbols (e.g., "Apple" → "AAPL")
3. Consider context clues (e.g., "trading", "stock", "price", "investment")
4. Include cryptocurrencies if mentioned
5. Exclude currency codes (USD, EUR) unless explicitly about forex trading
6. Exclude common words that happen to be capitalized

Return a JSON array of objects with this format:
[
  {{
    "symbol": "AAPL",
    "confidence": 0.95,
    "company_name": "Apple Inc",
    "reasoning": "Explicit mention of Apple company",
    "context": "price inquiry"
  }}
]

Only include symbols you are confident about (confidence > 0.7).
"""
    
    async def extract_symbols(self, text: str, use_ai: bool = True, 
                            fallback_on_failure: bool = True) -> List[SymbolExtractionResult]:
        """
        Extract symbols using AI with intelligent fallback
        
        Args:
            text: Text to extract symbols from
            use_ai: Whether to use AI processing first
            fallback_on_failure: Whether to use regex fallback if AI fails
            
        Returns:
            List of SymbolExtractionResult objects
        """
        start_time = time.time()
        results = []
        
        if not text or not text.strip():
            return results
        
        # Try AI extraction first
        if use_ai:
            try:
                ai_results = await self._extract_with_ai(text)
                if ai_results:
                    for result in ai_results:
                        result.processing_time = time.time() - start_time
                    return ai_results

                logger.debug(f"AI extraction returned no results for: {text[:50]}...")

                # If AI explicitly returns no symbols, trust it and don't fall back to regex
                # Only use regex fallback for $ symbols and company names (very high confidence)
                dollar_and_company_results = await self._extract_dollar_and_company_only(text)
                for result in dollar_and_company_results:
                    result.processing_time = time.time() - start_time
                return dollar_and_company_results

            except Exception as e:
                logger.warning(f"AI symbol extraction failed: {e}")

        # Use conservative regex fallback only when AI is disabled or failed
        if fallback_on_failure or not use_ai:
            regex_results = await self._extract_with_regex_conservative(text)
            for result in regex_results:
                result.processing_time = time.time() - start_time
            return regex_results
        
        return results
    
    async def _extract_with_ai(self, text: str) -> List[SymbolExtractionResult]:
        """Extract symbols using AI"""
        try:
            prompt = self.ai_prompt_template.format(text=text)
            
            # Get AI response
            response = await self.ai_client.generate_response(prompt)
            
            if not response:
                return []
            
            # Parse AI response
            symbols = self._parse_ai_response(response)
            
            # Convert to SymbolExtractionResult objects
            results = []
            for symbol_data in symbols:
                result = SymbolExtractionResult(
                    symbol=symbol_data.get('symbol', '').upper(),
                    confidence=float(symbol_data.get('confidence', 0.8)),
                    method=ExtractionMethod.AI_POWERED,
                    context=symbol_data.get('context', 'ai_analysis'),
                    original_text=text,
                    company_name=symbol_data.get('company_name'),
                    reasoning=symbol_data.get('reasoning'),
                    alternatives=symbol_data.get('alternatives', [])
                )
                
                # Validate symbol format
                if self._is_valid_symbol_format(result.symbol):
                    results.append(result)
            
            return results
            
        except Exception as e:
            logger.error(f"AI symbol extraction error: {e}")
            return []

    async def _extract_with_regex_conservative(self, text: str) -> List[SymbolExtractionResult]:
        """Extract symbols using only high-confidence regex patterns (no standalone symbols)"""
        results = []
        text_upper = text.upper()
        text_lower = text.lower()

        # 1. Extract explicit symbols with $ prefix (highest confidence)
        dollar_matches = re.finditer(self.regex_patterns['dollar_prefix'], text)
        for match in dollar_matches:
            symbol = match.group(1)
            if self._is_valid_symbol_format(symbol):
                results.append(SymbolExtractionResult(
                    symbol=symbol,
                    confidence=0.95,
                    method=ExtractionMethod.REGEX_FALLBACK,
                    context="dollar_prefix",
                    original_text=match.group(0)
                ))

        # 2. Company name mapping (high confidence)
        for company, ticker in self.company_mapping.items():
            if company in text_lower:
                results.append(SymbolExtractionResult(
                    symbol=ticker,
                    confidence=0.85,
                    method=ExtractionMethod.REGEX_FALLBACK,
                    context=f"company_mapping:{company}",
                    original_text=company
                ))

        # 3. Only extract symbols with explicit context (no standalone uppercase words)
        high_confidence_patterns = ['ticker_context', 'trading_context']
        for pattern_name in high_confidence_patterns:
            if pattern_name in self.regex_patterns:
                pattern = self.regex_patterns[pattern_name]
                matches = re.finditer(pattern, text, re.IGNORECASE)
                for match in matches:
                    symbol = match.group(1).upper()
                    if (self._is_valid_symbol_format(symbol) and
                        symbol not in self.exclusions):

                        results.append(SymbolExtractionResult(
                            symbol=symbol,
                            confidence=0.8,
                            method=ExtractionMethod.REGEX_FALLBACK,
                            context=pattern_name,
                            original_text=match.group(0)
                        ))

        # Remove duplicates
        seen = set()
        unique_results = []
        for result in results:
            if result.symbol not in seen:
                seen.add(result.symbol)
                unique_results.append(result)

        return unique_results

    async def _extract_dollar_and_company_only(self, text: str) -> List[SymbolExtractionResult]:
        """Extract only $ symbols and company names (highest confidence only)"""
        results = []
        text_lower = text.lower()

        # 1. Extract explicit symbols with $ prefix (highest confidence)
        dollar_matches = re.finditer(self.regex_patterns['dollar_prefix'], text)
        for match in dollar_matches:
            symbol = match.group(1)
            if self._is_valid_symbol_format(symbol):
                results.append(SymbolExtractionResult(
                    symbol=symbol,
                    confidence=0.95,
                    method=ExtractionMethod.REGEX_FALLBACK,
                    context="dollar_prefix",
                    original_text=match.group(0)
                ))

        # 2. Company name mapping only (no other regex patterns)
        for company, ticker in self.company_mapping.items():
            if company in text_lower:
                results.append(SymbolExtractionResult(
                    symbol=ticker,
                    confidence=0.85,
                    method=ExtractionMethod.REGEX_FALLBACK,
                    context=f"company_mapping:{company}",
                    original_text=company
                ))

        # Remove duplicates
        seen = set()
        unique_results = []
        for result in results:
            if result.symbol not in seen:
                seen.add(result.symbol)
                unique_results.append(result)

        return unique_results

    async def _extract_with_regex(self, text: str) -> List[SymbolExtractionResult]:
        """Extract symbols using regex patterns with company mapping"""
        results = []
        text_upper = text.upper()
        text_lower = text.lower()
        
        # 1. Extract explicit symbols with $ prefix (highest confidence)
        dollar_matches = re.finditer(self.regex_patterns['dollar_prefix'], text)
        for match in dollar_matches:
            symbol = match.group(1)
            if self._is_valid_symbol_format(symbol):
                results.append(SymbolExtractionResult(
                    symbol=symbol,
                    confidence=0.95,
                    method=ExtractionMethod.REGEX_FALLBACK,
                    context="dollar_prefix",
                    original_text=match.group(0)
                ))
        
        # 2. Extract symbols with context clues
        for pattern_name, pattern in self.regex_patterns.items():
            if pattern_name == 'dollar_prefix':
                continue  # Already handled
                
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                symbol = match.group(1).upper()
                if (self._is_valid_symbol_format(symbol) and 
                    symbol not in self.exclusions):
                    
                    confidence = 0.8 if 'context' in pattern_name else 0.6
                    results.append(SymbolExtractionResult(
                        symbol=symbol,
                        confidence=confidence,
                        method=ExtractionMethod.REGEX_FALLBACK,
                        context=pattern_name,
                        original_text=match.group(0)
                    ))
        
        # 3. Company name mapping
        for company, ticker in self.company_mapping.items():
            if company in text_lower:
                results.append(SymbolExtractionResult(
                    symbol=ticker,
                    confidence=0.85,
                    method=ExtractionMethod.COMPANY_MAPPING,
                    context="company_name",
                    original_text=company,
                    company_name=company.title()
                ))
        
        # Remove duplicates, keeping highest confidence
        return self._deduplicate_results(results)
    
    def _parse_ai_response(self, response: str) -> List[Dict[str, Any]]:
        """Parse AI response to extract symbol data"""
        import json
        
        # Try to find JSON in the response
        json_patterns = [
            r'\[.*?\]',  # Array
            r'\{.*?\}'   # Single object
        ]
        
        for pattern in json_patterns:
            matches = re.findall(pattern, response, re.DOTALL)
            for match in matches:
                try:
                    data = json.loads(match)
                    if isinstance(data, list):
                        return data
                    elif isinstance(data, dict):
                        return [data]
                except json.JSONDecodeError:
                    continue
        
        # Fallback: try to parse the entire response
        try:
            return json.loads(response.strip())
        except json.JSONDecodeError:
            logger.debug(f"Could not parse AI response as JSON: {response[:100]}...")
            return []
    
    def _is_valid_symbol_format(self, symbol: str) -> bool:
        """Validate symbol format"""
        if not symbol or len(symbol) < 1 or len(symbol) > 5:
            return False
        
        # Must be uppercase letters, possibly with dots
        if not re.match(r'^[A-Z]{1,5}(\.[A-Z]{1,2})?$', symbol):
            return False
        
        # Exclude known non-symbols
        if symbol in self.exclusions:
            return False
        
        return True
    
    def _deduplicate_results(self, results: List[SymbolExtractionResult]) -> List[SymbolExtractionResult]:
        """Remove duplicates, keeping highest confidence"""
        symbol_map = {}
        
        for result in results:
            symbol = result.symbol
            if (symbol not in symbol_map or 
                result.confidence > symbol_map[symbol].confidence):
                symbol_map[symbol] = result
        
        # Sort by confidence descending
        return sorted(symbol_map.values(), key=lambda x: x.confidence, reverse=True)
    
    # Convenience methods
    async def extract_symbols_simple(self, text: str, use_ai: bool = True) -> List[str]:
        """Extract symbols and return just the symbol strings"""
        results = await self.extract_symbols(text, use_ai=use_ai)
        return [result.symbol for result in results]
    
    async def extract_with_confidence(self, text: str, min_confidence: float = 0.7) -> List[Tuple[str, float]]:
        """Extract symbols with confidence scores above threshold"""
        results = await self.extract_symbols(text, use_ai=True)
        return [(r.symbol, r.confidence) for r in results if r.confidence >= min_confidence]

# Global instance for easy access
enhanced_symbol_extractor = EnhancedSymbolExtractor()

# Convenience functions
async def extract_symbols_ai(text: str) -> List[str]:
    """Extract symbols using AI-powered analysis"""
    return await enhanced_symbol_extractor.extract_symbols_simple(text, use_ai=True)

async def extract_symbols_with_metadata(text: str) -> List[SymbolExtractionResult]:
    """Extract symbols with full metadata"""
    return await enhanced_symbol_extractor.extract_symbols(text, use_ai=True)
