"""
Enhanced AI Client with Dynamic Tool Access
Allows AI models to call MCP tools and other services dynamically.
"""

import asyncio
import json
import logging
from typing import Dict, Any, Optional, List, Union
from datetime import datetime
import openai
from openai import AsyncOpenA<PERSON>

from .ai_tool_registry import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ry, MCPToolManager
from .rate_limit_handler import Rate<PERSON>imitHandler
from .circuit_breaker import CircuitBreaker, CircuitBreakerConfig

logger = logging.getLogger(__name__)

class EnhancedAIClient:
    """
    Enhanced AI client that can dynamically call tools based on context.
    Integrates MCP tools, data providers, and analysis functions.
    """
    
    def __init__(
        self,
        api_key: str,
        model: str = "gpt-4o-mini",
        tool_registry: Optional[AIToolRegistry] = None,
        mcp_tool_manager: Optional[MCPToolManager] = None
    ):
        """Initialize the enhanced AI client."""
        self.api_key = api_key
        self.model = model
        self.client = AsyncOpenAI(api_key=api_key)
        
        # Tool management
        self.tool_registry = tool_registry or AIToolRegistry()
        self.mcp_tool_manager = mcp_tool_manager
        
        # Rate limiting and circuit breaker
        self.rate_limiter = RateLimitHandler()
        self.circuit_breaker = CircuitBreaker(
            name="enhanced_ai_client",
            config=CircuitBreakerConfig(
                failure_threshold=5,
                timeout=60.0,
                recovery_timeout=30.0,
                expected_exceptions=(Exception,)
            )
        )
        
        # Configuration
        self.max_tool_calls = 10
        self.tool_timeout = 30.0
        
        logger.info(f"🤖 Enhanced AI Client initialized with model: {model}")
    
    def _create_tool_calling_prompt(self, query: str, available_tools: List[Dict[str, Any]]) -> str:
        """Create a prompt that enables tool calling for the AI."""
        tools_description = "\n".join([
            f"- {tool['name']}: {tool['description']}"
            for tool in available_tools
        ])
        
        return f"""
You are an advanced trading AI assistant with access to real-time market data and analysis tools.

AVAILABLE TOOLS:
{tools_description}

INSTRUCTIONS:
1. Analyze the user's query and determine which tools you need to call
2. You can call multiple tools in sequence to gather comprehensive data
3. Always use real data from the tools - never make up prices, dates, or technical values
4. If you need current prices, use get_global_quote
5. If you need technical analysis, use get_rsi, get_macd, get_bbands, etc.
6. If you need news sentiment, use get_news_sentiment
7. If you need comprehensive analysis, use get_comprehensive_analysis
8. Always explain your reasoning and cite the data sources

USER QUERY: {query}

Please analyze this query and call the appropriate tools to provide a comprehensive answer.
"""
    
    def _create_tool_calling_messages(self, query: str, available_tools: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Create messages for tool calling."""
        system_prompt = self._create_tool_calling_prompt(query, available_tools)
        
        return [
            {
                "role": "system",
                "content": system_prompt
            },
            {
                "role": "user",
                "content": query
            }
        ]
    
    def _create_tool_definitions(self, available_tools: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Create tool definitions for OpenAI function calling."""
        tools = []
        
        for tool in available_tools:
            tools.append({
                "type": "function",
                "function": {
                    "name": tool["name"],
                    "description": tool["description"],
                    "parameters": tool["parameters"]
                }
            })
        
        return tools
    
    async def _execute_tool_call(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a tool call."""
        try:
            logger.info(f"🔧 Executing tool: {tool_name} with params: {list(parameters.keys())}")
            
            # Execute with timeout
            result = await asyncio.wait_for(
                self.tool_registry.execute_tool(tool_name, parameters),
                timeout=self.tool_timeout
            )
            
            logger.info(f"✅ Tool {tool_name} executed successfully")
            return result
        
        except asyncio.TimeoutError:
            logger.error(f"⏰ Tool {tool_name} timed out after {self.tool_timeout}s")
            return {
                "success": False,
                "error": f"Tool {tool_name} timed out",
                "tool_name": tool_name
            }
        
        except Exception as e:
            logger.error(f"❌ Tool {tool_name} execution failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "tool_name": tool_name
            }
    
    async def generate_response_with_tools(self, query: str, max_iterations: int = 5) -> Dict[str, Any]:
        """Generate AI response with dynamic tool calling."""
        start_time = datetime.now()
        
        try:
            # Check circuit breaker
            if not await self.circuit_breaker.can_execute():
                raise Exception("Circuit breaker is OPEN")
            
            # Get available tools
            available_tools = self.tool_registry.get_available_tools()
            
            if not available_tools:
                logger.warning("No tools available, falling back to basic response")
                return await self._generate_basic_response(query)
            
            # Create messages and tool definitions
            messages = self._create_tool_calling_messages(query, available_tools)
            tools = self._create_tool_definitions(available_tools)
            
            # Track tool calls
            tool_calls_made = []
            total_tool_calls = 0
            
            # Initial AI call
            logger.info("🤖 Making initial AI call with tool access")
            
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                tools=tools,
                tool_choice="auto",
                temperature=0.1,
                max_tokens=2000
            )
            
            # Process response and tool calls
            current_messages = messages.copy()
            current_messages.append({
                "role": "assistant",
                "content": response.choices[0].message.content,
                "tool_calls": [
                    {
                        "id": tool_call.id,
                        "type": tool_call.type,
                        "function": {
                            "name": tool_call.function.name,
                            "arguments": tool_call.function.arguments
                        }
                    }
                    for tool_call in response.choices[0].message.tool_calls or []
                ]
            })
            
            # Execute tool calls
            for tool_call in response.choices[0].message.tool_calls or []:
                if total_tool_calls >= self.max_tool_calls:
                    logger.warning(f"Max tool calls ({self.max_tool_calls}) reached")
                    break
                
                tool_name = tool_call.function.name
                tool_args = json.loads(tool_call.function.arguments)
                
                # Execute tool
                tool_result = await self._execute_tool_call(tool_name, tool_args)
                tool_calls_made.append({
                    "tool": tool_name,
                    "args": tool_args,
                    "result": tool_result
                })
                
                total_tool_calls += 1
                
                # Add tool result to messages
                current_messages.append({
                    "role": "tool",
                    "tool_call_id": tool_call.id,
                    "content": json.dumps(tool_result)
                })
            
            # Generate final response with tool results
            if tool_calls_made:
                logger.info(f"🔧 Made {len(tool_calls_made)} tool calls, generating final response")
                
                final_response = await self.client.chat.completions.create(
                    model=self.model,
                    messages=current_messages,
                    temperature=0.1,
                    max_tokens=2000
                )
                
                final_content = final_response.choices[0].message.content
            else:
                final_content = response.choices[0].message.content
            
            # Update circuit breaker
            await self.circuit_breaker.success()
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return {
                "success": True,
                "response": final_content,
                "tool_calls": tool_calls_made,
                "total_tool_calls": total_tool_calls,
                "processing_time": processing_time,
                "tools_available": len(available_tools),
                "model_used": self.model
            }
        
        except Exception as e:
            logger.error(f"❌ Enhanced AI response failed: {e}")
            await self.circuit_breaker.failure()
            
            return {
                "success": False,
                "response": f"Sorry, I encountered an error: {str(e)}",
                "error": str(e),
                "processing_time": (datetime.now() - start_time).total_seconds()
            }
    
    async def _generate_basic_response(self, query: str) -> Dict[str, Any]:
        """Generate a basic response without tools."""
        try:
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are a helpful trading assistant. Provide general guidance but note that you don't have access to real-time data."
                    },
                    {
                        "role": "user",
                        "content": query
                    }
                ],
                temperature=0.1,
                max_tokens=1000
            )
            
            return {
                "success": True,
                "response": response.choices[0].message.content,
                "tool_calls": [],
                "total_tool_calls": 0,
                "tools_available": 0,
                "model_used": self.model
            }
        
        except Exception as e:
            logger.error(f"❌ Basic AI response failed: {e}")
            return {
                "success": False,
                "response": f"Sorry, I encountered an error: {str(e)}",
                "error": str(e)
            }
    
    async def get_available_tools(self) -> Dict[str, Any]:
        """Get information about available tools."""
        return self.tool_registry.get_tool_schema()
    
    async def get_tool_stats(self) -> Dict[str, Any]:
        """Get tool execution statistics."""
        return self.tool_registry.get_execution_stats()
    
    async def close(self):
        """Close the AI client and tool managers."""
        if self.mcp_tool_manager:
            await self.mcp_tool_manager.close()
        
        if hasattr(self, 'client'):
            await self.client.close()
