"""
Cross-Validation AI System

This module implements a system that uses multiple AI models to validate important
financial decisions and flag disagreements for enhanced trustworthiness.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
from enum import Enum

from src.shared.error_handling.logging import get_logger
from src.shared.ai_services.smart_model_router import SmartModelRouter
from src.shared.ai_services.unified_ai_processor import UnifiedAIProcessor as RobustFinancialAnalyzer

logger = get_logger(__name__)

class ValidationImportance(Enum):
    """Importance levels for cross-validation"""
    CRITICAL = "critical"    # Trading recommendations, major financial decisions
    HIGH = "high"           # Technical analysis, price predictions
    MEDIUM = "medium"       # General market commentary
    LOW = "low"            # Educational content, basic information

class ConsensusLevel(Enum):
    """Consensus levels between AI models"""
    STRONG_AGREEMENT = "strong_agreement"      # 90%+ agreement
    MODERATE_AGREEMENT = "moderate_agreement"  # 70-89% agreement
    WEAK_AGREEMENT = "weak_agreement"          # 50-69% agreement
    DISAGREEMENT = "disagreement"              # <50% agreement

@dataclass
class ModelResponse:
    """Response from a single AI model"""
    model_name: str
    response: str
    confidence: float
    processing_time: float
    key_points: List[str]
    numerical_claims: Dict[str, float]
    sentiment: str  # bullish, bearish, neutral
    recommendation: Optional[str] = None

@dataclass
class CrossValidationResult:
    """Result of cross-validation across multiple AI models"""
    consensus_level: ConsensusLevel
    consensus_confidence: float
    primary_response: str
    model_responses: List[ModelResponse]
    disagreements: List[Dict[str, Any]]
    consensus_points: List[str]
    final_recommendation: Optional[str]
    validation_timestamp: datetime
    requires_human_review: bool

class CrossValidationAI:
    """
    Cross-validation system that uses multiple AI models to validate responses
    and detect disagreements for enhanced trustworthiness
    """
    
    def __init__(self):
        self.model_router = SmartModelRouter()
        self.robust_analyzer = RobustFinancialAnalyzer()
        
        # Models to use for cross-validation (all via OpenRouter)
        self.validation_models = [
            "deepseek/deepseek-r1-0528:free",      # Primary analysis model (via OpenRouter)
            "moonshotai/kimi-k2:free",             # Heavy analysis model (via OpenRouter)
            "google/gemini-2.0-flash-exp:free",    # Fallback model (via OpenRouter)
        ]
        
        # Thresholds for different validation levels
        self.validation_thresholds = {
            ValidationImportance.CRITICAL: 0.85,   # Require 85% consensus
            ValidationImportance.HIGH: 0.75,       # Require 75% consensus
            ValidationImportance.MEDIUM: 0.65,     # Require 65% consensus
            ValidationImportance.LOW: 0.55         # Require 55% consensus
        }
    
    async def cross_validate_response(self, 
                                    query: str, 
                                    context: Dict[str, Any],
                                    importance: ValidationImportance = ValidationImportance.MEDIUM) -> CrossValidationResult:
        """
        Cross-validate a response using multiple AI models
        
        Args:
            query: The user query to validate
            context: Context including symbols, market data, etc.
            importance: Importance level determining validation rigor
            
        Returns:
            CrossValidationResult with consensus analysis
        """
        logger.info(f"🔄 Starting cross-validation for {importance.value} importance query")
        
        # Determine how many models to use based on importance
        models_to_use = self._select_validation_models(importance)
        
        # Get responses from multiple models
        model_responses = await self._get_model_responses(query, context, models_to_use)
        
        # Analyze consensus
        consensus_analysis = self._analyze_consensus(model_responses)
        
        # Detect disagreements
        disagreements = self._detect_disagreements(model_responses)
        
        # Generate consensus response
        consensus_response = self._generate_consensus_response(model_responses, consensus_analysis)
        
        # Determine if human review is needed
        requires_review = self._requires_human_review(consensus_analysis, disagreements, importance)
        
        result = CrossValidationResult(
            consensus_level=consensus_analysis['level'],
            consensus_confidence=consensus_analysis['confidence'],
            primary_response=consensus_response,
            model_responses=model_responses,
            disagreements=disagreements,
            consensus_points=consensus_analysis['consensus_points'],
            final_recommendation=consensus_analysis.get('recommendation'),
            validation_timestamp=datetime.now(),
            requires_human_review=requires_review
        )
        
        logger.info(f"✅ Cross-validation complete: {result.consensus_level.value}, "
                   f"confidence: {result.consensus_confidence:.1f}%, "
                   f"review needed: {requires_review}")
        
        return result
    
    def _select_validation_models(self, importance: ValidationImportance) -> List[str]:
        """Select which models to use based on importance level"""
        if importance == ValidationImportance.CRITICAL:
            return self.validation_models  # Use all models
        elif importance == ValidationImportance.HIGH:
            return self.validation_models[:2]  # Use top 2 models
        else:
            return self.validation_models[:1]  # Use primary model only
    
    async def _get_model_responses(self, query: str, context: Dict[str, Any], models: List[str]) -> List[ModelResponse]:
        """Get responses from multiple AI models"""
        responses = []
        
        # Create tasks for parallel processing
        tasks = []
        for model_name in models:
            task = self._get_single_model_response(query, context, model_name)
            tasks.append(task)
        
        # Execute in parallel with timeout
        try:
            results = await asyncio.wait_for(asyncio.gather(*tasks, return_exceptions=True), timeout=30.0)
            
            for result in results:
                if isinstance(result, ModelResponse):
                    responses.append(result)
                elif isinstance(result, Exception):
                    logger.warning(f"Model response failed: {result}")
        
        except asyncio.TimeoutError:
            logger.warning("Cross-validation timeout - some models may not have responded")
        
        return responses
    
    async def _get_single_model_response(self, query: str, context: Dict[str, Any], model_name: str) -> ModelResponse:
        """Get response from a single AI model"""
        start_time = datetime.now()
        
        try:
            # Use the robust analyzer (model selection handled internally)
            result = await self.robust_analyzer.process_query(
                query=query,
                context=context
            )
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # Extract key information from response
            # Handle both dict and ProcessingResult objects
            if hasattr(result, 'response'):
                # ProcessingResult object
                response_text = result.response
                confidence = result.confidence
            else:
                # Dictionary object
                response_text = result.get('response', '')
                confidence = result.get('confidence', 0.5)

            key_points = self._extract_key_points(response_text)
            numerical_claims = self._extract_numerical_claims(response_text)
            sentiment = self._extract_sentiment(response_text)
            recommendation = self._extract_recommendation(response_text)

            return ModelResponse(
                model_name=model_name,
                response=response_text,
                confidence=confidence,
                processing_time=processing_time,
                key_points=key_points,
                numerical_claims=numerical_claims,
                sentiment=sentiment,
                recommendation=recommendation
            )
        
        except Exception as e:
            logger.error(f"Failed to get response from {model_name}: {e}")
            # Return empty response to avoid breaking the validation
            return ModelResponse(
                model_name=model_name,
                response="",
                confidence=0.0,
                processing_time=0.0,
                key_points=[],
                numerical_claims={},
                sentiment="neutral"
            )
    
    def _analyze_consensus(self, responses: List[ModelResponse]) -> Dict[str, Any]:
        """Analyze consensus between model responses"""
        if not responses:
            return {
                'level': ConsensusLevel.DISAGREEMENT,
                'confidence': 0.0,
                'consensus_points': [],
                'recommendation': None
            }
        
        # Analyze sentiment consensus
        sentiments = [r.sentiment for r in responses if r.sentiment]
        sentiment_consensus = self._calculate_sentiment_consensus(sentiments)
        
        # Analyze key points consensus
        all_points = []
        for response in responses:
            all_points.extend(response.key_points)
        consensus_points = self._find_consensus_points(all_points)
        
        # Analyze recommendation consensus
        recommendations = [r.recommendation for r in responses if r.recommendation]
        recommendation_consensus = self._calculate_recommendation_consensus(recommendations)
        
        # Calculate overall consensus confidence
        confidence_scores = [r.confidence for r in responses if r.confidence > 0]
        avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0.0
        
        # Determine consensus level
        consensus_score = (sentiment_consensus + len(consensus_points) * 0.1 + recommendation_consensus) / 3
        
        if consensus_score >= 0.9:
            level = ConsensusLevel.STRONG_AGREEMENT
        elif consensus_score >= 0.7:
            level = ConsensusLevel.MODERATE_AGREEMENT
        elif consensus_score >= 0.5:
            level = ConsensusLevel.WEAK_AGREEMENT
        else:
            level = ConsensusLevel.DISAGREEMENT
        
        return {
            'level': level,
            'confidence': avg_confidence * consensus_score,
            'consensus_points': consensus_points,
            'recommendation': recommendation_consensus if recommendation_consensus > 0.7 else None
        }
    
    def _detect_disagreements(self, responses: List[ModelResponse]) -> List[Dict[str, Any]]:
        """Detect significant disagreements between models"""
        disagreements = []
        
        if len(responses) < 2:
            return disagreements
        
        # Check for sentiment disagreements
        sentiments = [r.sentiment for r in responses if r.sentiment]
        if len(set(sentiments)) > 1:
            disagreements.append({
                'type': 'sentiment_disagreement',
                'description': f"Models disagree on sentiment: {', '.join(set(sentiments))}",
                'severity': 'medium'
            })
        
        # Check for numerical claim disagreements
        numerical_claims = {}
        for response in responses:
            for claim, value in response.numerical_claims.items():
                if claim not in numerical_claims:
                    numerical_claims[claim] = []
                numerical_claims[claim].append(value)
        
        for claim, values in numerical_claims.items():
            if len(values) > 1:
                value_range = max(values) - min(values)
                avg_value = sum(values) / len(values)
                if avg_value > 0 and value_range / avg_value > 0.1:  # More than 10% difference
                    disagreements.append({
                        'type': 'numerical_disagreement',
                        'description': f"Models disagree on {claim}: range {min(values):.2f} - {max(values):.2f}",
                        'severity': 'high'
                    })
        
        return disagreements
    
    def _generate_consensus_response(self, responses: List[ModelResponse], consensus_analysis: Dict[str, Any]) -> str:
        """Generate a consensus response from multiple model responses"""
        if not responses:
            return "Unable to generate consensus response - no valid model responses received."
        
        # Use the response from the most confident model as base
        best_response = max(responses, key=lambda r: r.confidence)
        
        # Add consensus information
        consensus_info = [
            f"\n---",
            f"**Cross-Validation Results**",
            f"🤖 **Models Consulted**: {len(responses)}",
            f"🎯 **Consensus Level**: {consensus_analysis['level'].value.replace('_', ' ').title()}",
            f"📊 **Consensus Confidence**: {consensus_analysis['confidence']:.1f}%"
        ]
        
        if consensus_analysis['consensus_points']:
            consensus_info.append(f"✅ **Key Agreements**: {', '.join(consensus_analysis['consensus_points'][:3])}")
        
        return best_response.response + "\n" + "\n".join(consensus_info)
    
    def _requires_human_review(self, consensus_analysis: Dict[str, Any], 
                              disagreements: List[Dict[str, Any]], 
                              importance: ValidationImportance) -> bool:
        """Determine if human review is required"""
        # Always require review for critical importance with disagreements
        if importance == ValidationImportance.CRITICAL and disagreements:
            return True
        
        # Require review if consensus is below threshold
        threshold = self.validation_thresholds[importance]
        if consensus_analysis['confidence'] < threshold:
            return True
        
        # Require review for high-severity disagreements
        high_severity_disagreements = [d for d in disagreements if d.get('severity') == 'high']
        if high_severity_disagreements:
            return True
        
        return False
    
    def _extract_key_points(self, response: str) -> List[str]:
        """Extract key points from response"""
        # Simple implementation - look for bullet points or numbered lists
        lines = response.split('\n')
        key_points = []
        
        for line in lines:
            line = line.strip()
            if line.startswith(('•', '-', '*')) or (line and line[0].isdigit() and '.' in line[:3]):
                key_points.append(line)
        
        return key_points[:5]  # Return top 5 key points
    
    def _extract_numerical_claims(self, response: str) -> Dict[str, float]:
        """Extract numerical claims from response"""
        import re
        claims = {}
        
        # Extract price mentions
        price_matches = re.findall(r'\$(\d+(?:\.\d{2})?)', response)
        if price_matches:
            claims['price'] = float(price_matches[0])
        
        # Extract percentage mentions
        percent_matches = re.findall(r'(\d+(?:\.\d+)?)\s*%', response)
        if percent_matches:
            claims['percentage'] = float(percent_matches[0])
        
        return claims
    
    def _extract_sentiment(self, response: str) -> str:
        """Extract sentiment from response"""
        response_lower = response.lower()
        
        bullish_words = ['bullish', 'positive', 'upward', 'growth', 'increase', 'buy']
        bearish_words = ['bearish', 'negative', 'downward', 'decline', 'decrease', 'sell']
        
        bullish_count = sum(1 for word in bullish_words if word in response_lower)
        bearish_count = sum(1 for word in bearish_words if word in response_lower)
        
        if bullish_count > bearish_count:
            return 'bullish'
        elif bearish_count > bullish_count:
            return 'bearish'
        else:
            return 'neutral'
    
    def _extract_recommendation(self, response: str) -> Optional[str]:
        """Extract trading recommendation from response"""
        response_lower = response.lower()
        
        if any(word in response_lower for word in ['buy', 'purchase', 'invest']):
            return 'buy'
        elif any(word in response_lower for word in ['sell', 'exit', 'avoid']):
            return 'sell'
        elif any(word in response_lower for word in ['hold', 'maintain', 'keep']):
            return 'hold'
        
        return None
    
    def _calculate_sentiment_consensus(self, sentiments: List[str]) -> float:
        """Calculate sentiment consensus score"""
        if not sentiments:
            return 0.0
        
        sentiment_counts = {}
        for sentiment in sentiments:
            sentiment_counts[sentiment] = sentiment_counts.get(sentiment, 0) + 1
        
        max_count = max(sentiment_counts.values())
        return max_count / len(sentiments)
    
    def _find_consensus_points(self, all_points: List[str]) -> List[str]:
        """Find points that appear in multiple responses"""
        # Simple implementation - look for similar phrases
        consensus_points = []
        
        # This is a simplified version - in practice, you'd use more sophisticated NLP
        point_counts = {}
        for point in all_points:
            # Normalize the point
            normalized = point.lower().strip('•-*0123456789. ')
            point_counts[normalized] = point_counts.get(normalized, 0) + 1
        
        # Return points that appear more than once
        for point, count in point_counts.items():
            if count > 1:
                consensus_points.append(point)
        
        return consensus_points[:3]  # Return top 3 consensus points
    
    def _calculate_recommendation_consensus(self, recommendations: List[str]) -> float:
        """Calculate recommendation consensus score"""
        if not recommendations:
            return 0.0
        
        rec_counts = {}
        for rec in recommendations:
            rec_counts[rec] = rec_counts.get(rec, 0) + 1
        
        max_count = max(rec_counts.values())
        return max_count / len(recommendations)
