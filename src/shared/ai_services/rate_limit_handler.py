"""
AI Service Rate Limit Handler
============================

Handles rate limiting for AI services with intelligent backoff, 
provider switching, and graceful degradation.
"""

import asyncio
import time
import logging
from typing import Dict, Optional, List, Any, Callable
from dataclasses import dataclass
from enum import Enum
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class RateLimitStrategy(Enum):
    """Strategies for handling rate limits"""
    EXPONENTIAL_BACKOFF = "exponential_backoff"
    PROVIDER_SWITCHING = "provider_switching"
    GRACEFUL_DEGRADATION = "graceful_degradation"
    CIRCUIT_BREAKER = "circuit_breaker"

@dataclass
class RateLimitInfo:
    """Information about rate limiting"""
    provider: str
    limit_type: str  # "requests_per_minute", "tokens_per_minute", etc.
    reset_time: Optional[datetime] = None
    retry_after: Optional[int] = None
    remaining_requests: Optional[int] = None

@dataclass
class ProviderConfig:
    """Configuration for an AI provider"""
    name: str
    priority: int  # Lower number = higher priority
    rate_limit_buffer: float = 0.1  # 10% buffer
    max_retries: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    enabled: bool = True

class RateLimitHandler:
    """Handles rate limiting across multiple AI providers"""
    
    def __init__(self):
        # All AI models are accessed through OpenRouter
        self.providers = {
            # Primary provider - OpenRouter handles all specialized models
            "openrouter": ProviderConfig("openrouter", 1, rate_limit_buffer=0.1, max_retries=3),

            # Direct provider fallbacks (if needed)
            "anthropic": ProviderConfig("anthropic", 2, rate_limit_buffer=0.05, max_retries=2),
            "google": ProviderConfig("google", 3, rate_limit_buffer=0.15, max_retries=3),

            # Local fallback
            "fallback": ProviderConfig("fallback", 99, rate_limit_buffer=0.0, max_retries=1)
        }
        
        # Track rate limit status per provider
        self.rate_limit_status: Dict[str, RateLimitInfo] = {}
        self.last_request_time: Dict[str, float] = {}
        self.request_counts: Dict[str, List[float]] = {}
        
        # Circuit breaker state
        self.circuit_open: Dict[str, bool] = {}
        self.circuit_open_time: Dict[str, float] = {}
        
    async def execute_with_rate_limit_handling(
        self, 
        ai_function: Callable,
        preferred_provider: str = "openrouter",
        fallback_providers: Optional[List[str]] = None,
        **kwargs
    ) -> Any:
        """
        Execute an AI function with intelligent rate limit handling
        
        Args:
            ai_function: The AI function to execute
            preferred_provider: Preferred AI provider
            fallback_providers: List of fallback providers
            **kwargs: Arguments to pass to the AI function
            
        Returns:
            Result from AI function or fallback response
        """
        if fallback_providers is None:
            fallback_providers = ["anthropic", "google", "fallback"]
        
        providers_to_try = [preferred_provider] + fallback_providers
        
        for provider in providers_to_try:
            if not self._is_provider_available(provider):
                logger.debug(f"Provider {provider} not available, trying next")
                continue
                
            try:
                # Check if we need to wait due to rate limiting
                wait_time = self._calculate_wait_time(provider)
                if wait_time > 0:
                    logger.info(f"Rate limit wait for {provider}: {wait_time:.2f}s")
                    await asyncio.sleep(wait_time)
                
                # Execute the AI function
                result = await self._execute_with_provider(ai_function, provider, **kwargs)
                
                # Record successful request
                self._record_success(provider)
                return result
                
            except Exception as e:
                error_str = str(e).lower()
                
                if "rate limit" in error_str or "429" in error_str:
                    # Handle rate limiting
                    await self._handle_rate_limit(provider, e)
                    continue
                    
                elif "timeout" in error_str or "503" in error_str:
                    # Handle service unavailable
                    await self._handle_service_unavailable(provider, e)
                    continue
                    
                else:
                    # Other errors - try next provider
                    logger.warning(f"Provider {provider} failed with error: {e}")
                    self._record_failure(provider)
                    continue
        
        # All providers failed - return graceful fallback
        return await self._generate_graceful_fallback(**kwargs)
    
    def _is_provider_available(self, provider: str) -> bool:
        """Check if a provider is available (not circuit broken)"""
        if provider not in self.providers:
            return False
            
        if not self.providers[provider].enabled:
            return False
            
        # Check circuit breaker
        if self.circuit_open.get(provider, False):
            # Check if circuit should be reset
            open_time = self.circuit_open_time.get(provider, 0)
            if time.time() - open_time > 300:  # 5 minute circuit breaker
                self.circuit_open[provider] = False
                logger.info(f"Circuit breaker reset for provider {provider}")
                return True
            return False
            
        return True
    
    def _calculate_wait_time(self, provider: str) -> float:
        """Calculate how long to wait before making a request"""
        current_time = time.time()
        last_request = self.last_request_time.get(provider, 0)
        
        # Minimum time between requests (basic rate limiting)
        min_interval = 1.0  # 1 second minimum between requests
        time_since_last = current_time - last_request
        
        if time_since_last < min_interval:
            return min_interval - time_since_last
            
        # Check if we have rate limit info
        rate_limit_info = self.rate_limit_status.get(provider)
        if rate_limit_info and rate_limit_info.reset_time:
            if datetime.now() < rate_limit_info.reset_time:
                # Still in rate limit period
                time_until_reset = (rate_limit_info.reset_time - datetime.now()).total_seconds()
                return min(time_until_reset, 60.0)  # Max 1 minute wait
                
        return 0.0
    
    async def _execute_with_provider(self, ai_function: Callable, provider: str, **kwargs) -> Any:
        """Execute AI function with specific provider"""
        # Record request time
        self.last_request_time[provider] = time.time()
        
        # Add provider info to kwargs if the function supports it
        if 'provider' in ai_function.__code__.co_varnames:
            kwargs['provider'] = provider
            
        # Execute the function
        result = await ai_function(**kwargs)
        return result
    
    async def _handle_rate_limit(self, provider: str, error: Exception):
        """Handle rate limit error"""
        logger.warning(f"Rate limit hit for provider {provider}: {error}")
        
        # Extract retry-after if available
        retry_after = self._extract_retry_after(str(error))
        reset_time = datetime.now() + timedelta(seconds=retry_after or 60)
        
        # Store rate limit info
        self.rate_limit_status[provider] = RateLimitInfo(
            provider=provider,
            limit_type="requests_per_minute",
            reset_time=reset_time,
            retry_after=retry_after
        )
        
        # Don't open circuit breaker for rate limits - they're temporary
        logger.info(f"Provider {provider} rate limited until {reset_time}")
    
    async def _handle_service_unavailable(self, provider: str, error: Exception):
        """Handle service unavailable error"""
        logger.warning(f"Service unavailable for provider {provider}: {error}")
        
        # Open circuit breaker for service issues
        self.circuit_open[provider] = True
        self.circuit_open_time[provider] = time.time()
        
        logger.info(f"Circuit breaker opened for provider {provider}")
    
    def _record_success(self, provider: str):
        """Record successful request"""
        current_time = time.time()
        
        # Track request times for rate limiting
        if provider not in self.request_counts:
            self.request_counts[provider] = []
        
        self.request_counts[provider].append(current_time)
        
        # Keep only last minute of requests
        cutoff_time = current_time - 60
        self.request_counts[provider] = [
            t for t in self.request_counts[provider] if t > cutoff_time
        ]
        
        # Clear rate limit status on success
        if provider in self.rate_limit_status:
            del self.rate_limit_status[provider]
    
    def _record_failure(self, provider: str):
        """Record failed request"""
        # Could implement failure tracking here
        pass
    
    def _extract_retry_after(self, error_message: str) -> Optional[int]:
        """Extract retry-after time from error message"""
        import re
        
        # Look for retry-after patterns
        patterns = [
            r'retry.*?(\d+).*?second',
            r'wait.*?(\d+).*?second',
            r'retry.*?(\d+)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, error_message.lower())
            if match:
                return int(match.group(1))
                
        return None
    
    async def _generate_graceful_fallback(self, **kwargs) -> Any:
        """Generate a graceful fallback response when all providers fail"""
        logger.warning("All AI providers failed, generating fallback response")
        
        # Return a structured fallback response
        return {
            "success": False,
            "error": "AI services temporarily unavailable",
            "fallback": True,
            "message": "I'm experiencing high demand right now. Please try again in a few moments.",
            "timestamp": datetime.now().isoformat()
        }
    
    def get_provider_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all providers"""
        status = {}
        current_time = time.time()
        
        for provider_name, config in self.providers.items():
            rate_limit_info = self.rate_limit_status.get(provider_name)
            circuit_open = self.circuit_open.get(provider_name, False)
            
            status[provider_name] = {
                "enabled": config.enabled,
                "available": self._is_provider_available(provider_name),
                "circuit_open": circuit_open,
                "rate_limited": rate_limit_info is not None,
                "last_request": self.last_request_time.get(provider_name, 0),
                "requests_last_minute": len(self.request_counts.get(provider_name, []))
            }
            
            if rate_limit_info:
                status[provider_name]["rate_limit_reset"] = rate_limit_info.reset_time.isoformat() if rate_limit_info.reset_time else None
                
        return status

# Global rate limit handler
rate_limit_handler = RateLimitHandler()

# Convenience decorator
def with_rate_limit_handling(preferred_provider: str = "openrouter"):
    """Decorator to add rate limit handling to AI functions"""
    def decorator(func: Callable) -> Callable:
        async def wrapper(*args, **kwargs):
            return await rate_limit_handler.execute_with_rate_limit_handling(
                func, preferred_provider, **kwargs
            )
        return wrapper
    return decorator
