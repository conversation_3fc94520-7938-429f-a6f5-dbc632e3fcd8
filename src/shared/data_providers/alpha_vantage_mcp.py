"""
Alpha Vantage MCP (Model Context Protocol) client integration.
Provides native AI access to Alpha Vantage data through MCP protocol.
"""

import os
import json
import asyncio
import logging
from typing import Dict, Any, Optional, List, Union
from datetime import datetime, timedelta
import aiohttp
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class MCPTool:
    """Represents an MCP tool definition."""
    name: str
    description: str
    input_schema: Dict[str, Any]

class AlphaVantageMCPClient:
    """
    Alpha Vantage MCP client for native AI integration.
    Provides direct access to Alpha Vantage tools through MCP protocol.
    """
    
    def __init__(self, api_key: Optional[str] = None):
        """Initialize the MCP client."""
        self.api_key = api_key or os.getenv('ALPHA_VANTAGE_API_KEY', '')
        self.mcp_url = f"https://mcp.alphavantage.co/mcp?apikey={self.api_key}"
        self.session = None
        self.is_configured = bool(self.api_key)
        
        # Rate limiting: 5 calls per minute for free tier
        self.calls_per_minute = 5
        self.call_timestamps: List[float] = []
        self.rate_limiter = asyncio.Semaphore(1)
        
        if not self.is_configured:
            logger.warning("Alpha Vantage MCP client not configured - API key missing")
    
    async def _ensure_session(self):
        """Ensure HTTP session is initialized."""
        if self.session is None:
            timeout = aiohttp.ClientTimeout(total=30.0)
            connector = aiohttp.TCPConnector(limit=5, limit_per_host=2)
            self.session = aiohttp.ClientSession(
                timeout=timeout,
                connector=connector
            )
    
    async def _rate_limit_check(self):
        """Check and enforce rate limiting."""
        if not self.is_configured:
            return False
            
        now = datetime.now().timestamp()
        
        # Remove timestamps older than 1 minute
        self.call_timestamps = [ts for ts in self.call_timestamps if now - ts < 60]
        
        if len(self.call_timestamps) >= self.calls_per_minute:
            sleep_time = 60 - (now - self.call_timestamps[0])
            if sleep_time > 0:
                logger.warning(f"Rate limit reached, sleeping for {sleep_time:.1f} seconds")
                await asyncio.sleep(sleep_time)
                # Clean up old timestamps after sleep
                now = datetime.now().timestamp()
                self.call_timestamps = [ts for ts in self.call_timestamps if now - ts < 60]
        
        self.call_timestamps.append(now)
        return True
    
    async def _make_mcp_request(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Make a request to the MCP server."""
        if not self.is_configured:
            raise ValueError("Alpha Vantage MCP client not configured")
        
        await self._ensure_session()
        
        if not await self._rate_limit_check():
            raise Exception("Rate limit exceeded")
        
        # MCP request format
        mcp_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "tools/call",
            "params": {
                "name": tool_name,
                "arguments": parameters
            }
        }
        
        try:
            async with self.rate_limiter:
                async with self.session.post(
                    self.mcp_url,
                    json=mcp_request,
                    headers={"Content-Type": "application/json"}
                ) as response:
                    if response.status != 200:
                        raise Exception(f"MCP request failed with status {response.status}")
                    
                    result = await response.json()
                    
                    if "error" in result:
                        raise Exception(f"MCP error: {result['error']}")
                    
                    return result.get("result", {})
        
        except Exception as e:
            logger.error(f"MCP request failed: {e}")
            raise
    
    # Core Stock APIs
    async def get_global_quote(self, symbol: str) -> Dict[str, Any]:
        """Get real-time quote for a symbol."""
        return await self._make_mcp_request("GLOBAL_QUOTE", {"symbol": symbol})
    
    async def get_time_series_daily(self, symbol: str, outputsize: str = "compact") -> Dict[str, Any]:
        """Get daily time series data."""
        return await self._make_mcp_request("TIME_SERIES_DAILY", {
            "symbol": symbol,
            "outputsize": outputsize
        })
    
    async def get_time_series_intraday(self, symbol: str, interval: str = "5min", outputsize: str = "compact") -> Dict[str, Any]:
        """Get intraday time series data."""
        return await self._make_mcp_request("TIME_SERIES_INTRADAY", {
            "symbol": symbol,
            "interval": interval,
            "outputsize": outputsize
        })
    
    async def get_realtime_bulk_quotes(self, symbols: List[str]) -> Dict[str, Any]:
        """Get real-time quotes for multiple symbols."""
        return await self._make_mcp_request("REALTIME_BULK_QUOTES", {
            "symbols": ",".join(symbols)
        })
    
    # Technical Indicators
    async def get_rsi(self, symbol: str, interval: str = "daily", time_period: int = 14, series_type: str = "close") -> Dict[str, Any]:
        """Get RSI (Relative Strength Index) values."""
        return await self._make_mcp_request("RSI", {
            "symbol": symbol,
            "interval": interval,
            "time_period": time_period,
            "series_type": series_type
        })
    
    async def get_macd(self, symbol: str, interval: str = "daily", series_type: str = "close") -> Dict[str, Any]:
        """Get MACD (Moving Average Convergence Divergence) values."""
        return await self._make_mcp_request("MACD", {
            "symbol": symbol,
            "interval": interval,
            "series_type": series_type
        })
    
    async def get_bbands(self, symbol: str, interval: str = "daily", time_period: int = 20, series_type: str = "close") -> Dict[str, Any]:
        """Get Bollinger Bands values."""
        return await self._make_mcp_request("BBANDS", {
            "symbol": symbol,
            "interval": interval,
            "time_period": time_period,
            "series_type": series_type
        })
    
    async def get_sma(self, symbol: str, interval: str = "daily", time_period: int = 20, series_type: str = "close") -> Dict[str, Any]:
        """Get Simple Moving Average values."""
        return await self._make_mcp_request("SMA", {
            "symbol": symbol,
            "interval": interval,
            "time_period": time_period,
            "series_type": series_type
        })
    
    async def get_ema(self, symbol: str, interval: str = "daily", time_period: int = 20, series_type: str = "close") -> Dict[str, Any]:
        """Get Exponential Moving Average values."""
        return await self._make_mcp_request("EMA", {
            "symbol": symbol,
            "interval": interval,
            "time_period": time_period,
            "series_type": series_type
        })
    
    async def get_stoch(self, symbol: str, interval: str = "daily") -> Dict[str, Any]:
        """Get Stochastic Oscillator values."""
        return await self._make_mcp_request("STOCH", {
            "symbol": symbol,
            "interval": interval
        })
    
    # Alpha Intelligence
    async def get_news_sentiment(self, symbol: str, limit: int = 50) -> Dict[str, Any]:
        """Get news sentiment analysis."""
        return await self._make_mcp_request("NEWS_SENTIMENT", {
            "symbol": symbol,
            "limit": limit
        })
    
    async def get_top_gainers_losers(self) -> Dict[str, Any]:
        """Get top gainers and losers."""
        return await self._make_mcp_request("TOP_GAINERS_LOSERS", {})
    
    # Fundamental Data
    async def get_company_overview(self, symbol: str) -> Dict[str, Any]:
        """Get company overview and fundamentals."""
        return await self._make_mcp_request("COMPANY_OVERVIEW", {"symbol": symbol})
    
    async def get_earnings(self, symbol: str) -> Dict[str, Any]:
        """Get earnings data."""
        return await self._make_mcp_request("EARNINGS", {"symbol": symbol})
    
    # Utility methods
    async def get_technical_analysis(self, symbol: str, indicators: List[str] = None) -> Dict[str, Any]:
        """Get comprehensive technical analysis for a symbol."""
        if indicators is None:
            indicators = ["RSI", "MACD", "BBANDS", "SMA", "EMA", "STOCH"]
        
        results = {}
        
        for indicator in indicators:
            try:
                if indicator == "RSI":
                    results["RSI"] = await self.get_rsi(symbol)
                elif indicator == "MACD":
                    results["MACD"] = await self.get_macd(symbol)
                elif indicator == "BBANDS":
                    results["BBANDS"] = await self.get_bbands(symbol)
                elif indicator == "SMA":
                    results["SMA"] = await self.get_sma(symbol)
                elif indicator == "EMA":
                    results["EMA"] = await self.get_ema(symbol)
                elif indicator == "STOCH":
                    results["STOCH"] = await self.get_stoch(symbol)
            except Exception as e:
                logger.warning(f"Failed to get {indicator} for {symbol}: {e}")
                results[indicator] = {"error": str(e)}
        
        return results
    
    async def get_comprehensive_analysis(self, symbol: str) -> Dict[str, Any]:
        """Get comprehensive analysis including price, technical indicators, and sentiment."""
        try:
            # Get all data in parallel
            tasks = [
                self.get_global_quote(symbol),
                self.get_time_series_daily(symbol),
                self.get_technical_analysis(symbol),
                self.get_news_sentiment(symbol),
                self.get_company_overview(symbol)
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            return {
                "quote": results[0] if not isinstance(results[0], Exception) else {"error": str(results[0])},
                "historical": results[1] if not isinstance(results[1], Exception) else {"error": str(results[1])},
                "technical": results[2] if not isinstance(results[2], Exception) else {"error": str(results[2])},
                "sentiment": results[3] if not isinstance(results[3], Exception) else {"error": str(results[3])},
                "fundamentals": results[4] if not isinstance(results[4], Exception) else {"error": str(results[4])},
                "timestamp": datetime.now().isoformat()
            }
        
        except Exception as e:
            logger.error(f"Comprehensive analysis failed for {symbol}: {e}")
            return {"error": str(e)}
    
    async def close(self):
        """Close the HTTP session."""
        if self.session:
            await self.session.close()
    
    def __del__(self):
        """Cleanup on deletion."""
        if self.session and not self.session.closed:
            asyncio.create_task(self.session.close())
