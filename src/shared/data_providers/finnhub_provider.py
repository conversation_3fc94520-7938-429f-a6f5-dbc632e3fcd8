"""
Finnhub API data provider implementation.
Migrated from src/data/providers/finnhub_provider.py to unified location.
"""

import os
import json
import asyncio
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta

import aiohttp
from aiohttp import ClientSession

from .unified_base import (
    UnifiedDataProvider,
    DataProviderConfig,
    MarketDataRequest,
    MarketDataResponse,
    MarketDataType,
    TimeFrame,
    ProviderStatus,
    ProviderError
)

logger = logging.getLogger(__name__)

class FinnhubProvider(UnifiedDataProvider):
    """
    Finnhub API provider implementation.
    Provides real-time and historical market data from Finnhub.
    """
    
    def __init__(self, config: Optional[DataProviderConfig] = None):
        """Initialize the Finnhub provider."""
        if config is None:
            # Create default config from environment
            config = DataProviderConfig(
                name="finnhub",
                api_key=os.getenv('FINNHUB_API_KEY', ''),
                base_url='https://finnhub.io/api/v1',
                rate_limit=60,  # 60 requests per minute for free tier
                timeout=float(os.getenv('DATA_PROVIDER_TIMEOUT', '10.0'))
            )
        
        self.api_key = config.api_key
        self.base_url = config.base_url
        self.session = None

        super().__init__("finnhub", "api", config.__dict__ if hasattr(config, '__dict__') else config)
        
    def _initialize(self):
        """Initialize the provider."""
        if not self.api_key:
            logger.warning("Finnhub API key not provided")
            self.status = ProviderStatus.DISABLED
            return
        
        self.session = None
        self.status = ProviderStatus.READY
        logger.info("Finnhub provider initialized")
    
    async def _get_session(self) -> ClientSession:
        """Get or create HTTP session."""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession()
        return self.session
    
    async def _make_request(self, endpoint: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """Make a request to Finnhub API."""
        if self.status == ProviderStatus.DISABLED:
            raise ProviderError("Finnhub provider is disabled", "finnhub")
        
        # Check rate limit
        self._check_rate_limit()
        
        # Prepare request
        params = params or {}
        params['token'] = self.api_key
        
        session = await self._get_session()
        url = f"{self.base_url}/{endpoint}"
        
        try:
            async with session.get(url, params=params, timeout=self.config.timeout) as response:
                if response.status == 200:
                    return await response.json()
                elif response.status == 429:
                    self.status = ProviderStatus.RATE_LIMITED
                    raise ProviderError("Rate limit exceeded", "finnhub")
                else:
                    error_text = await response.text()
                    logger.error(f"Finnhub API error: {response.status} - {error_text}")
                    self.error_count += 1
                    raise ProviderError(f"API error: {response.status}", "finnhub")
        except asyncio.TimeoutError:
            self.error_count += 1
            raise ProviderError("Request timeout", "finnhub")
        except Exception as e:
            self.error_count += 1
            logger.error(f"Finnhub request error: {e}")
            raise ProviderError(str(e), "finnhub")
    
    async def get_current_price(self, symbol: str) -> Dict[str, Any]:
        """Get current price for a symbol."""
        try:
            result = await self._make_request(f"quote", {"symbol": symbol.upper()})
            
            return {
                "symbol": symbol.upper(),
                "price": result.get('c', 0),  # Current price
                "open": result.get('o', 0),   # Open price
                "high": result.get('h', 0),   # High price
                "low": result.get('l', 0),    # Low price
                "previous_close": result.get('pc', 0),  # Previous close
                "change": result.get('d', 0),  # Change
                "change_percent": result.get('dp', 0),  # Change percent
                "timestamp": datetime.now().isoformat(),
                "source": "finnhub"
            }
        except Exception as e:
            logger.error(f"Error getting current price for {symbol}: {e}")
            return {"error": str(e), "symbol": symbol}
    
    async def get_ticker(self, symbol: str) -> Dict[str, Any]:
        """Get comprehensive ticker data for a symbol (alias for get_current_price)."""
        return await self.get_current_price(symbol)
    
    async def get_historical_data(self, symbol: str, resolution: str = "D", 
                                count: int = 100) -> Dict[str, Any]:
        """Get historical data for a symbol."""
        try:
            # Calculate date range
            end_time = int(datetime.now().timestamp())
            start_time = end_time - (count * 24 * 60 * 60)  # count days ago
            
            params = {
                "symbol": symbol.upper(),
                "resolution": resolution,
                "from": start_time,
                "to": end_time
            }
            
            result = await self._make_request("stock/candle", params)
            
            if result.get('s') == 'ok':
                # Convert to standard format
                history = []
                timestamps = result.get('t', [])
                opens = result.get('o', [])
                highs = result.get('h', [])
                lows = result.get('l', [])
                closes = result.get('c', [])
                volumes = result.get('v', [])
                
                for i in range(len(timestamps)):
                    history.append({
                        'date': datetime.fromtimestamp(timestamps[i]).strftime('%Y-%m-%d'),
                        'open': opens[i] if i < len(opens) else 0,
                        'high': highs[i] if i < len(highs) else 0,
                        'low': lows[i] if i < len(lows) else 0,
                        'close': closes[i] if i < len(closes) else 0,
                        'volume': volumes[i] if i < len(volumes) else 0,
                        'symbol': symbol.upper()
                    })
                
                return {
                    "symbol": symbol.upper(),
                    "resolution": resolution,
                    "data": history,
                    "source": "finnhub"
                }
            else:
                return {"error": f"No data available for {symbol}", "symbol": symbol}
                
        except Exception as e:
            logger.error(f"Error getting historical data for {symbol}: {e}")
            return {"error": str(e), "symbol": symbol}
    
    async def get_company_profile(self, symbol: str) -> Dict[str, Any]:
        """Get company profile information."""
        try:
            result = await self._make_request("stock/profile2", {"symbol": symbol.upper()})
            return {
                "symbol": symbol.upper(),
                "name": result.get('name', ''),
                "country": result.get('country', ''),
                "currency": result.get('currency', ''),
                "exchange": result.get('exchange', ''),
                "industry": result.get('finnhubIndustry', ''),
                "market_cap": result.get('marketCapitalization', 0),
                "shares_outstanding": result.get('shareOutstanding', 0),
                "website": result.get('weburl', ''),
                "logo": result.get('logo', ''),
                "source": "finnhub"
            }
        except Exception as e:
            logger.error(f"Error getting company profile for {symbol}: {e}")
            return {"error": str(e), "symbol": symbol}
    
    async def close(self):
        """Close the HTTP session."""
        if self.session and not self.session.closed:
            await self.session.close()
            self.session = None
