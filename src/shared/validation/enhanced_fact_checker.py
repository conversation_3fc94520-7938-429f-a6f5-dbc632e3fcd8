"""
Enhanced Fact-Checking System

This module provides comprehensive fact-checking capabilities that validate AI responses
against multiple data sources to ensure accuracy and detect potential misinformation.
"""

import re
import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass
from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum

from src.shared.error_handling.logging import get_logger
from src.shared.data_providers.aggregator import DataProviderAggregator
from src.api.data.providers.modules.validation import RealDataValidator, DataQualityAssessor

logger = get_logger(__name__)

class FactCheckSeverity(Enum):
    """Severity levels for fact-checking issues"""
    CRITICAL = "critical"  # Factually incorrect information
    HIGH = "high"         # Potentially misleading
    MEDIUM = "medium"     # Minor inaccuracies
    LOW = "low"          # Style or formatting issues

@dataclass
class FactCheckIssue:
    """Represents a fact-checking issue found in AI response"""
    severity: FactCheckSeverity
    category: str
    description: str
    ai_claim: str
    actual_value: Optional[str] = None
    confidence: float = 1.0
    source: str = "unknown"

@dataclass
class FactCheckResult:
    """Result of comprehensive fact-checking"""
    is_accurate: bool
    confidence_score: float  # 0-100
    issues: List[FactCheckIssue]
    validated_claims: int
    total_claims: int
    data_sources_used: List[str]
    validation_timestamp: datetime

class EnhancedFactChecker:
    """
    Comprehensive fact-checking system that validates AI responses against multiple data sources
    """
    
    def __init__(self):
        self.data_aggregator = DataProviderAggregator()
        self.data_quality_assessor = DataQualityAssessor()
        self.real_data_validator = RealDataValidator()
        
        # Enhanced AI-powered extraction with regex fallback
        self.use_ai_extraction = True

        # Fallback regex patterns (used when AI fails)
        self.fallback_price_patterns = [
            r'\$?(\d+(?:\.\d{2})?)\s*(?:per share|each|USD)?',
            r'price.*?(\d+(?:\.\d{2})?)',
            r'trading.*?(\d+(?:\.\d{2})?)',
            r'valued.*?(\d+(?:\.\d{2})?)'
        ]

        self.fallback_percentage_patterns = [
            r'(\d+(?:\.\d+)?)\s*%',
            r'(\d+(?:\.\d+)?)\s*percent',
            r'increased.*?(\d+(?:\.\d+)?)\s*%',
            r'decreased.*?(\d+(?:\.\d+)?)\s*%'
        ]

        self.fallback_ratio_patterns = [
            r'P/E.*?(\d+(?:\.\d+)?)',
            r'price.*?earnings.*?(\d+(?:\.\d+)?)',
            r'debt.*?equity.*?(\d+(?:\.\d+)?)',
            r'current.*?ratio.*?(\d+(?:\.\d+)?)'
        ]
    
    async def fact_check_response(self, ai_response: str, context: Dict[str, Any]) -> FactCheckResult:
        """
        Perform comprehensive fact-checking of AI response
        
        Args:
            ai_response: The AI-generated response to fact-check
            context: Context including symbols, query type, etc.
            
        Returns:
            FactCheckResult with detailed validation results
        """
        logger.info("🔍 Starting comprehensive fact-checking")
        
        issues = []
        validated_claims = 0
        total_claims = 0
        data_sources_used = []
        
        # Extract symbols from context
        symbols = context.get('symbols', [])
        
        # 1. Validate numerical claims
        numerical_issues, numerical_stats = await self._validate_numerical_claims(
            ai_response, symbols
        )
        issues.extend(numerical_issues)
        validated_claims += numerical_stats['validated']
        total_claims += numerical_stats['total']
        data_sources_used.extend(numerical_stats['sources'])
        
        # 2. Validate market data claims
        market_issues, market_stats = await self._validate_market_data_claims(
            ai_response, symbols
        )
        issues.extend(market_issues)
        validated_claims += market_stats['validated']
        total_claims += market_stats['total']
        data_sources_used.extend(market_stats['sources'])
        
        # 3. Validate technical analysis claims
        technical_issues, technical_stats = await self._validate_technical_claims(
            ai_response, symbols
        )
        issues.extend(technical_issues)
        validated_claims += technical_stats['validated']
        total_claims += technical_stats['total']
        data_sources_used.extend(technical_stats['sources'])
        
        # 4. Cross-reference with multiple data sources
        cross_ref_issues = await self._cross_reference_data_sources(ai_response, symbols)
        issues.extend(cross_ref_issues)
        
        # Calculate overall confidence score
        confidence_score = self._calculate_confidence_score(issues, validated_claims, total_claims)
        
        # Determine if response is accurate
        critical_issues = [issue for issue in issues if issue.severity == FactCheckSeverity.CRITICAL]
        is_accurate = len(critical_issues) == 0 and confidence_score >= 70
        
        logger.info(f"✅ Fact-checking complete: {validated_claims}/{total_claims} claims validated, "
                   f"confidence: {confidence_score:.1f}%, issues: {len(issues)}")
        
        return FactCheckResult(
            is_accurate=is_accurate,
            confidence_score=confidence_score,
            issues=issues,
            validated_claims=validated_claims,
            total_claims=total_claims,
            data_sources_used=list(set(data_sources_used)),
            validation_timestamp=datetime.now()
        )
    
    async def _validate_numerical_claims(self, response: str, symbols: List[str]) -> Tuple[List[FactCheckIssue], Dict[str, Any]]:
        """Validate numerical claims using AI-powered extraction with regex fallback"""
        issues = []
        validated = 0
        total = 0
        sources = []

        # Extract price claims using AI or regex fallback
        price_claims = await self._extract_price_claims_ai(response)

        for price_claim in price_claims:
            total += 1
            claimed_price = price_claim['value']
            confidence = price_claim.get('confidence', 0.8)

            # Validate against real data for each symbol
            for symbol in symbols:
                    try:
                        market_data = await self.data_aggregator.get_ticker(symbol)
                        if 'error' not in market_data:
                            sources.append(f"ticker_data_{symbol}")
                            actual_price = market_data.get('price', market_data.get('close'))
                            
                            if actual_price:
                                price_diff = abs(claimed_price - actual_price) / actual_price
                                
                                if price_diff > 0.05:  # More than 5% difference
                                    severity = FactCheckSeverity.CRITICAL if price_diff > 0.20 else FactCheckSeverity.HIGH
                                    issues.append(FactCheckIssue(
                                        severity=severity,
                                        category="price_accuracy",
                                        description=f"Price claim for {symbol} differs significantly from actual",
                                        ai_claim=f"${claimed_price:.2f}",
                                        actual_value=f"${actual_price:.2f}",
                                        confidence=1.0 - price_diff,
                                        source=f"market_data_{symbol}"
                                    ))
                                else:
                                    validated += 1
                    except Exception as e:
                        logger.warning(f"Failed to validate price for {symbol}: {e}")
        
        return issues, {'validated': validated, 'total': total, 'sources': sources}
    
    async def _validate_market_data_claims(self, response: str, symbols: List[str]) -> Tuple[List[FactCheckIssue], Dict[str, Any]]:
        """Validate market data claims (volume, market cap, etc.)"""
        issues = []
        validated = 0
        total = 0
        sources = []
        
        # Extract percentage claims using AI or regex fallback
        percentage_claims = await self._extract_percentage_claims_ai(response)

        for percentage_claim in percentage_claims:
            total += 1
            claimed_percentage = percentage_claim['value']
            confidence = percentage_claim.get('confidence', 0.8)

            # Validate against actual change data
            for symbol in symbols:
                    try:
                        market_data = await self.data_aggregator.get_ticker(symbol)
                        if 'error' not in market_data:
                            sources.append(f"change_data_{symbol}")
                            actual_change = market_data.get('change_percent')
                            
                            if actual_change is not None:
                                change_diff = abs(claimed_percentage - abs(actual_change))
                                
                                if change_diff > 2.0:  # More than 2% difference
                                    severity = FactCheckSeverity.HIGH if change_diff > 5.0 else FactCheckSeverity.MEDIUM
                                    issues.append(FactCheckIssue(
                                        severity=severity,
                                        category="percentage_accuracy",
                                        description=f"Percentage change claim for {symbol} differs from actual",
                                        ai_claim=f"{claimed_percentage:.1f}%",
                                        actual_value=f"{actual_change:.1f}%",
                                        confidence=max(0.0, 1.0 - (change_diff / 10.0)),
                                        source=f"market_data_{symbol}"
                                    ))
                                else:
                                    validated += 1
                    except Exception as e:
                        logger.warning(f"Failed to validate percentage for {symbol}: {e}")
        
        return issues, {'validated': validated, 'total': total, 'sources': sources}
    
    async def _validate_technical_claims(self, response: str, symbols: List[str]) -> Tuple[List[FactCheckIssue], Dict[str, Any]]:
        """Validate technical analysis claims"""
        issues = []
        validated = 0
        total = 0
        sources = []
        
        # Look for technical indicator mentions
        technical_terms = ['RSI', 'MACD', 'moving average', 'support', 'resistance', 'bollinger']
        
        for term in technical_terms:
            if term.lower() in response.lower():
                total += 1
                # For now, we'll mark technical claims as validated if they use proper terminology
                # In a full implementation, we'd calculate actual technical indicators
                validated += 1
                sources.append(f"technical_analysis_{term}")
        
        return issues, {'validated': validated, 'total': total, 'sources': sources}
    
    async def _cross_reference_data_sources(self, response: str, symbols: List[str]) -> List[FactCheckIssue]:
        """Cross-reference claims across multiple data sources"""
        issues = []
        
        for symbol in symbols:
            try:
                # Get data from multiple providers
                providers = ['yfinance', 'alpha_vantage']
                data_points = {}
                
                for provider in providers:
                    try:
                        data = await self.data_aggregator.get_ticker(symbol, preferred_provider=provider)
                        if 'error' not in data:
                            data_points[provider] = data.get('price', data.get('close'))
                    except Exception as e:
                        logger.debug(f"Provider {provider} failed for {symbol}: {e}")
                
                # Check for significant discrepancies between sources
                if len(data_points) >= 2:
                    prices = list(data_points.values())
                    price_range = max(prices) - min(prices)
                    avg_price = sum(prices) / len(prices)
                    
                    if price_range / avg_price > 0.02:  # More than 2% discrepancy
                        issues.append(FactCheckIssue(
                            severity=FactCheckSeverity.MEDIUM,
                            category="data_source_discrepancy",
                            description=f"Significant price discrepancy between data sources for {symbol}",
                            ai_claim="Multiple sources",
                            actual_value=f"Range: ${min(prices):.2f} - ${max(prices):.2f}",
                            confidence=0.7,
                            source="cross_reference"
                        ))
            
            except Exception as e:
                logger.warning(f"Failed to cross-reference data for {symbol}: {e}")
        
        return issues
    
    def _calculate_confidence_score(self, issues: List[FactCheckIssue], validated: int, total: int) -> float:
        """Calculate overall confidence score based on validation results"""
        if total == 0:
            return 50.0  # Neutral score when no claims to validate
        
        # Base score from validation rate
        validation_rate = validated / total if total > 0 else 0
        base_score = validation_rate * 100
        
        # Deduct points for issues
        deductions = 0
        for issue in issues:
            if issue.severity == FactCheckSeverity.CRITICAL:
                deductions += 30
            elif issue.severity == FactCheckSeverity.HIGH:
                deductions += 15
            elif issue.severity == FactCheckSeverity.MEDIUM:
                deductions += 5
            elif issue.severity == FactCheckSeverity.LOW:
                deductions += 1
        
        final_score = max(0.0, base_score - deductions)
        return min(100.0, final_score)
    
    def generate_fact_check_report(self, result: FactCheckResult) -> str:
        """Generate a human-readable fact-check report"""
        report_lines = [
            f"🔍 **Fact-Check Report**",
            f"📊 **Accuracy**: {'✅ Verified' if result.is_accurate else '⚠️ Issues Found'}",
            f"🎯 **Confidence**: {result.confidence_score:.1f}%",
            f"📈 **Claims Validated**: {result.validated_claims}/{result.total_claims}",
            f"🔗 **Data Sources**: {', '.join(result.data_sources_used)}",
            f"⏰ **Validated At**: {result.validation_timestamp.strftime('%Y-%m-%d %H:%M:%S')}"
        ]
        
        if result.issues:
            report_lines.append(f"\n⚠️ **Issues Found ({len(result.issues)})**:")
            for i, issue in enumerate(result.issues[:5], 1):  # Show first 5 issues
                severity_emoji = {
                    FactCheckSeverity.CRITICAL: "🔴",
                    FactCheckSeverity.HIGH: "🟡", 
                    FactCheckSeverity.MEDIUM: "🟠",
                    FactCheckSeverity.LOW: "🟢"
                }
                emoji = severity_emoji.get(issue.severity, "⚪")
                report_lines.append(f"{i}. {emoji} {issue.description}")
                if issue.ai_claim and issue.actual_value:
                    report_lines.append(f"   Claimed: {issue.ai_claim} | Actual: {issue.actual_value}")
        
        return "\n".join(report_lines)

    async def _extract_price_claims_ai(self, text: str) -> List[Dict[str, Any]]:
        """Extract price claims using AI with regex fallback"""
        try:
            # Try AI extraction first
            if self.use_ai_extraction:
                from src.shared.ai_services.intelligent_text_parser import intelligent_parser
                prices = await intelligent_parser.extract_prices(text, use_ai=True)

                if prices:
                    return [{'value': price, 'confidence': 0.9, 'method': 'ai'} for price in prices]

            # Fallback to regex
            return self._extract_price_claims_regex(text)

        except Exception as e:
            logger.warning(f"AI price extraction failed: {e}")
            return self._extract_price_claims_regex(text)

    def _extract_price_claims_regex(self, text: str) -> List[Dict[str, Any]]:
        """Extract price claims using regex patterns"""
        claims = []

        for pattern in self.fallback_price_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                try:
                    price = float(match)
                    claims.append({
                        'value': price,
                        'confidence': 0.7,
                        'method': 'regex'
                    })
                except ValueError:
                    continue

        return claims

    async def _extract_percentage_claims_ai(self, text: str) -> List[Dict[str, Any]]:
        """Extract percentage claims using AI with regex fallback"""
        try:
            # Try AI extraction first
            if self.use_ai_extraction:
                from src.shared.ai_services.intelligent_text_parser import intelligent_parser
                percentages = await intelligent_parser.extract_percentages(text, use_ai=True)

                if percentages:
                    return percentages

            # Fallback to regex
            return self._extract_percentage_claims_regex(text)

        except Exception as e:
            logger.warning(f"AI percentage extraction failed: {e}")
            return self._extract_percentage_claims_regex(text)

    def _extract_percentage_claims_regex(self, text: str) -> List[Dict[str, Any]]:
        """Extract percentage claims using regex patterns"""
        claims = []
        for pattern in self.fallback_percentage_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                try:
                    percentage = float(match.group(1))
                    claims.append({
                        'value': percentage,
                        'confidence': 0.7,
                        'method': 'regex'
                    })
                except (ValueError, IndexError):
                    continue
        return claims


class MultiSourceFactValidator:
    """
    Advanced fact validator that cross-references multiple data sources
    and maintains a knowledge base of validated facts
    """

    def __init__(self):
        self.fact_checker = EnhancedFactChecker()
        self.validated_facts_cache = {}  # Cache of previously validated facts
        self.source_reliability_scores = {
            'yfinance': 0.85,
            'alpha_vantage': 0.90,
            'polygon': 0.95,
            'finnhub': 0.88
        }

    async def validate_with_consensus(self, ai_response: str, context: Dict[str, Any]) -> FactCheckResult:
        """
        Validate facts using consensus across multiple data sources
        """
        # Get basic fact-check result
        base_result = await self.fact_checker.fact_check_response(ai_response, context)

        # Enhance with consensus validation
        consensus_issues = await self._validate_consensus(ai_response, context.get('symbols', []))
        base_result.issues.extend(consensus_issues)

        # Recalculate confidence with consensus data
        base_result.confidence_score = self._calculate_consensus_confidence(
            base_result.issues, base_result.validated_claims, base_result.total_claims
        )

        return base_result

    async def _validate_consensus(self, response: str, symbols: List[str]) -> List[FactCheckIssue]:
        """Validate using consensus across multiple data sources"""
        issues = []

        for symbol in symbols:
            # Get data from all available sources
            source_data = {}
            for source in self.source_reliability_scores.keys():
                try:
                    data = await self.fact_checker.data_aggregator.get_ticker(
                        symbol, preferred_provider=source
                    )
                    if 'error' not in data:
                        source_data[source] = data
                except Exception as e:
                    logger.debug(f"Source {source} failed for {symbol}: {e}")

            # Check for consensus on key metrics
            if len(source_data) >= 2:
                consensus_issues = self._check_price_consensus(symbol, source_data)
                issues.extend(consensus_issues)

        return issues

    def _check_price_consensus(self, symbol: str, source_data: Dict[str, Any]) -> List[FactCheckIssue]:
        """Check price consensus across data sources"""
        issues = []
        prices = []

        for source, data in source_data.items():
            price = data.get('price', data.get('close'))
            if price:
                prices.append((source, price))

        if len(prices) >= 2:
            price_values = [p[1] for p in prices]
            avg_price = sum(price_values) / len(price_values)
            max_deviation = max(abs(p - avg_price) / avg_price for p in price_values)

            if max_deviation > 0.05:  # More than 5% deviation
                issues.append(FactCheckIssue(
                    severity=FactCheckSeverity.HIGH,
                    category="consensus_failure",
                    description=f"Price consensus failure for {symbol} across data sources",
                    ai_claim="Multiple sources disagree",
                    actual_value=f"Deviation: {max_deviation:.1%}",
                    confidence=1.0 - max_deviation,
                    source="consensus_validation"
                ))

        return issues

    def _calculate_consensus_confidence(self, issues: List[FactCheckIssue], validated: int, total: int) -> float:
        """Calculate confidence score including consensus validation"""
        base_confidence = self.fact_checker._calculate_confidence_score(issues, validated, total)

        # Boost confidence if consensus validation passed
        consensus_issues = [i for i in issues if i.category == "consensus_failure"]
        if not consensus_issues and total > 0:
            base_confidence = min(100.0, base_confidence + 10)  # 10 point bonus for consensus

        return base_confidence
