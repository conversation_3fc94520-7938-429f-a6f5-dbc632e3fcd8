#!/usr/bin/env python3
"""
Test trading recommendations to see if NVDA bias is fixed
"""

import asyncio
import sys
import os
from collections import Counter

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_trading_recommendations():
    """Test trading recommendations to see if NVDA is always chosen"""
    
    print("🧪 TESTING TRADING RECOMMENDATIONS")
    print("=" * 50)
    
    # Import the AI processor
    from src.shared.ai_services.ai_processor_robust import RobustFinancialAnalyzer
    
    # Create analyzer instance
    analyzer = RobustFinancialAnalyzer()
    
    # Test queries that should trigger stock recommendations
    test_queries = [
        "What's the best tech stock to buy right now?",
        "I want to find a good AI stock for my portfolio",
        "Which stock has the most potential?",
        "What are some good growth stocks?",
        "I'm looking for a safe stock to invest in",
        "What stock should I buy today?",
        "Give me a trading recommendation",
        "What's a good stock for momentum trading?",
        "I need a stock pick for this week",
        "What's the hottest stock right now?"
    ]
    
    all_symbols_mentioned = []
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n📝 Query {i}: '{query}'")
        print("-" * 50)
        
        try:
            # Test the query processing using the general question handler
            response = await analyzer.answer_general_question(query)

            print(f"Response: {response[:200]}...")

            # Extract symbols mentioned in response
            import re
            symbols = re.findall(r'\b([A-Z]{2,5})\b', response)
            # Filter out common words that aren't stock symbols
            stock_symbols = [s for s in symbols if s in ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'AMD', 'META', 'SPY', 'QQQ']]

            all_symbols_mentioned.extend(stock_symbols)
            print(f"Symbols found: {stock_symbols}")

        except Exception as e:
            print(f"Error: {e}")
    
    print(f"\n🎯 OVERALL SYMBOL DISTRIBUTION")
    print("-" * 40)
    
    if all_symbols_mentioned:
        total_symbol_counts = Counter(all_symbols_mentioned)
        total_mentions = sum(total_symbol_counts.values())
        
        for symbol, count in total_symbol_counts.most_common():
            percentage = (count / total_mentions) * 100
            print(f"  {symbol}: {count} mentions ({percentage:.1f}%)")
        
        # Check NVDA bias
        nvda_percentage = (total_symbol_counts.get('NVDA', 0) / total_mentions) * 100
        
        print(f"\n📊 NVDA BIAS ANALYSIS")
        print("-" * 40)
        
        if nvda_percentage > 50:
            print(f"❌ NVDA still dominates with {nvda_percentage:.1f}% of mentions")
            print("   The bias fix may not be working in trading recommendations")
        elif nvda_percentage > 25:
            print(f"⚠️  NVDA has {nvda_percentage:.1f}% of mentions (moderate bias)")
            print("   Some bias reduction achieved but could be better")
        else:
            print(f"✅ NVDA has only {nvda_percentage:.1f}% of mentions (good distribution)")
            print("   Bias successfully reduced in trading recommendations!")
    else:
        print("No stock symbols found in responses")

if __name__ == "__main__":
    asyncio.run(test_trading_recommendations())
