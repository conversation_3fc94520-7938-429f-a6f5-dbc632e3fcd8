#!/usr/bin/env python3
"""
Test AI Quality - Grade the actual responses to trading questions
"""

import asyncio
import sys
sys.path.insert(0, 'src')

async def test_ai_trading_knowledge():
    """Test the AI's actual trading knowledge and response quality"""
    from shared.ai_services.enhanced_intent_detector import EnhancedIntentDetector
    from shared.ai_chat.ai_client import AIClientWrapper
    
    print("🧪 Testing AI Trading Knowledge & Response Quality")
    print("=" * 80)
    
    # Test questions with expected answers
    test_questions = [
        {
            "question": "What are the support and resistance levels for AAPL?",
            "expected_intent": "support_resistance",
            "should_include": ["support", "resistance", "levels", "AAPL", "price"],
            "max_length": 500
        },
        {
            "question": "What's the current technical analysis for TSLA?",
            "expected_intent": "technical_analysis", 
            "should_include": ["technical", "analysis", "TSLA", "indicators", "trend"],
            "max_length": 800
        },
        {
            "question": "Should I buy or sell NVDA right now?",
            "expected_intent": "recommendation",
            "should_include": ["buy", "sell", "NVDA", "recommendation", "analysis"],
            "max_length": 600
        },
        {
            "question": "What's the RSI and MACD for MSFT?",
            "expected_intent": "technical_analysis",
            "should_include": ["RSI", "MACD", "MSFT", "technical", "indicators"],
            "max_length": 400
        },
        {
            "question": "Compare AAPL vs GOOGL performance",
            "expected_intent": "comparison",
            "should_include": ["AAPL", "GOOGL", "compare", "performance", "analysis"],
            "max_length": 700
        }
    ]
    
    detector = EnhancedIntentDetector()
    ai_client = AIClientWrapper()
    
    total_score = 0
    max_score = len(test_questions) * 100
    
    for i, test in enumerate(test_questions, 1):
        print(f"\n{'='*20} TEST {i}/5 {'='*20}")
        print(f"Question: {test['question']}")
        print("-" * 60)
        
        # Test intent detection
        try:
            analysis = await detector.analyze_intent(test['question'])
            intent_score = 20 if analysis.primary_intent.value == test['expected_intent'] else 0
            print(f"✅ Intent Detection: {analysis.primary_intent.value} (Expected: {test['expected_intent']}) - {intent_score}/20 pts")
        except Exception as e:
            print(f"❌ Intent Detection Error: {e} - 0/20 pts")
            intent_score = 0
        
        # Test AI response quality
        try:
            # Create a trading-focused prompt
            trading_prompt = f"""
You are a professional trading analyst. Answer this question with specific, actionable trading insights:

Question: {test['question']}

Provide a detailed response that includes:
1. Specific technical analysis if applicable
2. Current market data and trends
3. Clear trading recommendations
4. Risk assessment
5. Professional trading terminology

Be specific, accurate, and professional. Use real market knowledge.
"""
            
            response = await ai_client.generate_response(trading_prompt)
            
            if not response:
                print("❌ AI Response: Empty response - 0/80 pts")
                response_score = 0
            else:
                print(f"📝 AI Response Length: {len(response)} chars")
                print(f"📝 AI Response Preview: {response[:200]}...")
                
                # Grade the response
                response_score = 0
                
                # Length check (10 pts)
                if len(response) > 100:
                    response_score += 10
                    print("✅ Length: Good (>100 chars) - 10/10 pts")
                else:
                    print("❌ Length: Too short - 0/10 pts")
                
                # Content relevance (30 pts)
                content_score = 0
                for keyword in test['should_include']:
                    if keyword.lower() in response.lower():
                        content_score += 5
                
                content_score = min(content_score, 30)
                response_score += content_score
                print(f"✅ Content Relevance: {content_score}/30 pts")
                
                # Professional quality (20 pts)
                professional_indicators = [
                    "analysis", "technical", "indicator", "trend", "support", "resistance",
                    "recommendation", "risk", "market", "trading", "investment"
                ]
                professional_score = 0
                for indicator in professional_indicators:
                    if indicator in response.lower():
                        professional_score += 2
                
                professional_score = min(professional_score, 20)
                response_score += professional_score
                print(f"✅ Professional Quality: {professional_score}/20 pts")
                
                # Specificity (20 pts)
                specific_indicators = [
                    "RSI", "MACD", "moving average", "volume", "price", "chart",
                    "buy", "sell", "hold", "target", "stop loss"
                ]
                specificity_score = 0
                for indicator in specific_indicators:
                    if indicator.lower() in response.lower():
                        specificity_score += 2
                
                specificity_score = min(specificity_score, 20)
                response_score += specificity_score
                print(f"✅ Specificity: {specificity_score}/20 pts")
                
                print(f"📊 Response Score: {response_score}/80 pts")
            
        except Exception as e:
            print(f"❌ AI Response Error: {e} - 0/80 pts")
            response_score = 0
        
        # Total score for this question
        question_score = intent_score + response_score
        total_score += question_score
        print(f"🎯 Question {i} Total: {question_score}/100 pts")
    
    # Final grading
    percentage = (total_score / max_score) * 100
    print(f"\n{'='*80}")
    print(f"🏆 FINAL AI QUALITY SCORE: {total_score}/{max_score} ({percentage:.1f}%)")
    
    if percentage >= 90:
        grade = "A+ (Excellent)"
    elif percentage >= 80:
        grade = "A (Very Good)"
    elif percentage >= 70:
        grade = "B (Good)"
    elif percentage >= 60:
        grade = "C (Average)"
    else:
        grade = "D (Needs Improvement)"
    
    print(f"🎓 GRADE: {grade}")
    
    if percentage < 70:
        print("\n⚠️  RECOMMENDATIONS:")
        print("- Improve AI prompt engineering for better responses")
        print("- Add more specific trading knowledge training")
        print("- Implement response validation and quality checks")
        print("- Consider using specialized financial AI models")
    else:
        print("\n✅ AI is performing well for trading questions!")

if __name__ == "__main__":
    asyncio.run(test_ai_trading_knowledge())
