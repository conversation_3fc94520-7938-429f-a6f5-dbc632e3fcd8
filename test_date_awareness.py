#!/usr/bin/env python3
"""
Test if AI now knows the current date after system prompt fixes
"""

import asyncio
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_date_awareness():
    """Test if AI knows the current date"""
    
    print("🧪 TESTING DATE AWARENESS AFTER FIXES")
    print("=" * 50)
    
    # Import the AI processor
    from src.shared.ai_services.ai_processor_robust import RobustFinancialAnalyzer
    
    # Create analyzer instance
    analyzer = RobustFinancialAnalyzer()
    
    # Test queries that should show date awareness
    test_queries = [
        "What's today's date?",
        "What time is it?",
        "Give me a trading recommendation for this week",
        "What are some good options expiring this Friday?",
        "What's happening in the market today?"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n📝 Query {i}: '{query}'")
        print("-" * 50)
        
        try:
            # Test the query processing
            response = await analyzer.answer_general_question(query)
            
            print(f"Response: {response}")
            
            # Check for problematic phrases
            problematic_phrases = [
                "I don't have live calendar access",
                "I can't tell you today's exact date",
                "I don't know the current date",
                "I don't have access to real-time",
                "I can't access current"
            ]
            
            has_problems = any(phrase.lower() in response.lower() for phrase in problematic_phrases)
            
            if has_problems:
                print("❌ AI still claims it doesn't know the date/time")
            else:
                print("✅ AI appears to be date-aware")
            
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_date_awareness())
