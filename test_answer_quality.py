#!/usr/bin/env python3
"""
Answer Quality Audit Script
Tests the actual responses from the Discord bot to evaluate answer quality
"""

import asyncio
import json
import time
from datetime import datetime
from typing import Dict, List, Any
import sys
import os

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.bot.pipeline.commands.ask.pipeline import AskPipeline
from src.bot.pipeline.commands.ask.config import AskPipelineConfig
from src.shared.ai_services.unified_ai_processor import create_unified_processor

class AnswerQualityAuditor:
    """Comprehensive answer quality testing"""
    
    def __init__(self):
        self.results = []
        self.config = AskPipelineConfig()
        
    async def test_query(self, query: str, expected_elements: List[str], category: str) -> Dict[str, Any]:
        """Test a single query and evaluate response quality"""
        print(f"\n🔍 Testing: {query}")
        
        start_time = time.time()
        
        try:
            # Create pipeline
            pipeline = AskPipeline(self.config)

            # Process query
            result = await pipeline.process_query(
                query=query,
                user_id="test_user",
                username="quality_auditor"
            )
            
            response_time = time.time() - start_time
            
            # Extract response text
            response_text = ""
            if isinstance(result, dict):
                # Try different possible keys for the response
                response_text = (result.get('response') or
                               result.get('content') or
                               result.get('answer') or
                               result.get('result') or
                               str(result))
            elif hasattr(result, 'response') and result.response:
                response_text = result.response
            elif hasattr(result, 'content') and result.content:
                response_text = result.content
            elif isinstance(result, str):
                response_text = result
            else:
                response_text = str(result)
            
            # Evaluate quality
            quality_score = self.evaluate_response_quality(
                query, response_text, expected_elements, category
            )
            
            test_result = {
                'query': query,
                'category': category,
                'response': response_text,
                'response_time': round(response_time, 2),
                'quality_score': quality_score,
                'expected_elements': expected_elements,
                'timestamp': datetime.now().isoformat(),
                'success': True
            }
            
            print(f"✅ Response ({response_time:.2f}s): {response_text[:100]}...")
            print(f"📊 Quality Score: {quality_score['total']}/100")
            
        except Exception as e:
            test_result = {
                'query': query,
                'category': category,
                'response': f"ERROR: {str(e)}",
                'response_time': time.time() - start_time,
                'quality_score': {'total': 0, 'details': 'Failed to execute'},
                'expected_elements': expected_elements,
                'timestamp': datetime.now().isoformat(),
                'success': False
            }
            
            print(f"❌ Error: {str(e)}")
        
        self.results.append(test_result)
        return test_result
    
    def evaluate_response_quality(self, query: str, response: str, expected_elements: List[str], category: str) -> Dict[str, Any]:
        """Evaluate response quality across multiple dimensions"""
        
        scores = {
            'relevance': 0,      # Does it answer the question?
            'accuracy': 0,       # Is the information correct?
            'completeness': 0,   # Does it include expected elements?
            'clarity': 0,        # Is it clear and well-structured?
            'actionability': 0   # Can the user act on this information?
        }
        
        response_lower = response.lower()
        
        # Relevance (20 points)
        if any(keyword in response_lower for keyword in query.lower().split()):
            scores['relevance'] += 15
        if len(response) > 50:  # Substantial response
            scores['relevance'] += 5
        
        # Completeness (25 points)
        elements_found = 0
        for element in expected_elements:
            if element.lower() in response_lower:
                elements_found += 1
        
        if expected_elements:
            scores['completeness'] = int((elements_found / len(expected_elements)) * 25)
        else:
            scores['completeness'] = 20  # Default if no specific elements expected
        
        # Clarity (20 points)
        if len(response) > 20:  # Not too short
            scores['clarity'] += 5
        if len(response) < 1000:  # Not too long
            scores['clarity'] += 5
        if any(marker in response for marker in ['•', '-', '1.', '2.', '\n']):  # Structured
            scores['clarity'] += 10
        
        # Accuracy (20 points) - Basic checks
        if 'error' not in response_lower and 'failed' not in response_lower:
            scores['accuracy'] += 10
        if not any(bad in response_lower for bad in ['i don\'t know', 'cannot', 'unable']):
            scores['accuracy'] += 10
        
        # Actionability (15 points)
        if category in ['stock_analysis', 'trading_advice']:
            if any(action in response_lower for action in ['buy', 'sell', 'hold', 'consider', 'recommend']):
                scores['actionability'] += 10
            if any(metric in response_lower for metric in ['price', '$', 'target', 'support', 'resistance']):
                scores['actionability'] += 5
        else:
            scores['actionability'] = 15  # Full points for non-trading queries
        
        total_score = sum(scores.values())
        
        return {
            'total': total_score,
            'breakdown': scores,
            'grade': self.get_grade(total_score)
        }
    
    def get_grade(self, score: int) -> str:
        """Convert numeric score to letter grade"""
        if score >= 90: return 'A'
        elif score >= 80: return 'B'
        elif score >= 70: return 'C'
        elif score >= 60: return 'D'
        else: return 'F'
    
    async def run_comprehensive_audit(self):
        """Run comprehensive answer quality audit"""
        
        print("🎯 ANSWER QUALITY AUDIT")
        print("=" * 50)
        
        # Test cases covering different scenarios
        test_cases = [
            # Stock Analysis
            {
                'query': 'What is the current price of AAPL?',
                'expected_elements': ['price', 'AAPL', '$'],
                'category': 'stock_price'
            },
            {
                'query': 'Should I buy Tesla stock?',
                'expected_elements': ['Tesla', 'TSLA', 'analysis', 'recommendation'],
                'category': 'stock_analysis'
            },
            {
                'query': 'Analyze NVDA technical indicators',
                'expected_elements': ['NVDA', 'technical', 'indicators', 'analysis'],
                'category': 'technical_analysis'
            },
            
            # Market Analysis
            {
                'query': 'What are the top performing stocks today?',
                'expected_elements': ['stocks', 'performance', 'today'],
                'category': 'market_overview'
            },
            {
                'query': 'How is the S&P 500 performing?',
                'expected_elements': ['S&P 500', 'performance', 'market'],
                'category': 'market_index'
            },
            
            # Trading Advice
            {
                'query': 'Find me a good stock for swing trading',
                'expected_elements': ['stock', 'swing trading', 'recommendation'],
                'category': 'trading_advice'
            },
            {
                'query': 'What stocks have high volume today?',
                'expected_elements': ['volume', 'stocks', 'today'],
                'category': 'volume_analysis'
            },
            
            # General Queries
            {
                'query': 'Explain what a P/E ratio means',
                'expected_elements': ['P/E ratio', 'price', 'earnings', 'valuation'],
                'category': 'education'
            },
            {
                'query': 'What is happening in the crypto market?',
                'expected_elements': ['crypto', 'market', 'bitcoin', 'ethereum'],
                'category': 'crypto_analysis'
            },
            
            # Edge Cases
            {
                'query': 'asdfghjkl',
                'expected_elements': ['clarification', 'help'],
                'category': 'invalid_input'
            }
        ]
        
        # Run all tests
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📋 Test {i}/{len(test_cases)}")
            await self.test_query(
                test_case['query'],
                test_case['expected_elements'],
                test_case['category']
            )
            
            # Small delay between tests
            await asyncio.sleep(1)
        
        # Generate summary report
        self.generate_summary_report()
    
    def generate_summary_report(self):
        """Generate comprehensive summary report"""
        
        print("\n" + "=" * 60)
        print("📊 ANSWER QUALITY AUDIT SUMMARY")
        print("=" * 60)
        
        if not self.results:
            print("❌ No test results available")
            return
        
        # Calculate overall statistics
        successful_tests = [r for r in self.results if r['success']]
        total_tests = len(self.results)
        success_rate = (len(successful_tests) / total_tests) * 100
        
        if successful_tests:
            avg_quality = sum(r['quality_score']['total'] for r in successful_tests) / len(successful_tests)
            avg_response_time = sum(r['response_time'] for r in successful_tests) / len(successful_tests)
            
            # Grade distribution
            grades = [r['quality_score']['grade'] for r in successful_tests]
            grade_counts = {grade: grades.count(grade) for grade in 'ABCDF'}
        else:
            avg_quality = 0
            avg_response_time = 0
            grade_counts = {}
        
        print(f"🎯 Overall Results:")
        print(f"   • Total Tests: {total_tests}")
        print(f"   • Success Rate: {success_rate:.1f}%")
        print(f"   • Average Quality Score: {avg_quality:.1f}/100")
        print(f"   • Average Response Time: {avg_response_time:.2f}s")
        
        print(f"\n📈 Grade Distribution:")
        for grade, count in grade_counts.items():
            if count > 0:
                print(f"   • Grade {grade}: {count} tests")
        
        # Category breakdown
        print(f"\n📂 Results by Category:")
        categories = {}
        for result in successful_tests:
            cat = result['category']
            if cat not in categories:
                categories[cat] = []
            categories[cat].append(result['quality_score']['total'])
        
        for category, scores in categories.items():
            avg_score = sum(scores) / len(scores)
            print(f"   • {category}: {avg_score:.1f}/100 ({len(scores)} tests)")
        
        # Top and bottom performers
        if successful_tests:
            sorted_results = sorted(successful_tests, key=lambda x: x['quality_score']['total'], reverse=True)
            
            print(f"\n🏆 Best Response:")
            best = sorted_results[0]
            print(f"   Query: {best['query']}")
            print(f"   Score: {best['quality_score']['total']}/100")
            print(f"   Response: {best['response'][:100]}...")
            
            print(f"\n⚠️ Needs Improvement:")
            worst = sorted_results[-1]
            print(f"   Query: {worst['query']}")
            print(f"   Score: {worst['quality_score']['total']}/100")
            print(f"   Response: {worst['response'][:100]}...")
        
        # Save detailed results
        self.save_detailed_results()
    
    def save_detailed_results(self):
        """Save detailed results to file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"answer_quality_audit_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\n💾 Detailed results saved to: {filename}")

async def main():
    """Main execution function"""
    auditor = AnswerQualityAuditor()
    await auditor.run_comprehensive_audit()

if __name__ == "__main__":
    asyncio.run(main())
