#!/usr/bin/env python3
"""
Quick test to verify symbol extraction fix
"""

import asyncio
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_symbol_fix():
    """Test the fixed symbol extraction"""
    
    print("🔧 TESTING SYMBOL EXTRACTION FIX")
    print("=" * 40)
    
    # Test the problematic query that was extracting wrong symbols
    test_query = "I want to find a good stock to buy calls on next week"
    print(f"📝 Query: '{test_query}'")
    
    try:
        from src.shared.ai_services.enhanced_symbol_extractor import enhanced_symbol_extractor
        results = await enhanced_symbol_extractor.extract_symbols_simple(test_query, use_ai=True)
        print(f"✅ Enhanced extraction: {results}")
        
        if results == [] or (len(results) == 1 and results[0] in ['', 'NONE']):
            print("🎉 SUCCESS: No false positive symbols extracted!")
        else:
            print(f"❌ ISSUE: Still extracting symbols: {results}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test a query that should extract symbols
    test_query2 = "What is $AAPL doing today?"
    print(f"\n📝 Query: '{test_query2}'")
    
    try:
        results2 = await enhanced_symbol_extractor.extract_symbols_simple(test_query2, use_ai=True)
        print(f"✅ Enhanced extraction: {results2}")
        
        if 'AAPL' in results2:
            print("🎉 SUCCESS: Correctly extracted AAPL!")
        else:
            print(f"❌ ISSUE: Should have extracted AAPL: {results2}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_symbol_fix())
