#!/usr/bin/env python3
"""
Comprehensive Quality Assessment Suite
=====================================

This script conducts thorough quality testing of the new compatibility fixes
and pipeline functionality, focusing on quality metrics rather than just functionality.
"""

import asyncio
import time
import sys
import os
import traceback
import psutil
import gc
from typing import Dict, Any, List, Tuple
from datetime import datetime
import json

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

class QualityMetrics:
    """Track quality metrics for testing"""
    
    def __init__(self):
        self.metrics = {
            'import_performance': {},
            'memory_usage': {},
            'response_quality': {},
            'error_handling': {},
            'compatibility': {},
            'performance': {}
        }
        self.start_time = time.time()
        self.process = psutil.Process()
    
    def record_metric(self, category: str, name: str, value: Any, details: Dict = None):
        """Record a quality metric"""
        if category not in self.metrics:
            self.metrics[category] = {}
        
        self.metrics[category][name] = {
            'value': value,
            'timestamp': time.time() - self.start_time,
            'details': details or {}
        }
    
    def get_memory_usage(self) -> Dict[str, float]:
        """Get current memory usage"""
        memory_info = self.process.memory_info()
        return {
            'rss_mb': memory_info.rss / 1024 / 1024,
            'vms_mb': memory_info.vms / 1024 / 1024,
            'percent': self.process.memory_percent()
        }
    
    def generate_report(self) -> str:
        """Generate a comprehensive quality report"""
        report = []
        report.append("=" * 80)
        report.append("COMPREHENSIVE QUALITY ASSESSMENT REPORT")
        report.append("=" * 80)
        report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"Total Test Duration: {time.time() - self.start_time:.2f} seconds")
        report.append("")
        
        for category, metrics in self.metrics.items():
            if metrics:
                report.append(f"📊 {category.upper().replace('_', ' ')}")
                report.append("-" * 50)
                for name, data in metrics.items():
                    report.append(f"  {name}: {data['value']}")
                    if data['details']:
                        for key, val in data['details'].items():
                            report.append(f"    {key}: {val}")
                report.append("")
        
        return "\n".join(report)

class APICompatibilityQualityTest:
    """Test the quality of API compatibility layer"""
    
    def __init__(self, metrics: QualityMetrics):
        self.metrics = metrics
    
    async def test_import_performance(self):
        """Test the performance impact of compatibility imports"""
        print("🔍 Testing Import Performance Quality...")
        
        import_tests = [
            ("Package Ask Import", "from src.bot.pipeline.commands.ask import execute_ask_pipeline, execute_batch_ask_pipeline, AskPipeline"),
            ("Package Analyze Import", "from src.bot.pipeline.commands.analyze import execute_analyze_pipeline, execute_parallel_analyze_pipeline"),
            ("Direct Executor Import", "from src.bot.pipeline.commands.ask.executor import execute_ask_pipeline"),
            ("Direct Pipeline Import", "from src.bot.pipeline.commands.analyze.pipeline import execute_analyze_pipeline"),
            ("Batch Processor Import", "from src.bot.pipeline.commands.ask.batch_processor import execute_batch_ask_pipeline"),
            ("Parallel Pipeline Import", "from src.bot.pipeline.commands.analyze.parallel_pipeline import execute_parallel_analyze_pipeline")
        ]
        
        for test_name, import_statement in import_tests:
            # Measure import time and memory impact
            gc.collect()  # Clean up before test
            start_memory = self.metrics.get_memory_usage()
            start_time = time.perf_counter()
            
            try:
                # Execute import in a clean namespace
                namespace = {}
                exec(import_statement, namespace)
                
                end_time = time.perf_counter()
                end_memory = self.metrics.get_memory_usage()
                
                import_time = (end_time - start_time) * 1000  # Convert to milliseconds
                memory_delta = end_memory['rss_mb'] - start_memory['rss_mb']
                
                # Quality assessment
                quality_score = self._assess_import_quality(import_time, memory_delta)
                
                self.metrics.record_metric('import_performance', test_name, quality_score, {
                    'import_time_ms': round(import_time, 2),
                    'memory_delta_mb': round(memory_delta, 2),
                    'status': 'SUCCESS'
                })
                
                print(f"  ✅ {test_name}: {quality_score}/100 (Time: {import_time:.2f}ms, Memory: +{memory_delta:.2f}MB)")
                
            except Exception as e:
                self.metrics.record_metric('import_performance', test_name, 0, {
                    'error': str(e),
                    'status': 'FAILED'
                })
                print(f"  ❌ {test_name}: FAILED - {e}")
    
    def _assess_import_quality(self, import_time_ms: float, memory_delta_mb: float) -> int:
        """Assess the quality of an import based on performance metrics"""
        score = 100
        
        # Penalize slow imports
        if import_time_ms > 1000:  # > 1 second
            score -= 40
        elif import_time_ms > 500:  # > 500ms
            score -= 20
        elif import_time_ms > 100:  # > 100ms
            score -= 10
        
        # Penalize high memory usage
        if memory_delta_mb > 50:  # > 50MB
            score -= 30
        elif memory_delta_mb > 20:  # > 20MB
            score -= 15
        elif memory_delta_mb > 10:  # > 10MB
            score -= 5
        
        return max(0, score)
    
    async def test_error_handling_quality(self):
        """Test the quality of error handling in compatibility layer"""
        print("🔍 Testing Error Handling Quality...")
        
        error_tests = [
            ("Invalid Module Import", "from src.bot.pipeline.commands.nonexistent import fake_function"),
            ("Circular Import Resilience", "import sys; sys.modules['test_circular'] = sys.modules[__name__]; from test_circular import execute_ask_pipeline"),
            ("Partial Import Failure", "from src.bot.pipeline.commands.ask import execute_ask_pipeline, nonexistent_function")
        ]
        
        for test_name, test_code in error_tests:
            try:
                namespace = {}
                exec(test_code, namespace)
                # If no error, that might be unexpected
                self.metrics.record_metric('error_handling', test_name, 50, {
                    'status': 'UNEXPECTED_SUCCESS',
                    'note': 'Expected error but none occurred'
                })
                print(f"  ⚠️  {test_name}: Unexpected success")
                
            except ImportError as e:
                # Expected behavior - good error handling
                error_quality = self._assess_error_quality(str(e))
                self.metrics.record_metric('error_handling', test_name, error_quality, {
                    'error_type': 'ImportError',
                    'error_message': str(e),
                    'status': 'EXPECTED_ERROR'
                })
                print(f"  ✅ {test_name}: {error_quality}/100 (Proper ImportError)")
                
            except Exception as e:
                # Unexpected error type
                self.metrics.record_metric('error_handling', test_name, 25, {
                    'error_type': type(e).__name__,
                    'error_message': str(e),
                    'status': 'UNEXPECTED_ERROR'
                })
                print(f"  ⚠️  {test_name}: Unexpected error type - {type(e).__name__}")
    
    def _assess_error_quality(self, error_message: str) -> int:
        """Assess the quality of error messages"""
        score = 100
        
        # Good error messages should be informative
        if len(error_message) < 20:
            score -= 20
        
        # Should mention the specific module/function
        if 'module' not in error_message.lower():
            score -= 15
        
        # Should not expose internal paths unnecessarily
        if '/home/' in error_message or 'site-packages' in error_message:
            score -= 10
        
        return max(0, score)

class PipelineExecutionQualityTest:
    """Test the quality of pipeline execution and outputs"""

    def __init__(self, metrics: QualityMetrics):
        self.metrics = metrics

    async def test_ask_pipeline_quality(self):
        """Test the quality of ask pipeline responses"""
        print("🔍 Testing Ask Pipeline Response Quality...")

        test_queries = [
            ("Simple Stock Query", "What is the current price of AAPL?"),
            ("Technical Analysis Query", "Give me a technical analysis of NVDA"),
            ("Multi-Symbol Query", "Compare AAPL and GOOGL"),
            ("Market Sentiment Query", "What's the market sentiment for Tesla?"),
            ("Invalid Symbol Query", "Tell me about INVALIDTICKER123"),
            ("General Question", "How are you doing today?")
        ]

        for test_name, query in test_queries:
            print(f"  Testing: {test_name}")
            start_time = time.perf_counter()
            start_memory = self.metrics.get_memory_usage()

            try:
                # Import and execute ask pipeline
                from src.bot.pipeline.commands.ask import execute_ask_pipeline

                result = await asyncio.wait_for(
                    execute_ask_pipeline(
                        query=query,
                        user_id="quality_test_user",
                        guild_id="quality_test_guild",
                        correlation_id=f"quality_test_{int(time.time())}"
                    ),
                    timeout=30.0
                )

                end_time = time.perf_counter()
                end_memory = self.metrics.get_memory_usage()

                # Analyze response quality
                quality_score = self._analyze_response_quality(result, query)
                execution_time = (end_time - start_time) * 1000
                memory_delta = end_memory['rss_mb'] - start_memory['rss_mb']

                self.metrics.record_metric('response_quality', test_name, quality_score, {
                    'execution_time_ms': round(execution_time, 2),
                    'memory_delta_mb': round(memory_delta, 2),
                    'response_length': len(str(result.processing_results.get('response', ''))),
                    'status': str(result.status.value) if hasattr(result, 'status') else 'unknown',
                    'has_response': bool(result.processing_results.get('response'))
                })

                print(f"    ✅ Quality Score: {quality_score}/100 (Time: {execution_time:.0f}ms)")

            except asyncio.TimeoutError:
                self.metrics.record_metric('response_quality', test_name, 0, {
                    'error': 'Timeout after 30 seconds',
                    'status': 'TIMEOUT'
                })
                print(f"    ❌ TIMEOUT after 30 seconds")

            except Exception as e:
                self.metrics.record_metric('response_quality', test_name, 0, {
                    'error': str(e),
                    'error_type': type(e).__name__,
                    'status': 'ERROR'
                })
                print(f"    ❌ ERROR: {e}")

    async def test_analyze_pipeline_quality(self):
        """Test the quality of analyze pipeline responses"""
        print("🔍 Testing Analyze Pipeline Response Quality...")

        test_symbols = [
            ("Popular Stock", "AAPL"),
            ("Tech Stock", "NVDA"),
            ("ETF", "SPY"),
            ("Crypto-Related", "COIN"),
            ("Invalid Symbol", "INVALIDTICKER123")
        ]

        for test_name, symbol in test_symbols:
            print(f"  Testing: {test_name} ({symbol})")
            start_time = time.perf_counter()
            start_memory = self.metrics.get_memory_usage()

            try:
                # Import and execute analyze pipeline
                from src.bot.pipeline.commands.analyze import execute_analyze_pipeline

                result = await asyncio.wait_for(
                    execute_analyze_pipeline(
                        ticker=symbol,
                        user_id="quality_test_user",
                        guild_id="quality_test_guild",
                        correlation_id=f"quality_test_{int(time.time())}"
                    ),
                    timeout=45.0
                )

                end_time = time.perf_counter()
                end_memory = self.metrics.get_memory_usage()

                # Analyze response quality
                quality_score = self._analyze_analysis_quality(result, symbol)
                execution_time = (end_time - start_time) * 1000
                memory_delta = end_memory['rss_mb'] - start_memory['rss_mb']

                self.metrics.record_metric('response_quality', f"Analyze_{test_name}", quality_score, {
                    'execution_time_ms': round(execution_time, 2),
                    'memory_delta_mb': round(memory_delta, 2),
                    'response_length': len(str(result.processing_results.get('response', ''))),
                    'status': str(result.status.value) if hasattr(result, 'status') else 'unknown',
                    'has_response': bool(result.processing_results.get('response'))
                })

                print(f"    ✅ Quality Score: {quality_score}/100 (Time: {execution_time:.0f}ms)")

            except asyncio.TimeoutError:
                self.metrics.record_metric('response_quality', f"Analyze_{test_name}", 0, {
                    'error': 'Timeout after 45 seconds',
                    'status': 'TIMEOUT'
                })
                print(f"    ❌ TIMEOUT after 45 seconds")

            except Exception as e:
                self.metrics.record_metric('response_quality', f"Analyze_{test_name}", 0, {
                    'error': str(e),
                    'error_type': type(e).__name__,
                    'status': 'ERROR'
                })
                print(f"    ❌ ERROR: {e}")

    def _analyze_response_quality(self, result, query: str) -> int:
        """Analyze the quality of an ask pipeline response"""
        score = 100

        # Check if we have a result object
        if not result:
            return 0

        # Check if pipeline completed
        if hasattr(result, 'status') and result.status.value != 'completed':
            score -= 40

        # Check if we have a response
        response = result.processing_results.get('response', '') if hasattr(result, 'processing_results') else ''
        if not response:
            score -= 50
        elif len(response) < 50:  # Very short response
            score -= 30
        elif len(response) < 100:  # Short response
            score -= 15

        # Check response relevance (basic keyword matching)
        query_lower = query.lower()
        response_lower = response.lower()

        if 'price' in query_lower and 'price' not in response_lower and '$' not in response_lower:
            score -= 20

        if any(word in query_lower for word in ['technical', 'analysis']) and 'analysis' not in response_lower:
            score -= 15

        # Check for error indicators in response
        if any(word in response_lower for word in ['error', 'failed', 'unable', 'sorry']):
            score -= 25

        return max(0, score)

    def _analyze_analysis_quality(self, result, symbol: str) -> int:
        """Analyze the quality of an analyze pipeline response"""
        score = 100

        # Check if we have a result object
        if not result:
            return 0

        # Check if pipeline completed
        if hasattr(result, 'status') and result.status.value != 'completed':
            score -= 40

        # Check if we have a response
        response = result.processing_results.get('response', '') if hasattr(result, 'processing_results') else ''
        if not response:
            score -= 50
        elif len(response) < 100:  # Very short response
            score -= 30
        elif len(response) < 200:  # Short response
            score -= 15

        # Check if symbol is mentioned in response
        if symbol.upper() not in response.upper():
            score -= 20

        # Check for technical analysis indicators
        technical_indicators = ['rsi', 'macd', 'moving average', 'support', 'resistance', 'trend']
        if not any(indicator in response.lower() for indicator in technical_indicators):
            score -= 25

        # Check for error indicators in response
        if any(word in response.lower() for word in ['error', 'failed', 'unable', 'sorry']):
            score -= 25

        return max(0, score)

async def main():
    """Main quality assessment function"""
    print("🚀 Starting Comprehensive Quality Assessment")
    print("=" * 80)

    metrics = QualityMetrics()

    # Test 1: API Compatibility Quality
    print("📋 PHASE 1: API Compatibility Quality")
    api_test = APICompatibilityQualityTest(metrics)
    await api_test.test_import_performance()
    await api_test.test_error_handling_quality()

    # Test 2: Pipeline Execution Quality
    print("\n📋 PHASE 2: Pipeline Execution Quality")
    pipeline_test = PipelineExecutionQualityTest(metrics)
    await pipeline_test.test_ask_pipeline_quality()
    await pipeline_test.test_analyze_pipeline_quality()

    print("\n" + "=" * 80)
    print("📊 QUALITY ASSESSMENT COMPLETE")
    print("=" * 80)

    # Generate and display report
    report = metrics.generate_report()
    print(report)

    # Save report to file
    with open('quality_assessment_report.txt', 'w') as f:
        f.write(report)

    print(f"\n📄 Detailed report saved to: quality_assessment_report.txt")

if __name__ == "__main__":
    asyncio.run(main())
