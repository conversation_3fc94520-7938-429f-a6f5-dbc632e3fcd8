# Backtesting Frameworks Research

## Objective
Evaluate Python frameworks for backtesting trading strategies in this system. Criteria: Ease of integration with existing async pipeline and data providers (Polygon, Yahoo), support for technical indicators, strategy optimization, portfolio management, and live trading extension. Must be lightweight, open-source, and Python-native.

## Evaluated Frameworks

### 1. Backtrader
- **Description**: Mature, flexible backtesting library with Cerebro engine for running strategies on historical data.
- **Pros**:
  - Supports multiple data feeds (CSV, Pandas, live via brokers like Interactive Brokers).
  - Built-in indicators (SMA, RSI, MACD) – integrates with existing technical_analysis_processor.py.
  - Strategy optimization via `cerebro.optstrategy` (parameter sweeps, genetic algos via custom).
  - Portfolio simulation with position sizing, commissions, slippage.
  - Async-friendly with event loop; can extend to live trading.
  - Active community, extensive docs.
- **Cons**: Steeper learning curve for advanced features; no built-in ML integration (but extensible).
- **Compatibility**: High – Use `market_data['historical']` as Pandas feed. Add to requirements.txt: `backtrader==**********`.
- **Suitability**: Best fit for this project – balances features and simplicity.

### 2. Zipline
- **Description**: Quantopian's backtesting engine, focused on algorithmic trading.
- **Pros**: Robust for US equities, bundles data ingestion (from Quandl/Yahoo), performance metrics (Sharpe, drawdown).
- **Cons**: Heavier (requires ingest pipeline), less flexible for custom data, no native async/live trading, deprecated in favor of QuantConnect.
- **Compatibility**: Medium – Would require data reload, not seamless with current async fetch.
- **Suitability**: Overkill for initial implementation; better for institutional setups.

### 3. VectorBT
- **Description**: Vectorized backtesting using NumPy/Pandas for speed.
- **Pros**: Extremely fast for parameter optimization, simple API for signal-based strategies.
- **Cons**: Less support for complex order types/portfolio rebalancing, no live trading, focused on speed over realism.
- **Compatibility**: Low – Vectorized approach doesn't align with event-driven pipeline.
- **Suitability**: Good for quick prototyping, but not for full strategy simulation.

### 4. PyAlgoTrade
- **Description**: Event-driven backtester with strategy classes.
- **Pros**: Simple, supports TA-Lib indicators.
- **Cons**: Less active development, limited optimization tools.
- **Compatibility**: Medium, but Backtrader is superior.

### Recommendation
**Backtrader** is selected for implementation. It aligns with the project's Python/async nature, integrates directly with existing data structures, and supports all required features (backtesting, optimization, portfolio). Initial setup: Install via pip, create basic Cerebro in new module.

Next Steps: Design data structures, implement engine.