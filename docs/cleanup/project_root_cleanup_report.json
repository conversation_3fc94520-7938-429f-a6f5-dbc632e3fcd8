{"cleanup_timestamp": "2025-09-19T11:20:14.977410", "dry_run": false, "summary": {"total_files_analyzed": 170, "files_to_move": 147, "files_to_keep": 23, "unknown_files": 0, "moved_files": 147, "errors": 0}, "categorization": {"fix_scripts": ["fix_data_provider.py", "fix_circuit_breaker_error.py", "fix_ask_command_simple.py", "fix_processor_exception.py", "fix_discord_helpers.py", "fix_discord_final.py", "fix_timing_only.py", "fix_trading_keywords.py", "fix_pipeline_export.py", "fix_pattern_matching_order.py", "fix_discord_interaction.py", "fix_username_parameter.py", "fix_ai_processor_constructor.py", "fix_clean_responses.py", "fix_pipeline_grader.py", "fix_symbol_extraction.py", "fix_f_string.py", "fix_user_id_parameter.py", "fix_ai_price_hallucination.py", "fix_discord_send.py", "fix_pipeline_export_final.py", "fix_context_parameter.py", "fix_missing_method.py", "fix_api_keys.py", "fix_mock_issue.py", "fix_pipeline_data_structure.py", "fix_validation_logic.py", "fix_discord_interaction_final.py", "fix_ai_processor.py", "fix_ai_parsing_errors.py", "fix_symbol_extraction_ai.py", "fix_pipeline_ai.py", "fix_real_processing.py", "fix_chatbot_complete.py", "fix_logger_import.py", "fix_ai_query_interpretation.py", "fix_validation_final.py"], "debug_scripts": ["debug_data_format.py", "debug_pattern_matching.py", "debug_sentiment.py", "debug_sentiment_model.py", "debug_ast.py", "debug_sentiment_detailed.py"], "generate_scripts": ["generate_and_send_report.py", "generate_detailed_architecture.py", "generate_architecture.py"], "test_scripts": ["test_architectural_improvements.py", "test_zero_hallucination.py", "test_hybrid_approach.py", "test_ai_data_constraint.py", "test_docker_processor.py", "test_ast_parsing.py", "test_data_binding_fix.py", "test_ai_answer_quality_final.py", "test_core_pipeline.py", "test_enhanced_hybrid_approach.py", "test_hallucinated_response.py", "test_enhanced_hybrid_consolidated.py", "test_dashboard.py", "test_real_ai_responses.py", "test_enhanced_validation.py", "test_ai_improvements.py", "test_simple_ai.py", "test_fixed_validation.py", "test_ai_controlled_analysis.py", "test_security_middleware.py", "test_real_system.py", "test_data_issue.py", "test_ask_command_hallucination_fix.py", "test_hybrid_ai.py", "test_query_result_fix.py", "test_monte_carlo_demo.py", "test_discord_ask.py", "test_consolidated_architecture.py", "test_real_ai_system.py", "test_ai_vs_regex.py", "test_robust_processor.py", "test_professional_standards.py", "test_live_commands.py", "test_answer_quality.py"], "create_scripts": ["create_ai_query_interpreter.py", "create_fixed_pipeline.py", "create_working_pipeline.py", "create_watchlist_tables.py"], "optimize_scripts": ["optimize_performance.py", "optimize_bot_performance.py"], "demo_scripts": ["demo_dashboard.py", "demo_enhanced_analysis.py"], "check_scripts": ["check_tables.py", "check_table_structure.py"], "log_files": ["debug_test.log", "continuous_trading_monitor.log", "final_system_test_results.log", "working_system_test_results.log", "comprehensive_test_results.log", "real_trading_audit.log", "all_tests.log", "final_trading_audit.log", "test_run_latest.log", "enhanced_trading_audit.log", "final_test_results.log", "trading_system_audit.log", "docker_services_test_results.log"], "json_reports": ["architecture.json", "response_metrics.json", "architecture_detailed.json", "comprehensive_market_analysis_report_20250916_001933.json", "architecture_pretty.json"], "txt_reports": ["current_test_files.txt", "dashboard_requirements.txt", "codebase_explorer_requirements.txt", "security-requirements.txt", "cleanup_validation_report.txt", "market_analysis_summary_20250916_001933.txt"], "md_reports": [], "html_files": [], "shell_scripts": ["test_docker_setup.sh", "test_supabase_docker.sh"], "temp_files": [".env.tmp"], "env_files": [], "docker_files": ["docker-nuke"], "config_files": ["system_api.py", "config_api.py"], "specific": ["system_dashboard.html", "simple_test.py", "temp_logger_fix.py", "working_solution.py", "fixed_ai_service_wrapper.py", "final_docs_cleanup.py", "quick_start_db.py", "working_db_solution.py", "performance_optimization.py", "visualize_recent_query.py", ".coverage", "working_with_existing_tables.py", ".cursorignore", "comprehensive_test.py", "project_manifest.md", "nvda_price_target_analysis.py", ".env.example", "improve_pattern_matching.py", "final_working_solution.py", "pipeline_events.py", "simple_db_test.py", "example_pipeline_integration.py", "simple_pipeline_emitter.py", "import_test.py", "nvda_price_target_analysis_modular.py", "claude.html", "folder_cleanup_plan.md"], "keep": ["pyproject.toml", "DOCKER_BUILD_OPTIMIZATION.md", "pytest_comprehensive.ini", "ngrok.yml", "start_enhanced.sh", ".giti<PERSON>re", "PROJECT_OVERVIEW.md", "start_ai_automation.py", "pytest.ini", "redis.conf", "docker-compose.yml", "start_dashboard.py", "start_bot.py", "requirements.txt", "alembic.ini", "start_enhanced_bot.py", "docker-compose.production.yml", "README.md", ".env", ".dockerignore", "docker-entrypoint.sh", "config.yaml", "Dockerfile"], "unknown": []}, "moved_files": [{"source": "fix_data_provider.py", "target": "scripts/fix/fix_data_provider.py", "timestamp": "2025-09-19T11:20:14.944821"}, {"source": "fix_circuit_breaker_error.py", "target": "scripts/fix/fix_circuit_breaker_error.py", "timestamp": "2025-09-19T11:20:14.944961"}, {"source": "fix_ask_command_simple.py", "target": "scripts/fix/fix_ask_command_simple.py", "timestamp": "2025-09-19T11:20:14.945044"}, {"source": "fix_processor_exception.py", "target": "scripts/fix/fix_processor_exception.py", "timestamp": "2025-09-19T11:20:14.945138"}, {"source": "fix_discord_helpers.py", "target": "scripts/fix/fix_discord_helpers.py", "timestamp": "2025-09-19T11:20:14.945289"}, {"source": "fix_discord_final.py", "target": "scripts/fix/fix_discord_final.py", "timestamp": "2025-09-19T11:20:14.945396"}, {"source": "fix_timing_only.py", "target": "scripts/fix/fix_timing_only.py", "timestamp": "2025-09-19T11:20:14.945505"}, {"source": "fix_trading_keywords.py", "target": "scripts/fix/fix_trading_keywords.py", "timestamp": "2025-09-19T11:20:14.945605"}, {"source": "fix_pipeline_export.py", "target": "scripts/fix/fix_pipeline_export.py", "timestamp": "2025-09-19T11:20:14.945708"}, {"source": "fix_pattern_matching_order.py", "target": "scripts/fix/fix_pattern_matching_order.py", "timestamp": "2025-09-19T11:20:14.945822"}, {"source": "fix_discord_interaction.py", "target": "scripts/fix/fix_discord_interaction.py", "timestamp": "2025-09-19T11:20:14.945918"}, {"source": "fix_username_parameter.py", "target": "scripts/fix/fix_username_parameter.py", "timestamp": "2025-09-19T11:20:14.946029"}, {"source": "fix_ai_processor_constructor.py", "target": "scripts/fix/fix_ai_processor_constructor.py", "timestamp": "2025-09-19T11:20:14.946122"}, {"source": "fix_clean_responses.py", "target": "scripts/fix/fix_clean_responses.py", "timestamp": "2025-09-19T11:20:14.946212"}, {"source": "fix_pipeline_grader.py", "target": "scripts/fix/fix_pipeline_grader.py", "timestamp": "2025-09-19T11:20:14.947289"}, {"source": "fix_symbol_extraction.py", "target": "scripts/fix/fix_symbol_extraction.py", "timestamp": "2025-09-19T11:20:14.947479"}, {"source": "fix_f_string.py", "target": "scripts/fix/fix_f_string.py", "timestamp": "2025-09-19T11:20:14.947570"}, {"source": "fix_user_id_parameter.py", "target": "scripts/fix/fix_user_id_parameter.py", "timestamp": "2025-09-19T11:20:14.947671"}, {"source": "fix_ai_price_hallucination.py", "target": "scripts/fix/fix_ai_price_hallucination.py", "timestamp": "2025-09-19T11:20:14.948342"}, {"source": "fix_discord_send.py", "target": "scripts/fix/fix_discord_send.py", "timestamp": "2025-09-19T11:20:14.948466"}, {"source": "fix_pipeline_export_final.py", "target": "scripts/fix/fix_pipeline_export_final.py", "timestamp": "2025-09-19T11:20:14.948551"}, {"source": "fix_context_parameter.py", "target": "scripts/fix/fix_context_parameter.py", "timestamp": "2025-09-19T11:20:14.948641"}, {"source": "fix_missing_method.py", "target": "scripts/fix/fix_missing_method.py", "timestamp": "2025-09-19T11:20:14.949588"}, {"source": "fix_api_keys.py", "target": "scripts/fix/fix_api_keys.py", "timestamp": "2025-09-19T11:20:14.949747"}, {"source": "fix_mock_issue.py", "target": "scripts/fix/fix_mock_issue.py", "timestamp": "2025-09-19T11:20:14.950546"}, {"source": "fix_pipeline_data_structure.py", "target": "scripts/fix/fix_pipeline_data_structure.py", "timestamp": "2025-09-19T11:20:14.950784"}, {"source": "fix_validation_logic.py", "target": "scripts/fix/fix_validation_logic.py", "timestamp": "2025-09-19T11:20:14.951593"}, {"source": "fix_discord_interaction_final.py", "target": "scripts/fix/fix_discord_interaction_final.py", "timestamp": "2025-09-19T11:20:14.951720"}, {"source": "fix_ai_processor.py", "target": "scripts/fix/fix_ai_processor.py", "timestamp": "2025-09-19T11:20:14.951837"}, {"source": "fix_ai_parsing_errors.py", "target": "scripts/fix/fix_ai_parsing_errors.py", "timestamp": "2025-09-19T11:20:14.951931"}, {"source": "fix_symbol_extraction_ai.py", "target": "scripts/fix/fix_symbol_extraction_ai.py", "timestamp": "2025-09-19T11:20:14.952043"}, {"source": "fix_pipeline_ai.py", "target": "scripts/fix/fix_pipeline_ai.py", "timestamp": "2025-09-19T11:20:14.952160"}, {"source": "fix_real_processing.py", "target": "scripts/fix/fix_real_processing.py", "timestamp": "2025-09-19T11:20:14.952276"}, {"source": "fix_chatbot_complete.py", "target": "scripts/fix/fix_chatbot_complete.py", "timestamp": "2025-09-19T11:20:14.952401"}, {"source": "fix_logger_import.py", "target": "scripts/fix/fix_logger_import.py", "timestamp": "2025-09-19T11:20:14.952515"}, {"source": "fix_ai_query_interpretation.py", "target": "scripts/fix/fix_ai_query_interpretation.py", "timestamp": "2025-09-19T11:20:14.952622"}, {"source": "fix_validation_final.py", "target": "scripts/fix/fix_validation_final.py", "timestamp": "2025-09-19T11:20:14.952762"}, {"source": "debug_data_format.py", "target": "scripts/debug/debug_data_format.py", "timestamp": "2025-09-19T11:20:14.953773"}, {"source": "debug_pattern_matching.py", "target": "scripts/debug/debug_pattern_matching.py", "timestamp": "2025-09-19T11:20:14.953959"}, {"source": "debug_sentiment.py", "target": "scripts/debug/debug_sentiment.py", "timestamp": "2025-09-19T11:20:14.955053"}, {"source": "debug_sentiment_model.py", "target": "scripts/debug/debug_sentiment_model.py", "timestamp": "2025-09-19T11:20:14.956286"}, {"source": "debug_ast.py", "target": "scripts/debug/debug_ast.py", "timestamp": "2025-09-19T11:20:14.956456"}, {"source": "debug_sentiment_detailed.py", "target": "scripts/debug/debug_sentiment_detailed.py", "timestamp": "2025-09-19T11:20:14.956558"}, {"source": "generate_and_send_report.py", "target": "scripts/generate/generate_and_send_report.py", "timestamp": "2025-09-19T11:20:14.956678"}, {"source": "generate_detailed_architecture.py", "target": "scripts/generate/generate_detailed_architecture.py", "timestamp": "2025-09-19T11:20:14.957027"}, {"source": "generate_architecture.py", "target": "scripts/generate/generate_architecture.py", "timestamp": "2025-09-19T11:20:14.957195"}, {"source": "test_architectural_improvements.py", "target": "scripts/test/test_architectural_improvements.py", "timestamp": "2025-09-19T11:20:14.958375"}, {"source": "test_zero_hallucination.py", "target": "scripts/test/test_zero_hallucination.py", "timestamp": "2025-09-19T11:20:14.958588"}, {"source": "test_hybrid_approach.py", "target": "scripts/test/test_hybrid_approach.py", "timestamp": "2025-09-19T11:20:14.959461"}, {"source": "test_ai_data_constraint.py", "target": "scripts/test/test_ai_data_constraint.py", "timestamp": "2025-09-19T11:20:14.959576"}, {"source": "test_docker_processor.py", "target": "scripts/test/test_docker_processor.py", "timestamp": "2025-09-19T11:20:14.959707"}, {"source": "test_ast_parsing.py", "target": "scripts/test/test_ast_parsing.py", "timestamp": "2025-09-19T11:20:14.959803"}, {"source": "test_data_binding_fix.py", "target": "scripts/test/test_data_binding_fix.py", "timestamp": "2025-09-19T11:20:14.959900"}, {"source": "test_ai_answer_quality_final.py", "target": "scripts/test/test_ai_answer_quality_final.py", "timestamp": "2025-09-19T11:20:14.960009"}, {"source": "test_core_pipeline.py", "target": "scripts/test/test_core_pipeline.py", "timestamp": "2025-09-19T11:20:14.960118"}, {"source": "test_enhanced_hybrid_approach.py", "target": "scripts/test/test_enhanced_hybrid_approach.py", "timestamp": "2025-09-19T11:20:14.960231"}, {"source": "test_hallucinated_response.py", "target": "scripts/test/test_hallucinated_response.py", "timestamp": "2025-09-19T11:20:14.960347"}, {"source": "test_enhanced_hybrid_consolidated.py", "target": "scripts/test/test_enhanced_hybrid_consolidated.py", "timestamp": "2025-09-19T11:20:14.960448"}, {"source": "test_dashboard.py", "target": "scripts/test/test_dashboard.py", "timestamp": "2025-09-19T11:20:14.960540"}, {"source": "test_real_ai_responses.py", "target": "scripts/test/test_real_ai_responses.py", "timestamp": "2025-09-19T11:20:14.960846"}, {"source": "test_enhanced_validation.py", "target": "scripts/test/test_enhanced_validation.py", "timestamp": "2025-09-19T11:20:14.960944"}, {"source": "test_ai_improvements.py", "target": "scripts/test/test_ai_improvements.py", "timestamp": "2025-09-19T11:20:14.961716"}, {"source": "test_simple_ai.py", "target": "scripts/test/test_simple_ai.py", "timestamp": "2025-09-19T11:20:14.961828"}, {"source": "test_fixed_validation.py", "target": "scripts/test/test_fixed_validation.py", "timestamp": "2025-09-19T11:20:14.961908"}, {"source": "test_ai_controlled_analysis.py", "target": "scripts/test/test_ai_controlled_analysis.py", "timestamp": "2025-09-19T11:20:14.961965"}, {"source": "test_security_middleware.py", "target": "scripts/test/test_security_middleware.py", "timestamp": "2025-09-19T11:20:14.962031"}, {"source": "test_real_system.py", "target": "scripts/test/test_real_system.py", "timestamp": "2025-09-19T11:20:14.962099"}, {"source": "test_data_issue.py", "target": "scripts/test/test_data_issue.py", "timestamp": "2025-09-19T11:20:14.962177"}, {"source": "test_ask_command_hallucination_fix.py", "target": "scripts/test/test_ask_command_hallucination_fix.py", "timestamp": "2025-09-19T11:20:14.963334"}, {"source": "test_hybrid_ai.py", "target": "scripts/test/test_hybrid_ai.py", "timestamp": "2025-09-19T11:20:14.963701"}, {"source": "test_query_result_fix.py", "target": "scripts/test/test_query_result_fix.py", "timestamp": "2025-09-19T11:20:14.963833"}, {"source": "test_monte_carlo_demo.py", "target": "scripts/test/test_monte_carlo_demo.py", "timestamp": "2025-09-19T11:20:14.963929"}, {"source": "test_discord_ask.py", "target": "scripts/test/test_discord_ask.py", "timestamp": "2025-09-19T11:20:14.964036"}, {"source": "test_consolidated_architecture.py", "target": "scripts/test/test_consolidated_architecture.py", "timestamp": "2025-09-19T11:20:14.964163"}, {"source": "test_real_ai_system.py", "target": "scripts/test/test_real_ai_system.py", "timestamp": "2025-09-19T11:20:14.964332"}, {"source": "test_ai_vs_regex.py", "target": "scripts/test/test_ai_vs_regex.py", "timestamp": "2025-09-19T11:20:14.964467"}, {"source": "test_robust_processor.py", "target": "scripts/test/test_robust_processor.py", "timestamp": "2025-09-19T11:20:14.964588"}, {"source": "test_professional_standards.py", "target": "scripts/test/test_professional_standards.py", "timestamp": "2025-09-19T11:20:14.964723"}, {"source": "test_live_commands.py", "target": "scripts/test/test_live_commands.py", "timestamp": "2025-09-19T11:20:14.964944"}, {"source": "test_answer_quality.py", "target": "scripts/test/test_answer_quality.py", "timestamp": "2025-09-19T11:20:14.965070"}, {"source": "create_ai_query_interpreter.py", "target": "scripts/archive/create_ai_query_interpreter.py", "timestamp": "2025-09-19T11:20:14.965236"}, {"source": "create_fixed_pipeline.py", "target": "scripts/archive/create_fixed_pipeline.py", "timestamp": "2025-09-19T11:20:14.965983"}, {"source": "create_working_pipeline.py", "target": "scripts/archive/create_working_pipeline.py", "timestamp": "2025-09-19T11:20:14.966124"}, {"source": "create_watchlist_tables.py", "target": "scripts/archive/create_watchlist_tables.py", "timestamp": "2025-09-19T11:20:14.966257"}, {"source": "optimize_performance.py", "target": "scripts/archive/optimize_performance.py", "timestamp": "2025-09-19T11:20:14.966372"}, {"source": "optimize_bot_performance.py", "target": "scripts/archive/optimize_bot_performance.py", "timestamp": "2025-09-19T11:20:14.966474"}, {"source": "demo_dashboard.py", "target": "scripts/archive/demo_dashboard.py", "timestamp": "2025-09-19T11:20:14.966606"}, {"source": "demo_enhanced_analysis.py", "target": "scripts/archive/demo_enhanced_analysis.py", "timestamp": "2025-09-19T11:20:14.966709"}, {"source": "check_tables.py", "target": "scripts/debug/check_tables.py", "timestamp": "2025-09-19T11:20:14.966826"}, {"source": "check_table_structure.py", "target": "scripts/debug/check_table_structure.py", "timestamp": "2025-09-19T11:20:14.966918"}, {"source": "debug_test.log", "target": "logs/archive/debug_test.log", "timestamp": "2025-09-19T11:20:14.967055"}, {"source": "continuous_trading_monitor.log", "target": "logs/archive/continuous_trading_monitor.log", "timestamp": "2025-09-19T11:20:14.967144"}, {"source": "final_system_test_results.log", "target": "logs/archive/final_system_test_results.log", "timestamp": "2025-09-19T11:20:14.967276"}, {"source": "working_system_test_results.log", "target": "logs/archive/working_system_test_results.log", "timestamp": "2025-09-19T11:20:14.967381"}, {"source": "comprehensive_test_results.log", "target": "logs/archive/comprehensive_test_results.log", "timestamp": "2025-09-19T11:20:14.967477"}, {"source": "real_trading_audit.log", "target": "logs/archive/real_trading_audit.log", "timestamp": "2025-09-19T11:20:14.967599"}, {"source": "all_tests.log", "target": "logs/archive/all_tests.log", "timestamp": "2025-09-19T11:20:14.967695"}, {"source": "final_trading_audit.log", "target": "logs/archive/final_trading_audit.log", "timestamp": "2025-09-19T11:20:14.967798"}, {"source": "test_run_latest.log", "target": "logs/archive/test_run_latest.log", "timestamp": "2025-09-19T11:20:14.967890"}, {"source": "enhanced_trading_audit.log", "target": "logs/archive/enhanced_trading_audit.log", "timestamp": "2025-09-19T11:20:14.968004"}, {"source": "final_test_results.log", "target": "logs/archive/final_test_results.log", "timestamp": "2025-09-19T11:20:14.968108"}, {"source": "trading_system_audit.log", "target": "logs/archive/trading_system_audit.log", "timestamp": "2025-09-19T11:20:14.968208"}, {"source": "docker_services_test_results.log", "target": "logs/archive/docker_services_test_results.log", "timestamp": "2025-09-19T11:20:14.968324"}, {"source": "architecture.json", "target": "docs/reports/architecture.json", "timestamp": "2025-09-19T11:20:14.968446"}, {"source": "response_metrics.json", "target": "docs/reports/response_metrics.json", "timestamp": "2025-09-19T11:20:14.968530"}, {"source": "architecture_detailed.json", "target": "docs/reports/architecture_detailed.json", "timestamp": "2025-09-19T11:20:14.968637"}, {"source": "comprehensive_market_analysis_report_20250916_001933.json", "target": "docs/reports/comprehensive_market_analysis_report_20250916_001933.json", "timestamp": "2025-09-19T11:20:14.968737"}, {"source": "architecture_pretty.json", "target": "docs/reports/architecture_pretty.json", "timestamp": "2025-09-19T11:20:14.968823"}, {"source": "current_test_files.txt", "target": "docs/reports/current_test_files.txt", "timestamp": "2025-09-19T11:20:14.968916"}, {"source": "dashboard_requirements.txt", "target": "docs/reports/dashboard_requirements.txt", "timestamp": "2025-09-19T11:20:14.969007"}, {"source": "codebase_explorer_requirements.txt", "target": "docs/reports/codebase_explorer_requirements.txt", "timestamp": "2025-09-19T11:20:14.969105"}, {"source": "security-requirements.txt", "target": "docs/reports/security-requirements.txt", "timestamp": "2025-09-19T11:20:14.969184"}, {"source": "cleanup_validation_report.txt", "target": "docs/reports/cleanup_validation_report.txt", "timestamp": "2025-09-19T11:20:14.969285"}, {"source": "market_analysis_summary_20250916_001933.txt", "target": "docs/reports/market_analysis_summary_20250916_001933.txt", "timestamp": "2025-09-19T11:20:14.969392"}, {"source": "test_docker_setup.sh", "target": "scripts/archive/test_docker_setup.sh", "timestamp": "2025-09-19T11:20:14.970485"}, {"source": "test_supabase_docker.sh", "target": "scripts/archive/test_supabase_docker.sh", "timestamp": "2025-09-19T11:20:14.970816"}, {"source": ".env.tmp", "target": "temp/misc/.env.tmp", "timestamp": "2025-09-19T11:20:14.970984"}, {"source": "docker-nuke", "target": "scripts/archive/docker-nuke", "timestamp": "2025-09-19T11:20:14.971137"}, {"source": "system_api.py", "target": "scripts/archive/system_api.py", "timestamp": "2025-09-19T11:20:14.971274"}, {"source": "config_api.py", "target": "scripts/archive/config_api.py", "timestamp": "2025-09-19T11:20:14.971373"}, {"source": "system_dashboard.html", "target": "temp/html/system_dashboard.html", "timestamp": "2025-09-19T11:20:14.971478"}, {"source": "simple_test.py", "target": "scripts/test/simple_test.py", "timestamp": "2025-09-19T11:20:14.971581"}, {"source": "temp_logger_fix.py", "target": "scripts/fix/temp_logger_fix.py", "timestamp": "2025-09-19T11:20:14.971671"}, {"source": "working_solution.py", "target": "scripts/archive/working_solution.py", "timestamp": "2025-09-19T11:20:14.971774"}, {"source": "fixed_ai_service_wrapper.py", "target": "scripts/archive/fixed_ai_service_wrapper.py", "timestamp": "2025-09-19T11:20:14.971879"}, {"source": "final_docs_cleanup.py", "target": "scripts/archive/final_docs_cleanup.py", "timestamp": "2025-09-19T11:20:14.971973"}, {"source": "quick_start_db.py", "target": "scripts/archive/quick_start_db.py", "timestamp": "2025-09-19T11:20:14.972061"}, {"source": "working_db_solution.py", "target": "scripts/archive/working_db_solution.py", "timestamp": "2025-09-19T11:20:14.972131"}, {"source": "performance_optimization.py", "target": "scripts/archive/performance_optimization.py", "timestamp": "2025-09-19T11:20:14.972212"}, {"source": "visualize_recent_query.py", "target": "scripts/archive/visualize_recent_query.py", "timestamp": "2025-09-19T11:20:14.972362"}, {"source": ".coverage", "target": "temp/misc/.coverage", "timestamp": "2025-09-19T11:20:14.972530"}, {"source": "working_with_existing_tables.py", "target": "scripts/archive/working_with_existing_tables.py", "timestamp": "2025-09-19T11:20:14.972648"}, {"source": ".cursorignore", "target": "temp/misc/.cursorignore", "timestamp": "2025-09-19T11:20:14.972788"}, {"source": "comprehensive_test.py", "target": "scripts/test/comprehensive_test.py", "timestamp": "2025-09-19T11:20:14.972901"}, {"source": "project_manifest.md", "target": "docs/reports/project_manifest.md", "timestamp": "2025-09-19T11:20:14.973041"}, {"source": "nvda_price_target_analysis.py", "target": "scripts/archive/nvda_price_target_analysis.py", "timestamp": "2025-09-19T11:20:14.973175"}, {"source": ".env.example", "target": "temp/misc/.env.example", "timestamp": "2025-09-19T11:20:14.973322"}, {"source": "improve_pattern_matching.py", "target": "scripts/archive/improve_pattern_matching.py", "timestamp": "2025-09-19T11:20:14.973450"}, {"source": "final_working_solution.py", "target": "scripts/archive/final_working_solution.py", "timestamp": "2025-09-19T11:20:14.973552"}, {"source": "pipeline_events.py", "target": "scripts/archive/pipeline_events.py", "timestamp": "2025-09-19T11:20:14.973770"}, {"source": "simple_db_test.py", "target": "scripts/test/simple_db_test.py", "timestamp": "2025-09-19T11:20:14.974005"}, {"source": "example_pipeline_integration.py", "target": "scripts/archive/example_pipeline_integration.py", "timestamp": "2025-09-19T11:20:14.974272"}, {"source": "simple_pipeline_emitter.py", "target": "scripts/archive/simple_pipeline_emitter.py", "timestamp": "2025-09-19T11:20:14.974508"}, {"source": "import_test.py", "target": "scripts/test/import_test.py", "timestamp": "2025-09-19T11:20:14.974628"}, {"source": "nvda_price_target_analysis_modular.py", "target": "scripts/archive/nvda_price_target_analysis_modular.py", "timestamp": "2025-09-19T11:20:14.974720"}, {"source": "claude.html", "target": "temp/html/claude.html", "timestamp": "2025-09-19T11:20:14.974821"}, {"source": "folder_cleanup_plan.md", "target": "docs/cleanup/folder_cleanup_plan.md", "timestamp": "2025-09-19T11:20:14.975531"}], "errors": [], "target_directories": {"scripts/archive": "Archived development scripts", "scripts/debug": "Debug and diagnostic scripts", "scripts/fix": "One-time fix scripts", "scripts/generate": "Code generation scripts", "scripts/test": "Individual test scripts", "docs/reports": "Generated reports and analysis", "logs/archive": "Archived log files", "temp/misc": "Temporary and miscellaneous files", "temp/html": "HTML files and dashboards"}}