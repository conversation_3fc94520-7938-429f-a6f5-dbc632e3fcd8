# Folder Structure Cleanup Plan

## 🚨 **Current Problems**

### **Docker Configuration Chaos**
- 8 Docker files scattered across multiple locations
- 3 docker-compose files with overlapping functionality
- No clear hierarchy or organization

### **Requirements Fragmentation**
- 4+ requirements files in different locations
- Minimal requirements in containers folder
- No clear dependency management

### **Folder Structure Issues**
- Empty folders (requirements/services/)
- Scattered configs across multiple locations
- No clear organization strategy

## 🎯 **Proposed Cleanup**

### **1. Docker Consolidation**
```
docker/
├── Dockerfile                    # Main application Dockerfile
├── docker/compose/development.yml           # Main development compose
├── docker-compose.prod.yml      # Production compose
└── services/                    # Service-specific Dockerfiles
    ├── api.Dockerfile
    ├── discord-bot.Dockerfile
    └── webhook-ingest.Dockerfile
```

### **2. Requirements Consolidation**
```
requirements/
├── base.txt                     # Core dependencies
├── dev.txt                      # Development dependencies
├── prod.txt                     # Production dependencies
└── containers/                  # Container-specific requirements
    ├── api.txt
    ├── discord-bot.txt
    └── webhook-ingest.txt
```

### **3. Examples Organization**
```
examples/
├── README.md                    # Examples documentation
├── basic/                       # Basic usage examples
│   ├── ask_command.py
│   └── database_usage.py
├── advanced/                    # Advanced examples
│   └── debug_event_integration.py
└── templates/                   # Code templates
```

### **4. Config Consolidation**
```
config/
├── development.yaml             # Development config
├── production.yaml              # Production config
├── ai_models.yaml               # AI model configuration
└── monitoring.conf              # Performance monitoring
```

## 📋 **Cleanup Steps**

### **Step 1: Docker Cleanup**
1. Move all Dockerfiles to `docker/` folder
2. Consolidate docker-compose files
3. Remove duplicate configurations
4. Update references in code

### **Step 2: Requirements Cleanup**
1. Create base requirements file
2. Organize by environment (dev/prod)
3. Remove duplicate requirements
4. Update Dockerfiles to use new structure

### **Step 3: Examples Organization**
1. Add README.md to examples
2. Organize by complexity level
3. Add usage instructions
4. Create code templates

### **Step 4: Config Consolidation**
1. Organize config files by environment
2. Remove duplicate configurations
3. Add validation schemas
4. Update code references

## 🎉 **Expected Benefits**

1. **Clear Organization** - Logical folder structure
2. **Reduced Duplication** - Single source of truth
3. **Easier Maintenance** - Centralized configurations
4. **Better Documentation** - Clear examples and guides
5. **Simplified Deployment** - Streamlined Docker setup

## ⚠️ **Risks & Mitigation**

| Risk | Mitigation |
|------|------------|
| Broken references | Update all code references |
| Lost configurations | Backup before moving |
| Deployment issues | Test thoroughly |
| User confusion | Clear migration guide |

## 📊 **Current vs Target**

| Metric | Current | Target | Improvement |
|--------|---------|--------|-------------|
| Docker files | 8 scattered | 4 organized | 50% reduction |
| Requirements files | 4+ fragmented | 3 organized | 25% reduction |
| Empty folders | 1 | 0 | 100% elimination |
| Config files | Scattered | Organized | 100% better |
