# Real-Time Data Pipeline Architecture Design

## Overview
The real-time data pipeline extends the existing batch fetching in `src/bot/pipeline/commands/analyze/stages/fetch_data.py` to support live market data streaming. It builds on the async Python structure, using Polygon.io for WebSocket-based real-time updates while maintaining compatibility with the `PipelineContext` and `market_data` dict format.

Key goals:
- Seamless integration: Real-time data merges into batch-fetched `market_data` without breaking existing analysis.
- Event-driven: Use asyncio for non-blocking streams, handling updates via callbacks.
- Reliability: Include reconnection, buffering, and validation to handle network issues.
- Scalability: Support multiple tickers via subscription management.

## Components

### 1. Data Source Selection
- **Primary: Polygon.io WebSockets**
  - Endpoints: `wss://socket.polygon.io/stocks` for real-time quotes, trades, aggregates.
  - Authentication: API key from config.yaml (add `polygon_api_key`).
  - Data Format: JSON messages with fields like `sym` (symbol), `p` (price), `s` (size), `t` (timestamp) – map to existing keys (e.g., `price`, `timestamp`).
  - Fallback: REST API for initial batch/historical if WebSocket fails.
- **Compatibility**: Extends `MarketDataService` in `src/api/data/market_data_service.py`. Polygon client (`polygon-api-client`) is async-friendly; install via `pip install polygon-api-client` (update requirements.txt).
- **Alternatives Considered**:
  - Alpha Vantage: Limited WebSocket throughput (5 calls/min free).
  - Finnhub: Good free tier, but Polygon better for US stocks depth.

### 2. Core Modules
- **WebSocket Handler (`src/shared/data_pipeline/websocket_handler.py`)**:
  - Class: `PolygonWebSocketClient` inheriting from `asyncio.WebSocketClientProtocol`.
  - Methods:
    - `connect(api_key: str)`: Establish WebSocket connection.
    - `subscribe(tickers: list)`: Send subscription messages (e.g., `{"action":"subscribe","params":"T.*"}` for trades).
    - `on_message(data: dict)`: Parse incoming JSON, update local cache, emit events via `asyncio.Event` or pub/sub.
    - `handle_disconnect()`: Auto-reconnect with exponential backoff (max 5 retries).
  - Integration: Singleton instance shared across pipelines.

- **Data Buffering & Validation (`src/shared/data_pipeline/buffer_manager.py`)**:
  - Class: `DataBuffer` using `collections.deque` for fixed-size window (e.g., last 100 updates).
  - Methods:
    - `add_update(ticker: str, data: dict)`: Append validated data (check timestamp, price bounds).
    - `get_latest(ticker: str)`: Return most recent snapshot.
    - `compute_indicators()`: On-the-fly TA (e.g., SMA) using buffered data, compatible with `technical_indicators`.
  - Validation: Schema check with Pydantic models matching batch format; discard outliers (e.g., price > 2x prev).

- **Event-Driven Processor (`src/shared/data_pipeline/event_processor.py`)**:
  - Uses `asyncio.Queue` for incoming events from WebSocket.
  - Consumer loop: Process updates, trigger alerts if thresholds met (e.g., price change > 5%).
  - Integration Points:
    - Hook into `PipelineContext`: Update `context.processing_results['market_data']['live_updates']` with stream.
    - Pub/Sub: Use Redis (existing in docker) for broadcasting to bot/dashboard.

### 3. Integration with Existing Pipeline
- **Modification to Fetch Data Stage**:
  - In `fetch_data.py`: After batch fetch, initialize WebSocket if `real_time_enabled` in config.
  - Add `async def start_live_feed(context: PipelineContext)`: Subscribe to ticker, merge live data into `market_data['live_price']`, `market_data['live_timestamp']`.
  - Fallback: If WebSocket unavailable, use polling (every 5s via REST).

- **Alert Mechanism (`src/bot/alerts/real_time_alerts.py`)**:
  - Class: `AlertManager`.
  - Triggers: On price volatility, indicator crossovers (e.g., RSI > 70).
  - Delivery: Async post to Discord via bot client in `src/bot`.

- **Configuration**:
  - Add to `config.yaml`: `real_time: {enabled: true, provider: polygon, api_key: "...", buffer_size: 100, reconnect_delay: 5}`.
  - Environment: Load via `src/shared/config_loader.py`.

### 4. Data Flow
1. Batch Init: `fetch_data` gets historical/current via REST.
2. Stream Start: On pipeline run, connect WebSocket, subscribe to ticker(s).
3. Update Loop: Incoming messages → Buffer → Validate → Merge to context → Trigger events (analysis/alerts).
4. Shutdown: Graceful unsubscribe on pipeline end.

### 5. Error Handling & Monitoring
- Circuit Breaker: Use existing `core/circuit_breaker.py` for provider failures.
- Logging: Extend `get_trading_logger` with stream-specific tags.
- Metrics: Track latency, drop rate in `utils/metrics.py`; alert on >10% drops.
- Testing: Unit tests for handler (mock WebSocket), integration with simulated streams.

### 6. Dependencies & Setup
- New Packages: `polygon-api-client`, `websockets` (if needed).
- Database: Store live snapshots in Postgres (extend existing schema).
- Deployment: Update `docker-compose.yml` for any new services (e.g., dedicated stream processor).

This design ensures minimal disruption to batch flow while enabling real-time capabilities. Next steps: Implement WebSocket handler.