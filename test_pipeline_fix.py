#!/usr/bin/env python3
"""
Quick test to verify the pipeline bug is fixed
"""

import asyncio
import sys
import os

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

async def test_single_query():
    """Test a single query to verify the pipeline works"""
    
    try:
        from src.bot.pipeline.commands.ask.pipeline import AskPipeline
        from src.bot.pipeline.commands.ask.config import AskPipelineConfig
        
        print("🔧 Creating pipeline...")
        config = AskPipelineConfig()
        pipeline = AskPipeline(config)
        
        print("🔍 Testing simple query...")
        query = "What is AAPL?"
        
        result = await pipeline.process_query(
            query=query,
            user_id="test_user",
            username="pipeline_tester"
        )
        
        print(f"✅ Pipeline executed successfully!")
        print(f"📊 Result type: {type(result)}")
        print(f"📝 Result keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
        
        if isinstance(result, dict):
            if 'error' in result:
                print(f"❌ Error in result: {result['error']}")
                return False
            else:
                print(f"✅ Success! Response: {result.get('response', 'No response')[:100]}...")
                return True
        else:
            print(f"⚠️ Unexpected result type: {result}")
            return False
            
    except Exception as e:
        print(f"❌ Pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    print("🎯 PIPELINE FIX TEST")
    print("=" * 40)
    
    success = await test_single_query()
    
    if success:
        print("\n✅ PIPELINE FIX SUCCESSFUL!")
        print("The ProcessingResult bug has been resolved.")
    else:
        print("\n❌ PIPELINE STILL HAS ISSUES")
        print("Further debugging needed.")
    
    return success

if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
