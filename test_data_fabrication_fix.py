#!/usr/bin/env python3
"""
Test if AI stops fabricating market data after stronger system prompt fixes
"""

import asyncio
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_data_fabrication_fix():
    """Test if AI stops making up fake market data"""
    
    print("🧪 TESTING DATA FABRICATION FIX")
    print("=" * 50)
    
    # Import the AI processor
    from src.shared.ai_services.ai_processor_robust import RobustFinancialAnalyzer
    
    # Create analyzer instance
    analyzer = RobustFinancialAnalyzer()
    
    # Test queries that previously caused data fabrication
    test_queries = [
        "What's happening in the market today?",
        "Give me a trading recommendation for this week",
        "What are some good options expiring this Friday?",
        "What's the current price of NVDA?",
        "What's the best tech stock to buy right now?"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n📝 Query {i}: '{query}'")
        print("-" * 50)
        
        try:
            # Test the query processing
            response = await analyzer.answer_general_question(query)
            
            print(f"Response: {response}")
            
            # Check for signs of data fabrication
            fabrication_indicators = [
                # Specific fake prices
                "$116.25", "$230", "$574.50", "$575",
                # Fake market events
                "BoJ stood pat", "iPhone 17 pre-order", "Fed dot-plot",
                # Fake economic data
                "retail sales missed", "PMI", "existing-home sales",
                # Fake specific numbers that are too precise
                "09:40 AM ET", "09:41 ET", "+8 bp overnight",
                # Fake options data
                "pay ≤0.90", "target 1.80–2.00", "IV 18 → 19.5"
            ]
            
            # Check for good responses (admitting no data)
            good_responses = [
                "I need real market data",
                "I don't have access to live market data",
                "Please specify a symbol for real-time analysis",
                "I need current market data to provide accurate",
                "without real-time data"
            ]
            
            has_fabrication = any(indicator in response for indicator in fabrication_indicators)
            has_good_response = any(good in response for good in good_responses)
            
            if has_fabrication:
                print("❌ AI is still fabricating market data")
                print("   Found fabricated elements in response")
            elif has_good_response:
                print("✅ AI properly admits it needs real data")
            else:
                print("⚠️  Response unclear - may need manual review")
            
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_data_fabrication_fix())
