#!/usr/bin/env python3
"""
Test if the AI stops hallucinating after the stronger anti-fabrication rules
"""

import asyncio
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_hallucination_fix():
    """Test if AI stops creating fake trading scenarios"""
    
    print("🧪 TESTING HALLUCINATION FIX")
    print("=" * 50)
    
    # Import the AI processor
    from src.shared.ai_services.ai_processor_robust import RobustFinancialAnalyzer
    
    # Create analyzer instance
    analyzer = RobustFinancialAnalyzer()
    
    # Test queries that previously caused hallucination
    test_queries = [
        "Give me an options strategy for NVDA",
        "What's a good trade for this week?",
        "Show me a technical setup for earnings plays",
        "I want to trade options before earnings",
        "Give me a specific trading recommendation"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n📝 Query {i}: '{query}'")
        print("-" * 50)
        
        try:
            # Test the query processing
            response = await analyzer.answer_general_question(query)
            
            print(f"Response: {response}")
            
            # Check for signs of hallucination/fabrication
            hallucination_indicators = [
                # Specific fake dates
                "22 May", "13 May", "17 May", "21 May", "Wed-after-next",
                # Fake price levels
                "$880", "$920", "$860", "$900", "balance zone",
                # Fake market data
                "implied vol is ≈ 58%", "dealers are still short", "1.1 M contracts",
                # Fake specific numbers
                "6.5–7 % of spot", "realize 8–10 %", "40–60 % of debit",
                # Fake options structures
                "17 May expiry", "at-the-money strangle", "880-920 strangle"
            ]
            
            # Check for good responses (no fabrication)
            good_responses = [
                "I need current market data",
                "I need real market data to provide accurate",
                "Please specify a symbol for real-time analysis",
                "I don't have access to live market data",
                "educational content about trading concepts"
            ]
            
            has_hallucination = any(indicator in response for indicator in hallucination_indicators)
            has_good_response = any(good in response for good in good_responses)
            
            if has_hallucination:
                print("❌ AI is still hallucinating/fabricating data")
                print("   Found fabricated elements in response")
            elif has_good_response:
                print("✅ AI properly refuses to fabricate data")
            else:
                print("⚠️  Response unclear - may need manual review")
            
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_hallucination_fix())
