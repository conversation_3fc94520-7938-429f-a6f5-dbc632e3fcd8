================================================================================
COMPREHENSIVE QUALITY ASSESSMENT REPORT
================================================================================
Generated: 2025-09-19 22:37:54
Total Test Duration: 51.06 seconds

📊 IMPORT PERFORMANCE
--------------------------------------------------
  Package Ask Import: 30
    import_time_ms: 2137.82
    memory_delta_mb: 114.88
    status: SUCCESS
  Package Analyze Import: 100
    import_time_ms: 9.5
    memory_delta_mb: 0.25
    status: SUCCESS
  Direct Executor Import: 100
    import_time_ms: 0.08
    memory_delta_mb: 0.0
    status: SUCCESS
  Direct Pipeline Import: 100
    import_time_ms: 0.19
    memory_delta_mb: 0.0
    status: SUCCESS
  Batch Processor Import: 100
    import_time_ms: 0.13
    memory_delta_mb: 0.0
    status: SUCCESS
  Parallel Pipeline Import: 100
    import_time_ms: 0.07
    memory_delta_mb: 0.0
    status: SUCCESS

📊 RESPONSE QUALITY
--------------------------------------------------
  Simple Stock Query: 30
    execution_time_ms: 15008.73
    memory_delta_mb: 9.38
    response_length: 0
    status: completed
    has_response: False
  Technical Analysis Query: 35
    execution_time_ms: 14750.98
    memory_delta_mb: 2.12
    response_length: 0
    status: completed
    has_response: False
  Multi-Symbol Query: 50
    execution_time_ms: 1804.29
    memory_delta_mb: 0.75
    response_length: 0
    status: completed
    has_response: False
  Market Sentiment Query: 50
    execution_time_ms: 2179.61
    memory_delta_mb: 0.62
    response_length: 0
    status: completed
    has_response: False
  Invalid Symbol Query: 50
    execution_time_ms: 11180.48
    memory_delta_mb: 0.12
    response_length: 0
    status: completed
    has_response: False
  General Question: 50
    execution_time_ms: 1896.85
    memory_delta_mb: 0.38
    response_length: 0
    status: completed
    has_response: False
  Analyze_Popular Stock: 35
    execution_time_ms: 540.52
    memory_delta_mb: 7.12
    response_length: 514
    status: failed
    has_response: True
  Analyze_Tech Stock: 35
    execution_time_ms: 344.61
    memory_delta_mb: 0.12
    response_length: 514
    status: failed
    has_response: True
  Analyze_ETF: 35
    execution_time_ms: 315.33
    memory_delta_mb: 0.12
    response_length: 513
    status: failed
    has_response: True
  Analyze_Crypto-Related: 35
    execution_time_ms: 349.6
    memory_delta_mb: 0.0
    response_length: 514
    status: failed
    has_response: True
  Analyze_Invalid Symbol: 35
    execution_time_ms: 1.1
    memory_delta_mb: 0.0
    response_length: 405
    status: failed
    has_response: True

📊 ERROR HANDLING
--------------------------------------------------
  Invalid Module Import: 100
    error_type: ImportError
    error_message: No module named 'src.bot.pipeline.commands.nonexistent'
    status: EXPECTED_ERROR
  Circular Import Resilience: 85
    error_type: ImportError
    error_message: cannot import name 'execute_ask_pipeline' from 'builtins' (unknown location)
    status: EXPECTED_ERROR
  Partial Import Failure: 75
    error_type: ImportError
    error_message: cannot import name 'nonexistent_function' from 'src.bot.pipeline.commands.ask' (/home/<USER>/Desktop/tradingview-automatio/src/bot/pipeline/commands/ask/__init__.py)
    status: EXPECTED_ERROR
