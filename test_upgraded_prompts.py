#!/usr/bin/env python3
"""
Test the upgraded prompts.py to ensure all functions work correctly
"""

import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_upgraded_prompts():
    """Test all the new functions in the upgraded prompts.py"""
    
    print("🧪 TESTING UPGRADED PROMPTS.PY")
    print("=" * 50)
    
    try:
        # Import the upgraded prompts module
        from src.bot.pipeline.commands.ask.stages.prompts import (
            get_system_prompt, 
            get_fallback_responses,
            get_model_config_for_intent,
            QUALITY_STANDARDS,
            MONITORING_CONFIG
        )
        
        print("\n✅ All imports successful")
        
        # Test 1: System prompt with date injection
        print("\n📝 Test 1: System Prompt with Date Injection")
        print("-" * 40)
        system_prompt = get_system_prompt()
        
        if "2025" in system_prompt and "Today's date:" in system_prompt:
            print("✅ Date/time injection working")
        else:
            print("❌ Date/time injection failed")
            
        if "NEVER EVER fabricate" in system_prompt:
            print("✅ Strong anti-fabrication language present")
        else:
            print("❌ Anti-fabrication language missing")
            
        # Test 2: Fallback responses with date
        print("\n📝 Test 2: Fallback Responses with Date")
        print("-" * 40)
        fallback_responses = get_fallback_responses()
        
        if "2025" in fallback_responses["ai_error"]:
            print("✅ Fallback responses include current date")
        else:
            print("❌ Fallback responses missing date context")
            
        if "no_real_data" in fallback_responses:
            print("✅ New 'no_real_data' fallback response added")
        else:
            print("❌ Missing 'no_real_data' fallback response")
        
        # Test 3: Model configuration for different intents
        print("\n📝 Test 3: Model Configuration for Intents")
        print("-" * 40)
        
        test_intents = ["technical_analysis", "options_strategy", "price_check", "educational"]
        
        for intent in test_intents:
            config = get_model_config_for_intent(intent)
            if "task_type" in config and "complexity" in config:
                print(f"✅ {intent}: {config['task_type']} (complexity: {config['complexity']})")
            else:
                print(f"❌ {intent}: Invalid configuration")
        
        # Test 4: Quality standards updates
        print("\n📝 Test 4: Quality Standards Updates")
        print("-" * 40)
        
        if "I don't have live calendar access" in QUALITY_STANDARDS["prohibited_language"]:
            print("✅ Prohibited language includes date-related phrases")
        else:
            print("❌ Missing date-related prohibited language")
            
        if "anti_fabrication_checks" in QUALITY_STANDARDS:
            print("✅ Anti-fabrication checks added to quality standards")
        else:
            print("❌ Missing anti-fabrication checks")
            
        if QUALITY_STANDARDS["max_response_length"] == 2000:
            print("✅ Response length consistency fixed")
        else:
            print("❌ Response length still inconsistent")
        
        # Test 5: Monitoring configuration
        print("\n📝 Test 5: Monitoring Configuration")
        print("-" * 40)
        
        if MONITORING_CONFIG.get("alert_on_fabrication_attempts"):
            print("✅ Fabrication monitoring enabled")
        else:
            print("❌ Missing fabrication monitoring")
            
        if MONITORING_CONFIG.get("monitor_date_context"):
            print("✅ Date context monitoring enabled")
        else:
            print("❌ Missing date context monitoring")
        
        print("\n🎉 UPGRADE TEST COMPLETE!")
        print("All major improvements have been successfully implemented.")
        
    except Exception as e:
        print(f"❌ Error testing upgraded prompts: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_upgraded_prompts()
