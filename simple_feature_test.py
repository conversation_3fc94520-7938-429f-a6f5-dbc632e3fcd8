#!/usr/bin/env python3
"""
Simple Feature Test - Quick validation of new trading features
"""

import asyncio
import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_trading_strategies():
    """Test trading strategies functionality"""
    print("🧪 Testing Trading Strategies...")
    
    try:
        from bot.advanced_trading_strategies import advanced_strategies
        
        # Initialize
        await advanced_strategies.initialize()
        
        # Generate test data
        dates = pd.date_range(start='2024-01-01', end='2024-01-31', freq='D')
        np.random.seed(42)
        prices = 100 * np.cumprod(1 + np.random.normal(0, 0.02, len(dates)))
        
        data = pd.DataFrame({
            'close': prices,
            'high': prices * (1 + np.random.uniform(0, 0.02, len(dates))),
            'low': prices * (1 - np.random.uniform(0, 0.02, len(dates))),
            'volume': np.random.randint(1000000, 5000000, len(dates))
        }, index=dates)
        
        # Test strategy execution
        signals = await advanced_strategies.run_strategy('momentum_rsi', 'AAPL', data)
        print(f"✅ Generated {len(signals)} signals")
        
        # Test backtesting
        result = await advanced_strategies.backtest_strategy('momentum_rsi', 'AAPL', data)
        if result:
            print(f"✅ Backtest: {result.total_return:.2%} return, {result.sharpe_ratio:.2f} Sharpe")
        
        await advanced_strategies.cleanup()
        return True
        
    except Exception as e:
        print(f"❌ Trading strategies test failed: {e}")
        return False

async def test_risk_management():
    """Test risk management functionality"""
    print("🧪 Testing Risk Management...")
    
    try:
        from bot.risk_management_system import risk_manager, Position, PositionType
        
        await risk_manager.initialize()
        
        # Test position sizing
        size = await risk_manager.calculate_position_size('AAPL', 150.0, 140.0, 0.02)
        print(f"✅ Position size calculated: {size:.2f} shares")
        
        # Test stop loss
        stop_loss = await risk_manager.calculate_stop_loss('AAPL', 150.0, PositionType.LONG)
        print(f"✅ Stop loss calculated: ${stop_loss:.2f}")
        
        # Test adding position
        position = Position(
            symbol='AAPL',
            position_type=PositionType.LONG,
            quantity=100,
            entry_price=150.0,
            current_price=155.0,
            entry_time=datetime.now()
        )
        
        success = await risk_manager.add_position(position)
        print(f"✅ Position added: {success}")
        
        # Test risk metrics
        metrics = await risk_manager.get_portfolio_risk_metrics()
        print(f"✅ Risk metrics: ${metrics.portfolio_value:,.2f} portfolio value")
        
        await risk_manager.cleanup()
        return True
        
    except Exception as e:
        print(f"❌ Risk management test failed: {e}")
        return False

async def test_sentiment_analysis():
    """Test sentiment analysis functionality"""
    print("🧪 Testing Sentiment Analysis...")
    
    try:
        from bot.market_sentiment_analyzer import sentiment_analyzer, SentimentSource
        
        await sentiment_analyzer.initialize()
        
        # Test sentiment analysis
        test_text = "AAPL is looking very bullish with strong momentum and positive earnings"
        sentiment = await sentiment_analyzer.analyze_sentiment('AAPL', test_text, SentimentSource.NEWS)
        
        if sentiment:
            print(f"✅ Sentiment: {sentiment.sentiment_type.value} (confidence: {sentiment.confidence:.2f})")
        
        # Test market overview
        overview = await sentiment_analyzer.get_market_sentiment_overview()
        print(f"✅ Market sentiment: {overview.get('overall_sentiment', 'unknown')}")
        
        await sentiment_analyzer.cleanup()
        return True
        
    except Exception as e:
        print(f"❌ Sentiment analysis test failed: {e}")
        return False

async def test_performance_analytics():
    """Test performance analytics functionality"""
    print("🧪 Testing Performance Analytics...")
    
    try:
        from bot.performance_analytics import performance_analytics
        
        await performance_analytics.initialize()
        
        # Generate sample trades
        trades = []
        for i in range(10):
            trade = {
                'symbol': 'AAPL',
                'strategy': 'test',
                'entry_time': datetime.now() - timedelta(days=10-i),
                'exit_time': datetime.now() - timedelta(days=9-i),
                'entry_price': 100 + np.random.normal(0, 5),
                'exit_price': 100 + np.random.normal(0, 5),
                'pnl': np.random.normal(0, 0.02),
                'capital': 10000 + (i * 100)
            }
            trades.append(trade)
        
        # Test performance calculation
        report = await performance_analytics.calculate_performance_metrics(trades)
        
        if report:
            print(f"✅ Performance: {report.total_return:.2%} return, {report.sharpe_ratio:.2f} Sharpe")
            print(f"✅ Trades: {report.total_trades} total, {report.winning_trades} winning")
        
        await performance_analytics.cleanup()
        return True
        
    except Exception as e:
        print(f"❌ Performance analytics test failed: {e}")
        return False

async def main():
    """Run all tests"""
    print("🚀 Testing New Trading Features")
    print("=" * 50)
    
    results = []
    
    # Test each feature
    results.append(await test_trading_strategies())
    results.append(await test_risk_management())
    results.append(await test_sentiment_analysis())
    results.append(await test_performance_analytics())
    
    # Summary
    print("=" * 50)
    print("📊 TEST RESULTS")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    print(f"Passed: {passed}/{total}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
    else:
        print("⚠️ Some tests failed - check logs above")
    
    return results

if __name__ == "__main__":
    asyncio.run(main())
