#!/usr/bin/env python3
"""
Test full response to see what's actually being generated
"""

import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_full_response():
    """Test full responses to see what's happening"""
    
    print("🧪 TESTING FULL RESPONSES")
    print("=" * 50)
    
    # Import the AI processor
    from src.shared.ai_services.ai_processor_robust import RobustFinancialAnalyzer
    
    # Create analyzer instance
    analyzer = RobustFinancialAnalyzer()
    
    # Test queries that should trigger fallback responses
    test_queries = [
        "Hello",
        "What's the weather?", 
        "What time is it?",
    ]
    
    for query in test_queries:
        print(f"\n📝 Query: '{query}'")
        print("-" * 50)
        
        # Test the actual fallback method
        response = analyzer._generate_fallback_response(query)
        print(f"Full Response: {response}")
        
        # Extract symbols
        import re
        symbols = re.findall(r'\$([A-Z]+)', response)
        print(f"Symbols found: {symbols}")

if __name__ == "__main__":
    test_full_response()
