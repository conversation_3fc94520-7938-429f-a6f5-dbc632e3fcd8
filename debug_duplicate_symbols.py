#!/usr/bin/env python3
"""
Debug why symbols are being duplicated in responses
"""

import asyncio
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def debug_duplicate_symbols():
    """Debug the duplicate symbol issue"""
    
    print("🔍 DEBUGGING DUPLICATE SYMBOLS")
    print("=" * 50)
    
    # Import the AI processor
    from src.shared.ai_services.ai_processor_robust import RobustFinancialAnalyzer
    
    # Create analyzer instance
    analyzer = RobustFinancialAnalyzer()
    
    # Test the query that showed heavy QQQ duplication from the previous test
    query = "What's the best tech stock to buy right now?"
    
    print(f"Query: '{query}'")
    print("-" * 50)
    
    try:
        # Get the full response
        response = await analyzer.answer_general_question(query)
        
        print("FULL RESPONSE:")
        print("=" * 50)
        print(response)
        print("=" * 50)
        
        # Extract symbols with position information
        import re
        
        # Find all symbol matches with their positions
        pattern = r'\b([A-Z]{2,5})\b'
        matches = []
        for match in re.finditer(pattern, response):
            symbol = match.group(1)
            start_pos = match.start()
            end_pos = match.end()
            
            # Check if it's a stock symbol
            if symbol in ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'AMD', 'META', 'SPY', 'QQQ', 'JPM', 'BAC', 'JNJ', 'PFE', 'XOM', 'CVX', 'PLTR', 'SNOW']:
                matches.append({
                    'symbol': symbol,
                    'position': start_pos,
                    'context': response[max(0, start_pos-20):min(len(response), end_pos+20)]
                })
        
        print(f"\nSYMBOL EXTRACTION ANALYSIS:")
        print("-" * 50)
        
        for i, match in enumerate(matches, 1):
            print(f"{i}. {match['symbol']} at position {match['position']}")
            print(f"   Context: ...{match['context']}...")
            print()
        
        # Count duplicates
        from collections import Counter
        symbol_counts = Counter([m['symbol'] for m in matches])
        
        print("SYMBOL FREQUENCY:")
        print("-" * 30)
        for symbol, count in symbol_counts.most_common():
            print(f"  {symbol}: {count} times")
            if count > 3:
                print(f"    ⚠️  Excessive repetition detected!")
        
        # Check for patterns in the response that might cause duplication
        print(f"\nRESPONSE ANALYSIS:")
        print("-" * 30)
        
        # Check for repeated phrases
        words = response.split()
        word_counts = Counter(words)
        repeated_words = {word: count for word, count in word_counts.items() if count > 3 and len(word) > 2}
        
        if repeated_words:
            print("Repeated words/phrases:")
            for word, count in repeated_words.items():
                print(f"  '{word}': {count} times")
        
        # Check response length
        print(f"\nResponse length: {len(response)} characters")
        print(f"Word count: {len(words)}")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    asyncio.run(debug_duplicate_symbols())
