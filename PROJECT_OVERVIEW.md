# TradingView Automation & Market Analysis Platform

## Project Overview

Systematic market data analysis platform that processes TradingView webhooks and provides data-driven trading insights through automated analysis.

## Docker Configuration

The project now includes a reorganized Docker configuration system with improved structure and maintainability:

### New Docker Directory Structure
```
docker/
├── compose/                 # Docker Compose files
│   ├── production.yml       # Full production deployment
│   ├── development.yml      # Basic development setup
│   ├── development.optimized.yml  # Enhanced development setup
│   └── services/            # Service-specific configurations
│       ├── tradingview-ingest.yml  # Webhook processing system
│       └── pipeline.yml     # AI pipeline system
├── dockerfiles/             # Docker build files
│   ├── app.Dockerfile       # Main application
│   ├── bot.Dockerfile       # Discord bot service
│   ├── app.optimized.Dockerfile  # Optimized application build
│   ├── test.Dockerfile      # Testing environment
│   └── services/            # Service Dockerfiles
│       ├── webhook.Dockerfile     # Webhook receiver
│       ├── webhook.secure.Dockerfile  # Secure webhook receiver
│       ├── processor.Dockerfile   # Data processor
│       ├── monitor.Dockerfile     # Monitoring service
│       └── pipeline.Dockerfile    # Pipeline system
└── config/                  # Docker configuration files
    ├── env.template         # Template for environment variables
    ├── env.development      # Development environment variables
    └── env.production       # Production environment variables
```

### Usage

#### Production
```bash
# From the project root directory
docker-compose -f docker/compose/production.yml up --build
```

#### Development
```bash
# Basic development setup
docker-compose -f docker/compose/development.yml up --build

# Enhanced development setup
docker-compose -f docker/compose/development.optimized.yml up --build
```

#### Services
```bash
# Webhook processing system
docker-compose -f docker/compose/services/tradingview-ingest.yml up --build

# AI pipeline system
docker-compose -f docker/compose/services/pipeline.yml up --build
```

### Documentation
For detailed information about the Docker configuration:
- [Docker Audit Report](DOCKER_AUDIT_REPORT.md) - Complete analysis of the previous Docker configuration issues
- [Docker Consolidation Plan](DOCKER_CONSOLIDATION_PLAN.md) - Detailed implementation plan for the reorganization
- [Docker Summary](DOCKER_SUMMARY.md) - Overview of the improvements and benefits

## Current Implementation Status

**This system is in early development with core infrastructure in place.**

### What Works
- Real-time webhook processing from TradingView
- Basic market data analysis (5-minute intervals)
- PostgreSQL data storage and Redis queuing
- Discord bot integration for notifications
- Prometheus monitoring and metrics
- Webhook signature validation and security
- Automated analysis scheduler
- Basic data parsing and storage

### What's Planned
- Advanced AI analysis, specialized trader archetypes, sophisticated signal generation
- Enhanced AI trading analyzer with trader archetypes
- Specialized trader analyzers (Day Trader, Swing Trader, etc.)
- QQQ/SPY 0DTE specialist implementation
- Advanced signal generation algorithms
- Sequential analysis stages (Grand Check → Deep Dive → Discord)
- Machine learning integration and pattern recognition

## Completed Enhancements

### Core Bot Functionality
- All high-priority tasks from the Discord bot audit have been completed
- Enhanced `/ask` command with batch queries, voice input parsing, and multi-language detection
- Improved `/analyze` command with async parallel stages, user-specific historical reports, and automated scans
- Upgraded `/watchlist` with real-time updates, export/import functionality, and AI-driven symbol suggestions
- Expanded `/zones` with multi-timeframe analysis and probability scoring
- Implemented `/alerts`, `/portfolio`, and `/batch_analyze` commands
- Enhanced `/recommendations` with integration into analysis outputs

### AI and Analysis Capabilities
- Enhanced AI context understanding with user tracking, conversation history, and market awareness
- Advanced query classification with domain-specific detection and complexity scoring
- Multi-timeframe analysis capabilities for all technical analysis commands
- Probability scoring for support and resistance levels
- Integration of zones data into recommendations output

### Infrastructure and Security
- Fixed all pipeline import errors
- Added comprehensive error fallbacks
- Implemented financial advice disclaimers
- Integrated watchlist alerts with scheduler
- Added circuit breakers for external APIs

## Automation Architecture

### Completed Components

#### Watchlist Management System
- User watchlist creation and management
- Priority-based analysis scheduling (HIGH: 15min, MEDIUM: 1hr, LOW: 4hr)
- Symbol tracking with notes, tags, and analysis history
- Automatic next analysis calculation

#### Analysis Job Scheduler
- Priority-based job queuing (CRITICAL, HIGH, MEDIUM, LOW)
- Async job execution with concurrent job limits
- Job status tracking and management
- Graceful shutdown handling

#### Multi-Timeframe Analysis Engine
- Support for multiple timeframes (1m, 5m, 15m, 30m, 1h, 4h, 1d, 1w, 1mo)
- Parallel processing of different timeframes
- Consistent technical indicator calculations across timeframes

## Discord Bot Command Pipelines

### Overview
This section outlines the potential processing pipelines for each Discord bot command, highlighting their flexibility, complexity, and potential variations.

### 1. `/analyze [symbol]` Command
#### Purpose
Provide comprehensive stock analysis for a given stock symbol.

#### Pipeline Variations
##### Level 1: Basic Price Lookup
```
Input Validation -> 
Data Retrieval (Current Price) -> 
Simple Response Generation
```
- Minimal processing
- Quick response
- Low computational overhead

##### Level 3: Intermediate Analysis
```
Input Validation -> 
Multi-Source Data Collection ->
Data Validation and Cleaning ->
Technical Analysis Calculations ->
Signal Generation ->
Response Formatting
```
- Moderate processing
- Detailed analysis
- Multi-source data integration

##### Level 5: Advanced AI Analysis
```
Input Validation -> 
Multi-Source Data Collection ->
Data Validation and Cleaning ->
Multi-Timeframe Technical Analysis ->
AI Pattern Recognition ->
Trader Archetype Analysis ->
Custom Strategy Integration ->
Risk Assessment ->
Signal Generation ->
Response Formatting with Visualizations
```
- Complex processing
- AI-driven insights
- Personalized recommendations
- Rich visualizations

## System Architecture

### Core Components

#### 1. Webhook Ingestion System
- Real-time webhook processing from TradingView
- Signature validation and security checks
- Data parsing and normalization
- Queue management with Redis

#### 2. Analysis Engine
- Multi-timeframe technical analysis
- Custom indicator calculations
- Pattern recognition algorithms
- Signal generation logic

#### 3. Discord Bot Interface
- Command processing and routing
- User interaction management
- Notification system
- Report generation

#### 4. Data Storage
- PostgreSQL for persistent data storage
- Redis for caching and queuing
- TimescaleDB for time-series data (future)

#### 5. AI Enhancement Layer
- Context-aware AI assistants
- Trader archetype modeling
- Natural language processing
- Adaptive learning capabilities

### Data Flow

```
TradingView Webhook -> 
Webhook Ingestion -> 
Redis Queue -> 
Analysis Engine -> 
Signal Generation -> 
Discord Notification
```

### Security Architecture

#### Authentication
- Discord OAuth2 integration
- Role-based access control
- API key management
- Webhook signature validation

#### Data Protection
- Encrypted data transmission
- Secure credential storage
- Input validation and sanitization
- Rate limiting and abuse prevention

#### Infrastructure Security
- Containerized deployment
- Network isolation
- Regular security audits
- Automated vulnerability scanning

## Development Roadmap

### Phase 1: Foundation (Completed)
- Basic webhook processing
- Core analysis engine
- Discord bot integration
- Data storage implementation

### Phase 2: Enhancement (In Progress)
- Advanced technical analysis
- AI integration
- Multi-timeframe processing
- Enhanced user interface

### Phase 3: Optimization (Planned)
- Performance improvements
- Scalability enhancements
- Machine learning integration
- Advanced trading strategies

## Contributing

### Development Setup
1. Clone the repository
2. Set up environment variables
3. Configure Docker containers
4. Run tests to verify setup

### Coding Standards
- Follow PEP 8 for Python code
- Use type hints for all functions
- Write comprehensive unit tests
- Document all public APIs

### Testing
- Unit tests for all core functionality
- Integration tests for system components
- End-to-end tests for user workflows
- Performance benchmarks for critical paths

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Real-Time Data Pipeline

### Overview
The real-time data pipeline extends the batch-oriented market data fetching in the analysis engine to support live streaming via WebSockets. This enables immediate alerts, dynamic analysis, and reduced latency for trading decisions. It integrates seamlessly with the existing `PipelineContext` and `market_data` structure, using Polygon.io as the primary provider for US stock trades/quotes.

Key benefits:
- Live price updates merged into batch data without disrupting historical analysis.
- Event-driven processing with buffering for reliability.
- Configurable alerts for market events (e.g., price surges >5%, high volatility).
- End-to-end latency <500ms per update, verified via tests.

### Components
1. **WebSocket Handler (`src/shared/data_pipeline/websocket_handler.py`)**:
   - Async client for Polygon.io (`wss://socket.polygon.io/stocks`).
   - Handles authentication, subscription/unsubscription to tickers (e.g., trades via `T.{symbol}`).
   - Reconnection with exponential backoff (max 5 retries).
   - Callback-based updates: `on_update(data)` parses JSON to normalized dicts (`price`, `timestamp`, etc.).

2. **Data Buffer & Validation (`src/shared/data_pipeline/buffer_manager.py`)**:
   - Fixed-size deque (default 100 updates/symbol) for recent history.
   - Pydantic validation (`LiveDataUpdate` model) for schema/timestamp/price checks.
   - Methods: `add_update()`, `get_latest()`, `compute_indicators()` (SMA, volatility, change %).
   - Outlier rejection and cache for efficient indicator recompute.

3. **Alert Manager (`src/bot/alerts/real_time_alerts.py`)**:
   - Monitors buffer via polling (10s intervals) or callbacks.
   - Triggers on thresholds: price change >5%, volatility >2%, etc. (configurable in `config.yaml`).
   - Async Discord notifications via bot client.
   - Customizable: `on_alert_trigger` for pipeline integration.

### Integration
- **In Fetch Data Stage (`src/bot/pipeline/commands/analyze/stages/fetch_data.py`)**:
  - After batch fetch (REST via MarketDataService/Aggregator), if `real_time.enabled=true` in config:
    - Initialize `PolygonWebSocketClient` with API key.
    - Set `on_update` callback to merge `live_price`/`live_timestamp` into `market_data`.
    - Background task: Connect, subscribe to ticker, run listener.
  - Stores task in `context.processing_results['live_task']` for cleanup.

- **Configuration (`config.yaml`)**:
  ```
  real_time:
    enabled: true
    provider: polygon
    api_key: "your_polygon_key"
    buffer_size: 100
    reconnect_delay: 5
  alerts:
    price_change_pct: 5.0
    volatility_high: 2.0
    channel_id: "discord_channel_id"
  ```

- **Data Flow**:
  1. Batch init: REST fetch → `market_data` (price, historical, indicators).
  2. Stream start: WebSocket connect/subscribe → Updates to buffer → Validate/merge.
  3. Monitoring: Buffer checks → Alert if threshold hit → Discord notify.
  4. Analysis: Live indicators feed into AI stages for real-time insights.

### Setup & Dependencies
- **New Packages** (add to `requirements.txt`): `websockets`, `polygon-api-client`, `pydantic`.
- **Docker**: No new services; runs in existing app/bot containers. Update `docker-compose.yml` if dedicated stream needed.
- **API Key**: Obtain free Polygon.io key (supports 5/min WebSocket messages); fallback to polling if quota exceeded.
- **Testing**: Run `pytest tests/test_real_time_pipeline.py` – simulates updates, verifies buffering/alerts/latency.

### Limitations & Future Work
- Current: US stocks only; polling fallback not implemented.
- Planned: Redis pub/sub for multi-consumer (dashboard), advanced indicators (RSI via TA-Lib), broker integration for auto-trades.

For detailed architecture, see [real_time_pipeline_design.md](docs/real_time_pipeline_design.md).